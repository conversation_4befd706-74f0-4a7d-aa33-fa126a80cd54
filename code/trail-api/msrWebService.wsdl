<wsdl:definitions name="mediaStayRequests" targetNamespace="http://www.approuter.com/connectors/wsdl" xmlns="" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://www.approuter.com/connectors/wsdl" xmlns:tns1="http://www.approuter.com/connectors/request/4934/" xmlns:tns2="http://www.approuter.com/connectors/response/4934/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <wsdl:types>
    <xsd:schema attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://www.approuter.com/connectors/request/4934/" xmlns:tns="http://www.approuter.com/connectors/request/4934/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <xsd:element name="mediaStayRequests" type="tns:mediaStayRequest"/>
    <xsd:complexType name="mediaStayRequest">
        <xsd:sequence>
            <xsd:element maxOccurs="unbounded" minOccurs="0" name="entries" type="tns:entry"/>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="entry">
        <xsd:sequence>
            <xsd:element name="sourceId" nillable="false" type="xsd:decimal"/>
            <xsd:element name="DatePosted" nillable="false" type="xsd:dateTime"/>
            <xsd:element name="email" nillable="false" type="xsd:string"/>
            <xsd:element name="AudienceProfile" type="xsd:string"/>
            <xsd:element name="CityAndCountry" type="xsd:string"/>
            <xsd:element name="Position" type="xsd:string"/>
            <xsd:element name="CoverageDate" type="xsd:date"/>
            <xsd:element name="Title" type="xsd:string"/>
            <xsd:element name="ArrivalDate" nillable="false" type="xsd:date"/>
            <xsd:element name="FirstName" type="xsd:string"/>
            <xsd:element name="Circulation" type="xsd:string"/>
            <xsd:element name="LastName" type="xsd:string"/>
            <xsd:element name="DepartureDate" nillable="false" type="xsd:date"/>
            <xsd:element name="Medium" type="xsd:string"/>
            <xsd:element name="PublicationFrequency" type="xsd:string"/>
            <xsd:element name="PublicationName" type="xsd:string"/>
            <xsd:element name="RequestedHotel" type="xsd:string"/>
            <xsd:element name="ResponseNeededBy" type="xsd:date"/>
            <xsd:element name="Other" type="xsd:string"/>
            <xsd:element name="StayBasis" type="xsd:string"/>
            <xsd:element name="Coverage" type="xsd:string"/>
            <xsd:element name="StoryAngle" type="xsd:string"/>
            <xsd:element name="ArrivalTime" type="xsd:dateTime"/>
            <xsd:element name="ExpectedBoardBasis" type="xsd:string"/>
            <xsd:element name="Facebook" type="xsd:string"/>
            <xsd:element name="Instagram" type="xsd:string"/>
            <xsd:element name="NumberOfTravellers" nillable="false" type="xsd:integer"/>
            <xsd:element name="PhoneNumber" type="xsd:string"/>
            <xsd:element name="sfContactPhone" type="xsd:string"/>
            <xsd:element name="DescriptionOfPublication" type="xsd:string"/>
            <xsd:element name="PublicationURL" type="xsd:string"/>
            <xsd:element name="Twitter" type="xsd:string"/>
            <xsd:element name="SpecialRequests" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
    <xsd:schema attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://www.approuter.com/connectors/response/4934/" xmlns:tns="http://www.approuter.com/connectors/response/4934/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <xsd:element name="wsOutputs" type="tns:wsOutput"/>
    <xsd:complexType name="wsOutput">
        <xsd:sequence>
            <xsd:element maxOccurs="unbounded" minOccurs="0" name="entries" type="tns:entry"/>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="entry">
        <xsd:sequence>
            <xsd:element name="requestId" type="xsd:string"/>
            <xsd:element name="responseCode" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
  </wsdl:types>
  <wsdl:message name="mediaStayRequestsResponse">
    <wsdl:part element="tns2:wsOutputs" name="response">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="mediaStayRequestsRequest">
    <wsdl:part element="tns1:mediaStayRequests" name="request">
    </wsdl:part>
  </wsdl:message>
  <wsdl:portType name="mediaStayRequestsPort">
    <wsdl:operation name="mediaStayRequests">
      <wsdl:input message="tns:mediaStayRequestsRequest">
    </wsdl:input>
      <wsdl:output message="tns:mediaStayRequestsResponse">
    </wsdl:output>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="mediaStayRequestsBinding" type="tns:mediaStayRequestsPort">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="mediaStayRequests">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="mediaStayRequestsService">
    <wsdl:port binding="tns:mediaStayRequestsBinding" name="mediaStayRequestsPort">
      <soap:address location="https://{property:/WSDL_SOAP_Address_msrWebService_mediaStayRequests_4934}:/{property:/httpTail_msrWebService}"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>