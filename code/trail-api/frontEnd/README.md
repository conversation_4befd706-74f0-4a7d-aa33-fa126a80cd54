# SLH 前端项目

项目使用 vite搭建，使用 vue3 + pinia + element-plus 构建。

## 项目结构

前端项目主要在`frontEnd`目录下， 打包后的资源在`build`文件夹下，导出的文件`manifest.json`为项目打包后的资源对照表，在`.jsp`文件中使用后端提供的方法`${f:mix('src/css/global.scss')}`这样引入打包后的资源


```

├── public
├── src
│   ├── assets
│   ├── components
│   ├── config
│   ├── css
│   ├── enums
│   ├── icons
│   ├── pages
│   ├── stores
│   ├── utils
│   ├── vuews
│   ├── global.js
│   ├── hotel-detail.js
├── postcss.config.js
├── README.md
├── tailwind.config.js
├── vite.config.js

```

前端相关其他目录说明
页面模板目录`code/trail-api/WEB-INF/view`, 前端相关配置`code/trail-api/WEB-INF/classes/config.properties`, 其中设置`manifest.env=dev`为开发环境，引入开发时的资源，改为`manifest.env=prod`则引入打包后`build`中的的资源

## 项目启动

```bash
# 后端启动
brew services start tomcat@8

# 后端停止
brew services stop tomcat@8

# 后端重启
brew services restart tomcat@8
```

```bash
# 启动项目 code/trail-api/frontEnd
yarn dev

# 打包项目
yarn build
```

`注意`修改完配置文件`config.properties`后，需要重启后端服务。

## 项目中使用的技术栈

- [vite](https://vitejs.dev)
- [vue3](https://cn.vuejs.org)
- [vue use](https://vueuse.org/)
- [vue-i18n](https://vue-i18n.intlify.dev/)
- [pinia](https://pinia.vuejs.org/zh/)
- [element-plus](https://element-plus.gitee.io/)
- [tailwindcss](https://tailwindcss.com/)
- [dayjs](https://day.js.org/)
- [jquery](https://jquery.cuishifeng.cn/)
- [swiper](https://swiperjs.com/)
- [photoswipe](https://photoswipe.com/)
