import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import i18n from '@/utils/i18n'
import pinia from '@/stores/index'
import App from './App.vue'

const app = createApp(App)
app.use(i18n)
app.use(pinia)
app.use(ElementPlus)
// 全局注册组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.mount('#app')