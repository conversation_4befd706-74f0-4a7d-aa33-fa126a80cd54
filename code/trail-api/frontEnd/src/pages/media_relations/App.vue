<template>
  <el-config-provider :locale="eleLocale">
    <el-form
      ref="formRef"
      :model="formData"
      label-position="top"
      :rules="formRules"
      :size="elementPlusSize"
      status-icon
    >
      <el-form-item :label="$t('media_relations.hotel_name')" prop="hotelName">
        <el-input
          v-model="formData.hotelName"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.title')" prop="title">
        <el-select
          class="w-full"
          v-model="formData.title"
          :placeholder="$t('media_relations.please_select')">
          <el-option
            v-for="item in titleOption"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <div class="grid grid-cols-2 gap-3 md:gap-4" >
        <el-form-item :label="$t('media_relations.first_name')" prop="first_name">
          <el-input
            v-model="formData.first_name"
            :placeholder="$t('media_relations.please_enter')"
          />
        </el-form-item>
        <el-form-item :label="$t('media_relations.last_name')" prop="last_name">
          <el-input
            v-model="formData.last_name"
            :placeholder="$t('media_relations.please_enter')"
          />
        </el-form-item>
      </div>
      <el-form-item :label="$t('media_relations.country')" prop="cityAndCountry">
        <el-input
          v-model="formData.cityAndCountry"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.email')" prop="email">
        <el-input
          v-model="formData.email"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.address')" prop="address">
        <el-input
          v-model="formData.address"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.phone_number')" prop="phoneNumber">
        <el-input
          v-model="formData.phoneNumber"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.position')" prop="position">
        <el-select
          class="w-full"
          v-model="formData.position"
          :placeholder="$t('media_relations.please_select')">
          <el-option
            v-for="item in positionOption"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="formData.position === positionOption[positionOption.length - 1].value"
        :label="$t('media_relations.other_position')" prop="otherPosition"
      >
        <el-input
          v-model="formData.otherPosition"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.publication_name')" prop="publicationName">
        <el-input
          v-model="formData.publicationName"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.publication_url')" prop="publicationURL">
        <el-input
          v-model="formData.publicationURL"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.publication_description')" prop="publicationDescription">
        <el-input
          type="textarea"
          :rows="3"
          v-model="formData.publicationDescription"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.media')" prop="media">
        <el-checkbox-group v-model="formData.media">
          <el-checkbox
            v-for="item in mediaOption"
            :key="item.value"
            :label="item.value"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <div v-if="formData.media.includes(mediaOption[6].value)">
        <h3 class="text-primary text-base md-2 md:mb-4">{{ $t('media_relations.social_media_titile') }}</h3>
        <el-form-item :label="$t('media_relations.instagram')" prop="instagram">
          <el-input
            v-model="formData.instagram"
            :placeholder="$t('media_relations.please_enter')"
          />
        </el-form-item>
        <el-form-item :label="$t('media_relations.twitter')" prop="twitter">
          <el-input
            v-model="formData.twitter"
            :placeholder="$t('media_relations.please_enter')"
          />
        </el-form-item>
        <el-form-item :label="$t('media_relations.facebook')" prop="facebook">
          <el-input
            v-model="formData.facebook"
            :placeholder="$t('media_relations.please_enter')"
          />
        </el-form-item>
      </div>
      <el-form-item :label="$t('media_relations.frequency')" prop="frequency">
        <el-radio-group v-model="formData.frequency">
          <el-radio
            v-for="item in frequencyOption"
            :key="item.value"
            :label="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t('media_relations.circulation')" prop="circulation">
        <el-input
          v-model="formData.circulation"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.audience_profile')" prop="audienceProfile">
        <el-input
          type="textarea"
          :rows="3"
          v-model="formData.audienceProfile"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.coverage')" prop="coverage">
        <el-input
          v-model="formData.coverage"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.reporting_date')" prop="reportingDate">
        <el-date-picker
          v-model="formData.reportingDate"
          type="date"
          :placeholder="$t('media_relations.please_select')"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.story_angle')" prop="storyAngle">
        <el-input
          type="textarea"
          :rows="3"
          v-model="formData.storyAngle"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.arrival_date')"  prop="arrivalDate">
        <el-date-picker
          v-model="formData.arrivalDate"
          type="date"
          :placeholder="$t('media_relations.please_select')"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.arrival_time')" prop="arrivalTime">
        <el-input
          type="input"
          v-model="formData.arrivalTime"
          :placeholder="$t('media_relations.arrival_time_tip')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.check_out_date')" prop="checkOutDate">
        <el-date-picker
          v-model="formData.checkOutDate"
          type="date"
          :placeholder="$t('media_relations.please_select')"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.numberof_travellers')" prop="numberofTravellers">
        <el-select
          class="w-full"
          v-model="formData.numberofTravellers"
          :placeholder="$t('media_relations.please_select')">
          <el-option
            v-for="item in numberOption"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('media_relations.expected_board_basis')" prop="numberofTravellers">
        <el-select
          class="w-full"
          v-model="formData.expectedBoardBasis"
          :placeholder="$t('media_relations.please_select')">
          <el-option
            v-for="item in expectedOption"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('media_relations.special_requests')" prop="specialRequests">
        <el-input
          type="textarea"
          :rows="3"
          v-model="formData.specialRequests"
          :placeholder="$t('media_relations.please_enter')"
        />
      </el-form-item>
      <el-form-item :label="$t('media_relations.stay_basis')" prop="stayBasis">
        <el-select
          class="w-full"
          v-model="formData.stayBasis"
          :placeholder="$t('media_relations.please_select')">
          <el-option
            v-for="item in stayOption"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('media_relations.reply_date')" prop="replyDate">
        <el-date-picker
          v-model="formData.replyDate"
          type="date"
          :placeholder="$t('media_relations.please_select')"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item>
        <div class="w-full flex items-center justify-center">
          <button class="slh-btn-brand w-1/3" type="button" @click="onSubmit(formRef)">
            {{ $t('media_relations.submit') }}
          </button>
        </div>
      </el-form-item>
    </el-form>
  </el-config-provider>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import {ref, watch, reactive } from 'vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import zhTw from 'element-plus/dist/locale/zh-tw.mjs'
import { useWindowSize, useThrottleFn } from '@vueuse/core'
import { ScreenEnum } from '@/enums/appEnums'
import useAppStore from '@/stores/app'
import { useRequest } from '@/utils/request'
import { ElMessage } from 'element-plus'

const { locale, t } = useI18n()
const eleLocale = ref(locale.value === 'zh-tw' ? zhTw : zhCn)
const { setMobile } = useAppStore()
const { width } = useWindowSize()
watch(
  width,
  useThrottleFn((value) => {
    setMobile(value <= ScreenEnum.MD)
  }),
  {
    immediate: true
  }
)

const { elementPlusSize } = useAppStore()
const formRef = ref()

const titleOption = ['Dr', 'Miss', 'Mr', 'Mrs', 'Ms', 'Prof'].map(item => ({
  value: item,
  label: t(`media_relations.title_option.${item}`)
}))

const positionOption = ['Editor', 'Managing Director', 'News Editor', 'Business Editor', 'Travel Editor', 'Travel Writer', 'Food Writer', 'Spa Writer', 'Freelancer', 'Blogger', 'Photographer', 'Producer', 'Other'].map(item => ({
  value: item,
  label: t(`media_relations.position_option.${item}`)
}))

const mediaOption = ["Print - newspaper", "Print - magazine", "Broadcast", "Radio", "Online", "Blog", "Social Media", "Other", ].map(item => ({
  value: item,
  label: t(`media_relations.media_option.${item}`)
}))

const frequencyOption = ["Daily", "Weekly", "Monthly", "Other"].map(item => ({
  value: item,
  label: t(`media_relations.frequency_option.${item}`)
}))

const numberOption = [1,2,3,4].map(item => ({
  value: item,
  label: item,
}))

const expectedOption = ['B & B', 'Half-board', 'All inclusive',].map(item => ({
    value: item,
    label: t(`media_relations.expected_option.${item}`)
  }))

const stayOption = ['Complimentary', 'Press rate',].map(item => ({
  value: item,
  label: t(`media_relations.stay_option.${item}`)
}))

let formData = reactive({
  hotelName: '',
  title: titleOption[0].value,
  first_name: '',
  last_name: '',
  cityAndCountry: '',
  email: '',
  address: '',
  phoneNumber: '',
  position: positionOption[0].value,
  otherPosition: '',
  publicationName: '',
  publicationURL: '',
  publicationDescription: '',
  media: [],
  instagram: '',
  twitter: '',
  facebook: '',
  frequency: frequencyOption[0].value,
  circulation: '',
  audienceProfile: '',
  coverage: '',
  reportingDate: '',
  storyAngle: '',
  arrivalDate: '',
  arrivalTime: '',
  checkOutDate: '',
  numberofTravellers: numberOption[0].value,
  expectedBoardBasis: expectedOption[0].value,
  specialRequests: '',
  stayBasis: stayOption[0].value,
  replyDate: '',
})

// formData = {
//   "hotelName": "测试酒店",
//   "title": "Miss",
//   "first_name": "喻",
//   "last_name": "康",
//   "cityAndCountry": "中国",
//   "email": "<EMAIL>",
//   "address": "测试地址",
//   "phoneNumber": "13222222222",
//   "position": "Other",
//   "otherPosition": "其他职位",
//   "publicationName": "世界周刊",
//   "publicationURL": "baidu.com",
//   "publicationDescription": "出版物介绍",
//   "media": [
//       "Print - newspaper",
//       "Online",
//       "Social Media"
//   ],
//   "instagram": "Instagram账号-test",
//   "twitter": "Twitter账号-test",
//   "facebook": "Facebook账号-test",
//   "frequency": "Weekly",
//   "circulation": "123",
//   "audienceProfile": "年轻化",
//   "coverage": "123",
//   "reportingDate": "2023-11-22T16:00:00.000Z",
//   "storyAngle": "水电费是的",
//   "arrivalDate": "2023-11-22T16:00:00.000Z",
//   "arrivalTime": "2023-11-29T16:00:00.000Z",
//   "checkOutDate": "2023-11-21T16:00:00.000Z",
//   "numberofTravellers": 3,
//   "expectedBoardBasis": "Half-board",
//   "specialRequests": "无其他特殊要求",
//   "stayBasis": "Press rate",
//   "replyDate": "2023-11-29T16:00:00.000Z"
// }

const formRules = reactive({
  hotelName: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.hotel_name')}), trigger: 'blur' },
  ],
  title: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.title')}), trigger: 'blur' },
  ],
  first_name: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.first_name')}), trigger: 'blur' },
  ],
  last_name: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.last_name')}), trigger: 'blur' },
  ],
  cityAndCountry: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.country')}), trigger: 'blur' },
  ],
  email: [
    {required: true, message: t('common.email.required'), trigger: ['blur', 'change'],},
    {pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: t('common.email.error'), trigger: ['blur', 'change']},
  ],
  phoneNumber: [
    {required: true, message: t('common.phone.required'), trigger: ['blur', 'change'], },
    {pattern: /^(?:(?:\+|00)86)?1\d{10}$/, message: t('common.phone.error'), trigger: ['blur', 'change']},
  ],
  position: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.position')}), trigger: 'blur' },
  ],
  otherPosition: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.other_position')}), trigger: 'blur' },
  ],
  publicationName: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.publication_name')}), trigger: 'blur' },
  ],
  media: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.media')}), trigger: 'blur' },
  ],
  instagram: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.instagram')}), trigger: 'blur' },
  ],
  twitter: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.twitter')}), trigger: 'blur' },
  ],
  facebook: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.facebook')}), trigger: 'blur' },
  ],
  frequency: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.frequency')}), trigger: 'blur' },
  ],
  circulation: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.circulation')}), trigger: 'blur' },
  ],
  audienceProfile: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.audience_profile')}), trigger: 'blur' },
  ],
  coverage: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.coverage')}), trigger: 'blur' },
  ],
  reportingDate: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.reporting_date')}), trigger: 'change' },
  ],
  storyAngle: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.story_angle')}), trigger: 'blur' },
  ],
  arrivalDate: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.arrival_date')}), trigger: 'change' },
  ],
  arrivalTime: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.arrival_time')}), trigger: 'change' },
  ],
  checkOutDate: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.check_out_date')}), trigger: 'change' },
  ],
  stayBasis: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.stay_basis')}), trigger: 'blur' },
  ],
  replyDate: [
    { required: true,  message: t('media_relations.required', {name: t('media_relations.reply_date')}), trigger: 'change' },
  ],
})

// 提交订单
const { execute: mediastayExecute } = useRequest({
  url: '/trail-api/v1/mediastay/apply.do',
  method: "POST",
})

const submitFrom  = async () => {
  ElMessage.warning('正在开发中，敬请期待...')
  return false
  window.formData = formData
  const { data } = await mediastayExecute({
    params: {...formData}
  })
  conosole.log('data', data)
  if (data.code === 200) {
    ElMessage.success(t('media_relations.submit_success'))
  } else {
    ElMessage.error(t('media_relations.submit_error'))
  }
}

const onSubmit = async (formEl) => { 
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      submitFrom()
    }
  })
}
</script>
<style scoped>
</style>