<template>
  <el-config-provider :locale="eleLocale">
    <div class="w-full h-40 bg-black">
    </div>
    <div class="container mx-auto p-y-4 mt-8">
      <h1 class="py-4 text-black text-xl text-center">页面列表</h1>
      <ul class="grid grid-cols-4 gap-4 mb-4">
        <li
          v-for="(page, page_index) in pages"
          :key="page_index"
        >
          <a class="flex px-4 py-3 items-center bg-gray-200 rounded" :href="page.url">
            {{ page_index + 1}}
            {{ page.name }}
          </a>
        </li>
      </ul>
      <div>
        <el-date-picker
          v-model="dateValue"
          type="date"
          placeholder="选择一天"
          :size="size"
        />
      </div>
    </div>
  </el-config-provider>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { ref, watch, computed } from 'vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import zhTw from 'element-plus/dist/locale/zh-tw.mjs'
import { useWindowSize, useThrottleFn } from '@vueuse/core'
import { ScreenEnum } from '@/enums/appEnums'
import useAppStore from '@/stores/app'

const { locale, t } = useI18n()
const eleLocale = ref(locale.value === 'zh-tw' ? zhTw : zhCn)
const { setMobile } = useAppStore()
const { width } = useWindowSize()
watch(
  width,
  useThrottleFn((value) => {
    setMobile(value <= ScreenEnum.MD)
  }),
  {
    immediate: true
  }
)

const pages = [
  {
    name: '首页',
    url: '/index',
  },
  {
    name: '离开本站提示',
    url: '/before_leave',
  },
  {
    name: '登录',
    url: '/login',
  },
  {
    name: '订单填写',
    url: '/order_form',
  },
  {
    name: '订单详情',
    url: '/order_info',
  },
  {
    name: '订单查询',
    url: '/order_search',
  },
  {
    name: '房型列表',
    url: '/rate_list',
  },
  {
    name: '注册',
    url: '/register',
  },
  {
    name: '用户中心',
    url: '/user_center',
  }
]

const dateValue = ref('')

</script>
<style scoped>
</style>