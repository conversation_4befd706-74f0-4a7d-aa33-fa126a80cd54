<template>
  <el-config-provider :locale="eleLocale">
    <Register />
  </el-config-provider>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import {ref, watch } from 'vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import zhTw from 'element-plus/dist/locale/zh-tw.mjs'
import { useWindowSize, useThrottleFn } from '@vueuse/core'
import Register from '@/views/Register.vue'
import { ScreenEnum } from '@/enums/appEnums'
import useAppStore from '@/stores/app'

const { locale, t } = useI18n()
const eleLocale = ref(locale.value === 'zh-tw' ? zhTw : zhCn)
const { setMobile } = useAppStore()
const { width } = useWindowSize()
watch(
  width,
  useThrottleFn((value) => {
    setMobile(value <= ScreenEnum.MD)
  }),
  {
    immediate: true
  }
)
</script>
<style scoped>
</style>