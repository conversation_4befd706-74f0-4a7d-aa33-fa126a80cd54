<template>
  <el-config-provider :locale="eleLocale">
    <div v-loading="isLoading">
      <div class="filter__sction space-y-3 md:space-y-5" v-if="pageType === 'internationals'">
        <div class="filter__box -ml-1.25 md:ml-0">
          <div class="px-1.25 md:p-1.25" v-for="(item, index) in areas" :key="item.id">
            <el-button
              class="md:!px-6"
              type="primary"
              :size="isMobile ? 'small' : 'default'"
              :plain="item.id !== curArea.id"
              @click="handleAreaChange(index)"
            >
              {{ item.name }}
            </el-button>
          </div>
        </div>
        <div
          ref="foldCardRef"
          class="filter__box  divide-x divide-line-light_5 flex-wrap"
          :class="{'fold__card': needFold && !foldOpen}"
        >
          <template v-for="item in curArea?.countries" :key="item.id">
            <span
              class="px-2 md:px-4 my-1.5 md:my-2 line-clamp-none cursor-pointer text-xs md:text-sm"
              :class="[item.countryLink === queryParams.country ? 'text-primary' : 'text-text-light']"
              @click="handleCountryChange(item.countryLink)"
            >
              {{ item.countryName }}
            </span>
          </template>
          <span
            class="open__btn"
            v-if="needFold"
            @click="foldOpen = !foldOpen"
          >
            <i class="iconfont" :class="[`icon-arrow-${!foldOpen ? 'down' : 'up'}`]"></i>
          </span>
        </div>
      </div>
      <div class="filter__sction" v-if="pageType === 'china'">
        <div class="filter__box">
          <div class="p-1.25" v-for="item in cities" :key="item.id">
            <el-button
              class="!px-6"
              type="primary"
              :plain="item.id !== queryParams.city"
              @click="handleCityChange(item.id)"
            >
              {{ item.name }}
            </el-button>
          </div>
        </div>
      </div>
      <div class="flex items-center mb-4 lg:mb-8 xl:mb-8">
        <div class="flex-1">
          <el-radio-group v-model="showType">
            <el-radio-button v-for="item in showTypes" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
          </el-radio-group>
        </div>
        <div class="flex space-x-1 md:space-x-3 lg:space-x-4 xl:space-x-6">
          <div class="flex items-center space-x-0 md:space-x-3">
            <span class="hidden md:block">{{ $t('hotel_list.type') }}</span>
            <el-select
              class="sort_select w-25 md:w-44"
              v-model="queryParams.subject"
              :placeholder="$t('rate_list.sort_by.placeholder')"
              @change="handleSubjectChange"
              placement="bottom-end"
            >
              <el-option
                v-for="item in subjects"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
          <div class="flex items-center space-x-0 md:space-x-3">
            <span class="hidden md:block">{{ $t('rate_list.sort_by.label') }}</span>
            <el-select
              class="sort_select w-25 md:w-44"
              v-model="queryParams.orderBy"
              :placeholder="$t('rate_list.sort_by.placeholder')"
              placement="bottom-end"
            >
              <el-option
                v-for="item in sortOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
      </div>
      <div class="mb-7 text-sm "><span class="text-primary">{{ data?.hotelsCount || 0 }}</span> 个酒店</div>
      <HotelList :list="data?.hotels || []" v-if="!isLoading && showType === 2"/>
      <div v-if="showType === 1" class="map__area">
        <div id="gmap" class="w-full h-[200px] md:h-[540px] bg-gray-400"></div>
        <div class="swiper__area" v-if="!isLoading && data?.hotels?.length">
          <div class="swiper__inner">
            <swiper
              :lazy="true"
              :slides-per-view="isMobile ? 1.2 : 1"
              :centered-slides="isMobile"
              :allow-touch-move="!isMobile"
              @swiper="onSwiper"
              @slideChange="onSlideChange"
            >
              <swiper-slide
                v-for="item in data?.hotels || []"
                :key="item.id"
                class="swiper__slide"
              >
                <div class="slide__inner">
                  <div class="hotel__img__box">
                    <div class="hotel__img">
                      <img class="aspect-ele" loading="lazy" :src="item.coverUrl" alt="">
                    </div>
                  </div>
                  <div class="hotel__info">
                    <h3 class="hotel__name slh-font-sunti">{{ item.hotelName }}</h3>
                    <h4 class="hotel__en__name">{{ item.hotelNameEn }}</h4>
                    <p class="hotel__desc">{{ item.brief }}</p>
                    <p class="hotel__address">
                      <i class="iconfont icon-location address__icon"></i>
                      <span class="pl-2">{{ item.countryName }} {{ item.cityName }}</span>
                    </p>
                    <div class="hotel__actions">
                      <a class="slh-btn-brand w-full md:max-w-[153px]" :href="`${rootPath}/booking/rate_list?hotelCode=${item.hotelCode}&${urlSearch}`">{{ $t('hotel_list.view_price') }}</a>
                      <a class="slh-btn-default w-full md:max-w-[153px]" :href="`${rootPath}/hotel/${item.hotelLink}?${urlSearch}`">{{ $t('hotel_list.view_detail') }}</a>
                    </div>
                  </div>
                </div>
              </swiper-slide>
            </swiper>
            <button
              class="swiper-btn-left"
              :class="{'__disabled': hotelSwiper && hotelSwiper.isBeginning}" @click="onSwiperPrev">
              <i class="btn__icon iconfont icon-arrow-left"></i>
            </button>
            <button
              class="swiper-btn-right"
              :class="{'__disabled': hotelSwiper && hotelSwiper.isEnd}" 
              @click="onSwiperNext">
              <i class="btn__icon iconfont icon-arrow-right"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </el-config-provider>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import {ref, watch, reactive, defineProps, onMounted, nextTick } from 'vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import zhTw from 'element-plus/dist/locale/zh-tw.mjs'
import { useWindowSize, useThrottleFn } from '@vueuse/core'
import HotelList from '@/views/HotelList.vue'
import { ScreenEnum } from '@/enums/appEnums'
import useAppStore from '@/stores/app'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { useRequest } from '@/utils/request'
import { MAPBOX_KEY } from '@/config/index'
import {getUrlParam} from 'ppo'
import { getUrlSearch } from '@/utils/util'

// 标记点地图缩放比例
const markZoom = 10
const defalutMarkImg = `${path}/static/assets/imgs/location.png`; //默认地图标记图片地址
const activeMarkImg = `${path}/static/assets/imgs/location_cur.png`; //激活地图标记图片地址

const { locale, t } = useI18n()
const eleLocale = ref(locale.value === 'zh-tw' ? zhTw : zhCn)
const jumping = ref(false) // 页面跳转中
const needFold = ref(false) // 是否需要展开
const foldOpen = ref(true) // 折叠卡是否展开
const foldCardRef = ref(null) // 折叠卡
const pageType = (window?.pageType || '')
const { rootPath, isMobile, setMobile } = useAppStore()
const { width } = useWindowSize()
const urlSearch = ref(getUrlSearch()) // 链接参数

watch(
  width,
  useThrottleFn((value) => {
    setMobile(value <= ScreenEnum.MD)
  }),
  {
    immediate: true
  }
)

let gmap = null
const hotelSwiper = ref(null)
const showTypes = [1,2].map(item => ({
  id: item,
  name: t(`hotel_list.show_types.${item}`)
}))
const showType = ref(showTypes[1].id)

const cities = (window?.cities || []).map(item => ({
  id: item.cityLink,
  name: item.cityName,
  ...item,
}))

const countries = (window?.countries || []).map(item => ({
  id: item.countryLink,
  name: item.countryName,
  ...item,
}))

const areas = window?.areaList || []

// 排序方式选项
const sortOptions = [
  {
    value: 'nameAsc',
    label: t('hotel_list.sort_option.1'),  
  },
  {
    value: 'nameDsc',
    label: t('hotel_list.sort_option.2'), 
  },
]

const subjects = [
  {
    id: 'all',
    name: t('hotel_list.all_hotel_types')
  },
  ...(window?.activities || []).map(item => ({
    id: item.activityLink,
    name: item.title,
  }))
]

const curArea = ref(areas.length > 0 ? areas[0] : '')
const queryParams = reactive({
  activity: window?.activityLink || '',
  country: getUrlParam('country') || (pageType === 'internationals' ? curArea.value.countries[0].countryLink : '') || window.countryLink || '',
  city: getUrlParam('city') || (window.cityLink || ''),
  orderBy: sortOptions[0].value,
  subject: window?.subject || subjects[0].id,
})

// 判断当前城市是否属于当前国家
const isInCountry = (cityInfo) => {
  const curCountry = countries.find(item => item.countryLink === queryParams.country)
  return curCountry && cityInfo?.countryCode && cityInfo?.countryCode === curCountry.countryCode
}

const handleCityChange = (cityId) => {
  queryParams.city = queryParams.city !== cityId  ? cityId : ''
}

const handleCountryChange = (countryLink) => {
  if(queryParams.country !== countryLink){
    queryParams.country = countryLink
  }
}

// 折叠卡初始化
const foldInit = () => {
  const foldCardHeight = foldCardRef.value?.offsetHeight
  // console.log('foldCardHeight===', foldCardHeight)
  if(foldCardHeight > 64){
    needFold.value = true
    foldOpen.value = false
  } else {
    needFold.value = false
  }
}

const handleAreaChange = (areaIndex) => {
  const _curArea  = areas[areaIndex]
  curArea.value = _curArea
  if(_curArea.countries.length > 0){
    queryParams.country = _curArea.countries[0].countryLink
    queryParams.city = ''
    foldOpen.value = true
    nextTick(() => [
      foldInit()
    ])
  }
}

onMounted(()=> {
  foldInit()
})

const getApiUrl = () => {
  const url = `/trail-api/v1/hotels/chainCode/LX/country/${queryParams.country !== '' ? queryParams.country : countries && countries.length > 1 ? '-1' : (countries.length > 0 ? countries[0]?.id : '-1')}/city/${queryParams.city !== '' ? queryParams.city : '-1'}/activity/${queryParams.activity || '-1'}/subject/${queryParams.subject !== 'all' ? queryParams.subject : '-1'}/orderBy/${queryParams.orderBy || '-1'}`
  // console.log('queryParams == ', queryParams)
  // console.log('getApiUrl === url', url)
  return url
}

const { data, isLoading, execute } = useRequest({
  url: getApiUrl(),
  immediate: true,
})

watch(
  queryParams,
  () => {
    if(!jumping.value){
      execute(getApiUrl())
    }
  },
  {
    deep:true,
  }
)

const handleSubjectChange = (subjectId) => {
  if(window?.subject){
    // 主题列表页页面单独处理
    jumping.value = true
    window.location.href = subjectId === 'all' ? `${rootPath}/theme/hotels` : `${rootPath}/hotel-experience/${subjectId}`
  }
}

// 修改地图marker的
const changeMarker = (curItem) => {
  const {longitude, latitude, hotelCode} = curItem
  const $marker = $(`.slh-marker.hotel_${hotelCode}`)
  $marker.siblings('.is-active').find('img').attr('src', defalutMarkImg)
  $marker.addClass('is-active').siblings('.is-active').removeClass('is-active')
  $marker.find('img').attr('src', activeMarkImg)
  console.log('marker', $marker)
  gmap.setZoom(markZoom)
  gmap.setCenter([longitude, latitude])
}

const mapInit = () => {
  mapboxgl.accessToken = MAPBOX_KEY;
  const _locale = locale == 'zh_TW' ? 'zh-Hans' : 'zh-Hans'
  const bounds = data?.value?.hotels?.map(item => (new mapboxgl.LngLat(item.longitude, item.latitude)))
  console.log('bounds', bounds)
  const map = new mapboxgl.Map({
    container: 'gmap',
    style: 'mapbox://styles/mapbox/streets-v12',
    // center: [longitude, latitude],
    bounds: new mapboxgl.LngLatBounds(bounds),
    // zoom: 12,
    language: _locale,
    locale: _locale,
  });
  gmap = map
  map.addControl(new mapboxgl.NavigationControl());
  map.on('load', () => {
    createMarkers()
  })
}

const onSwiper = (swiper) => {
  hotelSwiper.value = swiper
}

const onSwiperPrev = () => {
  if(!hotelSwiper.value.isBeginning){
    hotelSwiper.value.slidePrev()
  }
}

const onSwiperNext = () => {
  if(!hotelSwiper.value.isEnd){
    hotelSwiper.value.slideNext()
  }
}

const onSlideChange = () => {
  if(gmap){
    const realIndex = hotelSwiper.value.realIndex
    const curItem = data.value.hotels[realIndex]
    if(curItem){
      changeMarker(curItem)
    }
  }
};

const createMarkers = () => {
  if(gmap){
    const hotels = data.value.hotels
    if(hotels.length){
      const firstHotel = hotels[0]
      if(firstHotel){
        const {longitude, latitude} = firstHotel
        gmap.setCenter([longitude, latitude])
      }
    }
    hotels.map((item, index) => {
      // 创建标记点
      const {longitude, latitude, hotelCode, hotelName} = item
      const el = document.createElement('span'); //这里可以是任意标签
      const imgEl = document.createElement('img'); //这里可以是任意标签
      el.className = `slh-marker hotel_${hotelCode} ${index === 0 ? 'is-active' : ''}`;
      el.alt = hotelName;
      imgEl.src = index === 0 ? activeMarkImg : defalutMarkImg
      el.append(imgEl)
      // 添加自定义标记点
      new mapboxgl.Marker({
        anchor: 'center',
        element: el,
        // offset: new mapboxgl.Point(-18, -18),
      }).setLngLat([longitude, latitude])
        .addTo(gmap);
      el.addEventListener("click", () => {
        // console.log('click', item)
        changeMarker(item)
        if(hotelSwiper){
          hotelSwiper.value.slideTo(index)
        }
      })
      // gmap.on('zoom', function () {
      //   const currentZoom = gmap.getZoom();
      //   const newSize = 6 * currentZoom;
      //   el.style.width = newSize + 'px';
      // });
    })
  }
}

// 移除所有地图标记
const removeAllMarkers = () => {
  var allMarkers = document.getElementsByClassName('slh-marker');
  while(allMarkers[0]) {
    allMarkers[0].parentNode.removeChild(allMarkers[0]);
  }
}

watch(
  showType,
  (newValue, oldValue) => {
    console.log('newValue', newValue, 'mapLoaded', SlhGlobal.mapLoaded)
    if(newValue === showTypes[0].id){
      Promise.resolve().then(() => {
        mapInit()
      })
    }
  },
)

watch(
  data,
  (newValue, oldValue) => {
    console.log('newValue', newValue, 'oldValue', oldValue, 'gmal', gmap)
    removeAllMarkers()
    createMarkers()
  },
  {
    initial: false,
    deep: true,
  }
)

</script>
<style lang="scss" scoped>
.map__area{
  @apply relative md:pb-35;
}
.filter__sction{
  @apply bg-primary-light_2 pb-3 md:py-3 lg:py-6 xl:py-9 mb-4 md:mb-6 lg:mb-8 xl:mb-8 md:px-2 lg:px-4 xl:px-6;
  .filter__box{
    @apply relative w-full overflow-x-scroll md:overflow-auto flex md:flex-wrap items-start justify-start md:justify-center;
    .open__btn{
      line-height:1;
      @apply absolute right-0 bottom-1.5 z-10 border-none md:hidden;
      .iconfont{
        line-height:1;
        font-size:18px;
      }
    }
    &.fold__card{
      @apply h-16 md:h-auto overflow-hidden md:overflow-auto;
      .open__btn{
        line-height:1;
        @apply bottom-3;
      }
    }
  }
}
.swiper__area{
  @apply -ml-4 -mr-4 md:ml-0 md:mr-0 md:w-full md:absolute left-0 bottom-0 z-10;
  .swiper__inner{
    @apply relative md:pt-0 px-0 md:px-8 max-w-[1012px] mx-auto;
  }
  .swiper__slide{
    @apply px-1.5 py-2 md:p-4.5 bg-bg_gary-light;
    .slide__inner{
      @apply flex flex-col md:flex-row items-start bg-white p-4 md:p-5 xl:p-7 text-xs md:text-sm text-text-light;
    }
    .hotel__img__box{
      @apply w-[281px] hidden md:block;
    }
    .hotel__img{
      @apply w-full aspect-w-281 aspect-h-187 object-cover;
    }
    .hotel__name{
      @apply text-lg md:text-xl lg:text-2xl text-black;
    }
    .hotel__info{
      @apply w-full md:w-auto flex-1 pt-4 md:pt-0 md:pl-5; 
    }
    .hotel__en__name{
      @apply mt-1 text-xs lg:text-sm;
    }
    .hotel__desc{
      @apply text-sm text-text-light mt-2 line-clamp-1 hidden md:block;
    }
    .hotel__address{
      @apply flex items-center leading-none mt-2 md:mt-3;
      .address__icon{
        @apply text-base lg:text-xl leading-none;
      }
    }
    .hotel__actions{
      @apply flex space-x-2 mt-2 md:mt-3;
    }
  }
  .swiper-btn-left,
  .swiper-btn-right{
    margin-top:-17.5px;
    
    @apply absolute top-1/2 w-[25px] h-[35px] lg:w-[50px] lg:h-[70px] bg-primary text-white z-10 flex items-center justify-center;
    .btn__icon{
      font-size:24px;
      @apply leading-none;
    }
    &.__disabled{
      @apply opacity-70;
    }
    @media (min-width: 768px){
      margin-top:-35px;
      .btn__icon{
        font-size:32px;
      }
    }
  }
  .swiper-btn-left{
    @apply left-0;
  }
  .swiper-btn-right{
    @apply right-0;
  }
}
</style>