import dayjs from 'dayjs'
import { getUrlParam } from 'ppo'

export const IS_PROD = import.meta.env.PROD // 是否是生产环境
export const DEDFAULT_LOCALE = window?.locale && window?.locale == 'zh_TW' ? 'zh-tw' : 'zh-cn' // 默认语言 'zh-cn' | 'zh-tw', 
export const GMAPS_URL = "https://gmaps.dragongap.cn/maps/api/js?key=AIzaSyAvAu1D-6wjlms4giwxxQPW4w2MOrE4wB0"
export const MAPBOX_KEY = "pk.eyJ1IjoiaWlpc2xlZSIsImEiOiJjamxlazhvZmswN2FlM2txaDBtaWd5MGRqIn0.g-vax0O9Zd3TU38Y-l8InA"

export const DATE_FORMAT = 'YYYY-MM-DD' // 日期格式
export const DATE_FORMAT2 = 'MM月DD日 ddd' // 日期格式
export const DATE_FORMAT3 = 'YYYYMMDD' // 日期格式
export const DATE_FORMAT4 = 'MM月DD日' // 日期格式
export const DATE_FORMAT5 = 'YYYYMMDD' // 日期格式
export const MONTH_FORMAT = 'MMYYYY' // 日期格式

export const ROOM_MAX_CONT  = 5 // 最大房间数
export const ADULT_MAX_CONT  = 10 // 最大成人数
export const CHILD_MAX_AGE = 18 // 儿童最大年龄
export const CHILD_MAX_CONT = 6 // 儿童最多人数
export const CHAIN_CODE = 'LX' // 中国代码

// 国家级区号
export const COUNTRIES = [
  {
    name: '中国',
    code: '+86',
    countryCode: 'CN', // China
  },
  {
    name: '美国',
    code: '+1',
    countryCode: 'US', // United States
  },
  {
    name: '加拿大',
    code: '+1',
    countryCode: 'CA', // Canada
  },
  {
    name: '英国',
    code: '+44',
    countryCode: 'GB', // United Kingdom (Great Britain)
  },
  {
    name: '澳大利亚',
    code: '+61',
    countryCode: 'AU', // Australia
  },
  {
    name: '新西兰',
    code: '+64',
    countryCode: 'NZ', // New Zealand
  },
  {
    name: '日本',
    code: '+81',
    countryCode: 'JP', // Japan
  },
  {
    name: '韩国',
    code: '+82',
    countryCode: 'KR', // South Korea
  },
  {
    name: '德国',
    code: '+49',
    countryCode: 'DE', // Germany
  },
  {
    name: '法国',
    code: '+33',
    countryCode: 'FR', // France
  },
  {
    name: '意大利',
    code: '+39',
    countryCode: 'IT', // Italy
  },
  {
    name: '西班牙',
    code: '+34',
    countryCode: 'ES', // Spain
  },
  {
    name: '荷兰',
    code: '+31',
    countryCode: 'NL', // Netherlands
  },
  {
    name: '瑞士',
    code: '+41',
    countryCode: 'CH', // Switzerland (Confoederatio Helvetica)
  },
  {
    name: '新加坡',
    code: '+65',
    countryCode: 'SG', // Singapore
  },
];

// 发送短信验证码冷却时间
export const COOLING_TIME = 60;
// 跳转倒计时
export const JUMP_COUNTDOWN = 5;

// 网络请求配置
export const REQUEST_CONFIG = {
  terminal: 1, //终端
  title: '后台管理系统', //网站默认标题
  version: '1.6.0', //版本号
  baseUrl: window?.API_BASE_URL || 'https://slh.agent.dragontrail.cn', //请求测试接口域名
  // baseUrl: `https://pos.slhhotels.cn`, //请求生产接口域名
  urlPrefix: '', //请求默认前缀
  timeout: 20 * 1000, //请求超时时长,
  imgHost: "https://www.slhhotels.cn/slhImg/", // 图片资源地址
}

// 默认酒店房型查询参数
const indate = getUrlParam('indate')
const outdate = getUrlParam('outdate')
const nights = getUrlParam('nights')
const rooms = getUrlParam('rooms')
const adultsPerRoom = getUrlParam('adultsPerRoom')
const childrenPerRoom = getUrlParam('childrenPerRoom')
const childrenAges = getUrlParam('childrenAges')
const keyword = getUrlParam('keyword')
const defalutNights = 1; // 默认晚数
const defalutIndate = (window?.checkinDate ? dayjs(window?.checkinDate) : dayjs()).format(DATE_FORMAT); // 默认入住时间
const defalutOutdate = (window?.checkoutDate ? dayjs(window?.checkoutDate) :  dayjs(`${defalutIndate} 00:00`).add(defalutNights, 'day')).format(DATE_FORMAT); // 默认离店住时间
export const DEFAULT_QUERY_PARAMS = {
  indate: indate ? dayjs(indate).format(DATE_FORMAT) : defalutIndate,
  outdate: outdate ? dayjs(outdate).format(DATE_FORMAT) : defalutOutdate,
  nights: nights ? Number(nights) : defalutNights, // 默认晚数
  rooms: rooms ? Number(rooms) : 1, // 默认房间数
  adultsPerRoom: adultsPerRoom ? Number(adultsPerRoom) : 1, // 默认成人数
  childrenPerRoom: childrenPerRoom ? Number(childrenPerRoom) : 0, // 默认儿童数
  childrenAges: childrenAges ? childrenAges : '', // 默认儿童年龄
  // keyword: keyword ? decodeURIComponent(keyword) : '',
  keyword: '',
}

// 目的地数据
export const DESTINATION_LIST = [
  {
    title: '热门',
    key: "hot",
  },
  {
    title: '中华',
    key: "China",
  },
  {
    title: '亚洲',
    key: "Asia",
  },
  {
    title: '美洲',
    key: "Americas",
  },
  {
    title: '欧洲',
    key: "Europe",
  },
  {
    title: '大洋洲',
    key: "Australasia",
  },
  {
    title: '非洲',
    key: "Africa",
  },
  {
    title: '其他',
    key: "other",
  }
]