export const emailRegExp = /^[^\s]+@[^\s]+\.[^\s]+$/
export const phoneRegExp = /^(?:(?:\+|00)86)?1\d{10}$/
export const loosePhoneRegExp = /^[0-9]+$/
export const urlRegExp = /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/|www\.)(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~\/])+$/
export const passwordRegExp = /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/
export const userEnNameRegExp = /(^[a-zA-Z][a-zA-Z\s]{0,50}[a-zA-Z]$)/
export const cardCVVRegExp = /^[0-9]{3,4}$/