import { deleteUrlParam } from 'ppo'


/**
 * @description 添加单位
 * @param {String | Number} value 值 100
 * @param {String} unit 单位 px em rem
 */
export const addUnit = (value, unit = 'px') => {
  return !Object.is(Number(value), NaN) ? `${value}${unit}` : value
}

/**
 * @description 是否为空
 * @param {unknown} value
 * @return {Boolean}
 */
export const isEmpty = (value) => {
  return value == null && typeof value == 'undefined'
}

/**
 * @description 搜索目的地
 * @param {unknown} value
 * @return {Boolean}
 */
export const searchDestination = (keyword) => {
	let result = {};
  const _queryCache = JSON.parse(JSON.stringify(queryCache))
  keyword = keyword.toLowerCase()
	if(_queryCache.countries && _queryCache.countries.length){
    result.countries = _queryCache.countries.filter(item => {
      if(
        (item.countryName && item.countryName.toLowerCase().includes(keyword)) ||
        (item.countryNameTra && item.countryNameTra.toLowerCase().includes(keyword)) ||
        (item.countryNameEn && item.countryNameEn.toLowerCase().includes(keyword)) ||
        (item.countryNamePinyin && item.countryNamePinyin.toLowerCase().includes(keyword))
      ){
        item.title = locale == "zh_TW" ? item.countryNameTra : item.countryName
        return item
      } else {
        return false
      }
    })
	}
  if(_queryCache.cities && _queryCache.cities.length){
    result.cities = _queryCache.cities.filter(item => {
      if(
        (item.countryName && item.countryName.toLowerCase().includes(keyword)) ||
        (item.countryNameTra && item.countryNameTra.toLowerCase().includes(keyword)) ||
        (item.countryNameEn && item.countryNameEn.toLowerCase().includes(keyword)) ||
        (item.countryNamePinyin && item.countryNamePinyin.toLowerCase().includes(keyword)) ||
        (item.cityName && item.cityName.toLowerCase().includes(keyword)) ||
        (item.cityNameTra && item.cityNameTra.toLowerCase().includes(keyword)) ||
        (item.cityNameEn && item.cityNameEn.toLowerCase().includes(keyword)) ||
        (item.cityNamePinyin && item.cityNamePinyin.toLowerCase().includes(keyword))
      ){
        item.title = locale == "zh_TW" ? item.cityNameTra : item.cityName
        return item
      } else {
        return false
      }
    })
	}
  if(_queryCache.hotels && _queryCache.hotels.length){
    result.hotels = _queryCache.hotels.filter(item => {
      if(
        (item.countryName && item.countryName.toLowerCase().includes(keyword)) ||
        (item.countryNameTra && item.countryNameTra.toLowerCase().includes(keyword)) ||
        (item.countryNameEn && item.countryNameEn.toLowerCase().includes(keyword)) ||
        (item.countryNamePinyin && item.countryNamePinyin.toLowerCase().includes(keyword)) ||
        (item.cityName && item.cityName.toLowerCase().includes(keyword)) ||
        (item.cityNameTra && item.cityNameTra.toLowerCase().includes(keyword)) ||
        (item.cityNameEn && item.cityNameEn.toLowerCase().includes(keyword)) ||
        (item.cityNamePinyin && item.cityNamePinyin.toLowerCase().includes(keyword)) ||
        (item.hotelName && item.hotelName.toLowerCase().includes(keyword)) ||
        (item.hotelNameEn && item.hotelNameEn.toLowerCase().includes(keyword)) ||
        (item.hotelNameTra && item.hotelNameTra.toLowerCase().includes(keyword)) ||
        (item.hotelAliasEn && item.hotelAliasEn.toLowerCase().includes(keyword)) ||
        (item.hotelNamePinyin && item.hotelNamePinyin.toLowerCase().includes(keyword))
      ){
        item.title = locale == "zh_TW" ? item.cityNameTra : item.cityName
        item.hotelName = locale == "zh_TW" ? item.hotelNameTra : item.hotelName
        item.countryName = locale == "zh_TW" ? item.countryNameTra : item.countryName
        item.cityName = locale == "zh_TW" ? item.cityNameTra : item.cityName
        return item
      } else {
        return false
      }
    })
	}
	return result;
}

/**
 * @description 是否是会员价
 * @param {unknown} string
 * @return {Boolean}
 */
export const isSLHClubCodes = (rateCode) => {
  const clubCodes = [
    'SLHINV',
    'SLHIN7',
    'SLHIN8',
    'SLHIN9',
    'SLMEXS',
    'SLMEX2',
    'SLMEX3',
  ]
  return rateCode && clubCodes.includes(rateCode);
}

// 获取url参数字符
export const getUrlSearch = () => {
  const url = deleteUrlParam('keyword')
  const urlObj = new URL(url)
  const search = urlObj.search
  const searchStr = search ? search.slice(1) : ''
  return searchStr
}