import { createI18n } from 'vue-i18n'
import zh_cn from '@/locales/zh_cn.json?v2'
import zh_tw from '@/locales/zh_tw.json'
import {DEDFAULT_LOCALE} from '@/config/index'

console.log('DEDFAULT_LOCALE', DEDFAULT_LOCALE)
// console.log('zh_tw', zh_tw)

// 字符Unicode
function toUnicodeEscape(str) {
  return str.replace(/[\s\S]/g, function(char) {
      var code = char.charCodeAt(0).toString(16).toUpperCase();
      while (code.length < 4) {
          code = '0' + code;
      }
      return '\\u' + code;
  });
}

// 获取对象字符转并Unicode
const getObjString = (obj, fkey) => {
  let objectString = ''
  Object.keys(obj).map(key => {
    const item = obj[key]
    const itemIsString = typeof item === 'string'
    const itemKey = `${fkey ? `${fkey}.` : ''}${key}`
if(!/\s/.test(itemKey)){
objectString = `${objectString}
${!itemIsString ? getObjString(item, itemKey) : `${itemKey}=${toUnicodeEscape(item)}`}`
}

  })
  return objectString
}

// const zhObjStr = getObjString(zh_cn)
// console.log('zhObjStr', zhObjStr)
// const zhTwObjStr = getObjString(zh_tw)
// console.log('zhTwObjStr', zhTwObjStr)

const i18n = createI18n({
  legacy: false, // 如果要支持compositionAPI，此项必须设置为false;
  locale: DEDFAULT_LOCALE,
  messages: {
    'zh-cn': zh_cn,
    'zh-tw': zh_tw,
  }
})

export default i18n