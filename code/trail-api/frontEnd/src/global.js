import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import i18n from '@/utils/i18n'
import pinia from '@/stores/index'
import SearchbarApp from '@/apps/SearchbarApp.vue'
// import { Swiper } from 'swiper'
class slhGlobal {
  constructor() {
    this.mapLoaded = false
  }
  // 导航初始化
  navInit() {
    const $slhLayout = $('#slh_layout')
    $('#slh_menu_open_btn').click(function (e) { 
      e.stopPropagation();
      $slhLayout.addClass('aside_open')
    });
    $('#slh_menu_close_btn').click(function (e) {
      $slhLayout.removeClass('aside_open')
    });
    $slhLayout.on('click', '#layout_main', function () {
      if($slhLayout.hasClass('aside_open')){
        $slhLayout.removeClass('aside_open')
      }
    })
    const $slhHdSearchbar = $('#slh_hd_searchbar')
    $('#slh_hd_searchbtn').click(function (e) {
      const layoutRole = $slhLayout.data('role')
      if(layoutRole === 'hotelDetail'){
        // 酒店详情页面的搜索做单独的处理
        e.preventDefault();
        const homeLink = $('.slh_header .hd__logo').href
        window.location.href = `${homeLink || window.location.origin}?#searchbarMarker`
      } else {
        $slhHdSearchbar.addClass('__show')
      }
    });
    const handleClick = (e) => {
      const slhHdSearchbar = document.getElementById('slh_hd_searchbar')
      const slhHdSearchBtn = document.getElementById('slh_hd_searchbtn')
      const dropdownNum = $(e.target).parents('.dropdown__box').length
      if(slhHdSearchbar && !slhHdSearchbar.contains(e.target) && !slhHdSearchBtn.contains(e.target) && dropdownNum <= 0 && $slhHdSearchbar.hasClass('__show')){
        $slhHdSearchbar.removeClass('__show')
      }
    }
    document.addEventListener("click", handleClick);
  }
  // 订单查询
  searchOrderInit() {
    const $orderSearch = $('.order_search')
    const $openBtn = $orderSearch.find('.open__btn')
    const $closeBtn = $orderSearch.find('.close__btn')
    const $orderDropdown = $orderSearch.find('.order__dropdown')
    const $submitBtn = $orderSearch.find('.submit__btn')
    const open = () => {
      $orderDropdown.removeClass('hidden')
    }
    const close = () => {
      $orderDropdown.addClass('hidden')
    }
    $openBtn.click(function (e) {
      e.stopPropagation();
      open()
    });
    $closeBtn.click(function (e) { 
      e.stopPropagation();
      close()
    });
    const handleClick = (e) => {
      const hidden = $orderDropdown.hasClass('hidden')
      if($orderSearch[0] && !$orderSearch[0].contains(e.target) && !hidden){
        close()
      }
    }
    document.addEventListener("click", handleClick);
    $submitBtn.click(function(){
      const lastName = $orderSearch.find('input[name="lastName"]').val()
      const reservationId = $orderSearch.find('input[name="reservationId"]').val()
      if(!lastName){
        alert('姓不能为空')
        return
      }
      if(!reservationId){
        alert('行程编号/确认号不能为空')
        return
      }
      close()
    })
  }
  // 组件初始化
  componentsInit() {
    // 幻灯片
    const $comBannerSwiper = $('.com-banner-swiper')
    if($comBannerSwiper.length){
      $comBannerSwiper.each((index, item) => {
        const slideLength = $(item).find('.swiper-slide').length
        const loop = $(item).data('loop')
        let swiperConfig = {}
        if(slideLength > 1){
          swiperConfig = {
            ...swiperConfig,
            loop: loop,
            autoplay: {delay: 5000},
            pagination: {
              el: $(item).find('.swiper-pagination')[0],
              clickable :true,
            },
            navigation: {
              nextEl: $(item).find('.swiper-button-next')[0],
              prevEl: $(item).find('.swiper-button-prev')[0],
            },
          }
        } else {
          $(item).find('.swiper-button-next, .swiper-button-prev').remove()
          swiperConfig = {
            ...swiperConfig,
            allowTouchMove: false,
          }
        }
        const comBannerSwiper = new Swiper (item, swiperConfig)
      })
    }
    // 搜索框
    const $slhSearchbar = $('#slhSearchbar')
    if($slhSearchbar.length){
      const role = $slhSearchbar.attr('role')
      const hotelCode = $slhSearchbar.attr('hotelCode')
      const hotelName = $slhSearchbar.attr('hotelName')
      console.log('slhSearchbar===role', role)
      console.log('slhSearchbar===hotelCode', hotelCode)
      console.log('slhSearchbar===hotelName', hotelName)
      const searchbarApp = createApp(SearchbarApp, {
        role: role,
        hotelCode: hotelCode || '',
        hotelName: hotelName || '',
      })
      searchbarApp.use(i18n)
      searchbarApp.use(pinia)
      searchbarApp.use(ElementPlus)
      // 全局注册组件
      for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        searchbarApp.component(key, component)
      }
      searchbarApp.mount('#slhSearchbar')
      this.searchbarApp = searchbarApp
    }
    // tab切换
    const $comTabs = $('.com-tabs')
    if($comTabs.length){
      const $navItem = $comTabs.find('.tabs__nav .nav__item')
      $navItem.click(function(){
        const tabIndex = $(this).index()
        console.log('tabIndex', tabIndex)
        $(this).addClass('__cur')
          .siblings('.nav__item').removeClass('__cur')
        $comTabs.find('.tabs__content .content__item')
          .eq(tabIndex).addClass('__show')
          .siblings('.content__item').removeClass('__show')
      })
    }
    // 多列幻灯片
    const $comColSwiper = $('.com-col-swiper')
    if($comColSwiper.length){
      $comColSwiper.each((index, item) => {
        const slideLength = $(item).find('.swiper-slide').length
        const perView = $(item).data('perview')
        const center = $(item).data('center')
        const loop = $(item).data('loop')
        const screenWidth = $(window).width()
        console.log('screenWidth', screenWidth)
        const swiperOption = screenWidth <= 768 ? {
          loop: loop && slideLength > 1,
          autoplay: slideLength > 1 ? {delay: 5000} : false,
          slidesPerView: 1.2,
        } : {
          slidesPerView: perView,
          centeredSlides: center,
          loop: loop && slideLength > perView,
          autoplay: slideLength > perView ? {delay: 5000} : false,
        }
        // console.log('loop', loop)
        // console.log('center', center)
        // console.log('perView', perView)
        const swiperConfig = {
          initialSlide: slideLength <= perView ? 1 : 0,
          autoplay: {delay: 5000},
          ...swiperOption,
          pagination: {
            el: $(item).find('.swiper-pagination')[0],
            clickable :true,
          },
          navigation: {
            nextEl: $(item).parent('.com-col-swiper-box').find('.com-col-swiper-btns .swiper__btn__next')[0],
            prevEl: $(item).parent('.com-col-swiper-box').find('.com-col-swiper-btns .swiper__btn__prev')[0],
          },
          scrollbar: {
            el: $(item).find('.swiper-scrollbar')[0],
            draggable: true,
          },
        }
        const comColSwiper = new Swiper (item, swiperConfig)
      })
    }
    // 展开收起
    const $ellipsis = $('.com-ellipsis')
    if($ellipsis.length){
      $ellipsis.map((index) => {
        const $item = $ellipsis.eq(index)
        const openText = $item.attr('open-text') || '展开';
        const closeText = $item.attr('close-text') || '收起';
        const $ellipsisContent = $item.find('.ellipsis__content')
        const contentClassNames = $ellipsisContent.attr('class').split(' ')
        const ellipsisClassName = contentClassNames.find(item => item.includes('line-clamp-'))
        if(ellipsisClassName){
          const contentHeight = $ellipsisContent.outerHeight()
          $ellipsisContent.removeClass(ellipsisClassName)
          let innerHeight = 0
          $ellipsisContent.children().map((index, item) => {
            innerHeight+= $(item).outerHeight(true)
          })
          if(contentHeight < innerHeight){
            let open = false
            $ellipsisContent.addClass(ellipsisClassName)
            $ellipsisContent.prepend(`<span class="ellipsis__btn">${openText}</span>`)
            const $ellipsisBtn = $item.find('.ellipsis__btn')
            $ellipsisBtn.click(function(){
              $ellipsisContent.toggleClass(ellipsisClassName)
              $(this).text(open ? openText : closeText)
              open = !open
            })
          }
        }
      })
    }
  }
  initMap() {
    const $gmap = $('#gmap')
    if($gmap.length){
      this.mapLoaded = true
      console.log('initMap ===')
    }
  }
  gotopInit() {
    const $gotopBtn = $('#gotopBtn')
    if($gotopBtn.length){
      $(window).scroll(function(){
        if($(window).scrollTop() > window.screen.height/2){
          $gotopBtn.removeClass('_hidden')
        }else{
          $gotopBtn.addClass('_hidden')
        }
      })
      $gotopBtn.click(function(){
        $('html,body').animate({scrollTop:0},500)
      })
    }
  }
  // 谷歌事件统计
  gtagEvent(event, params) {
    if(gtag){
      console.log('gtagEvent', event, params)
      gtag('event', event || 'default', params || {})
    }
  }
  init() {
    this.searchOrderInit()
    console.log('searchOrderInit === end')
    this.navInit()
    this.componentsInit()
    this.gotopInit()
    console.log('SlhGlobal -> init')
  }
}

let SlhGlobal = new slhGlobal()
window.SlhGlobal = SlhGlobal
SlhGlobal.init()