import PhotoSwipeLightbox from 'photoswipe/lightbox';
import 'photoswipe/style.css';
import '@/assets/libs/jquery.nav.js';
import { MAPBOX_KEY } from '@/config/index'

const lightboxLaodingTpl = '<div id="lightboxLoading" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;display: flex; justify-content: center; align-items: center;color: white; font-size:12px;">图片加载中<div>'

const lightboxAfterInit = () => {
  $('.pswp .pswp__scroll-wrap').prepend(lightboxLaodingTpl);
  setTimeout(() => {
    $("#lightboxLoading").remove()
    $('.pswp .pswp__item').css({
      opacity: 1
    })
  }, 1000)
}

class slhHotelDetail {
  constructor() {
    this.mapLoaded = false
    this.timer = null
  }
  // 可持续发展多列幻灯片
  activelySwiperInit() {
    const $comColSwiper = $('.actively-col-swiper')
    if($comColSwiper.length){
      const slideLength = $comColSwiper.find('.swiper-slide').length
      const perView = $comColSwiper.attr('perView')
      const center = $comColSwiper.attr('center')
      const comColSwiper = new Swiper ('.actively-col-swiper', {
        autoplay: {delay: 5000},
        slidesPerView: 1.2,
        spaceBetween: 16,
        breakpoints: {
          768: {  //当屏幕宽度大于等于768 
            loop: slideLength > 1,
            autoplay: slideLength > 1 ? {delay: 5000} : false,
            slidesPerView: perView - 0.7,
          },
          1024: {  //当屏幕宽度大于等于1280
            slidesPerView: perView,
            spaceBetween: 89,
            centeredSlides: center === 'true',
            loop: slideLength > perView,
            autoplay: slideLength > perView ? {delay: 5000} : false,
          },
        },
        scrollbar: {
          el: $comColSwiper.find('.swiper-scrollbar')[0],
          draggable: true,
        },
      })
    }
  }
  // 酒店画廊
  galleryInit(){
    const $hotelGallery = $('.hotel__gallery')
    if($hotelGallery.length){
      const thumbSwiper = new Swiper(".thumbSwiper", {
        // loop: true,
        // centeredSlides: true,
        lazy: true,
        spaceBetween:4,
        slidesPerView: 'auto',
        freeMode: true,
        watchSlidesProgress: true,
        breakpoints: {
          1024: {
            spaceBetween:8,
          },
        }
      });
      const mainSwiper = new Swiper(".mainSwiper", {
        navigation: {
          nextEl: ".mainSwiper .swiper-button-next",
          prevEl: ".mainSwiper .swiper-button-prev",
        },
        pagination: {
          el: ".mainSwiper .swiper-pagination",
          type: "fraction",
        },
        thumbs: {
          swiper: thumbSwiper,
        },
      })
    }
    const lightbox = new PhotoSwipeLightbox({
      gallery: '#hotel-gallery',
      children: 'a',
      pswpModule: () => import('photoswipe'),
      showHideAnimationType: 'none',
      closeOnVerticalDrag: false,
      errorMsg: '图片无法加载',
    });
    lightbox.on('afterInit', () => {
      lightboxAfterInit()
    });
    lightbox.init();
  }
  // 房型多列幻灯片
  roomSwiperInit() {
    const $comColSwiper = $('.hotel-col-swiper')
    if($comColSwiper.length){
      const slideLength = $comColSwiper.find('.swiper-slide').length
      const perView = $comColSwiper.attr('perView')
      const center = $comColSwiper.attr('center')
      const comColSwiper = new Swiper ('.hotel-col-swiper', {
        // loop: true,
        lazy: true,
        autoplay: {delay: 5000},
        slidesPerView: 1.2,
        spaceBetween: 20,
        breakpoints: {
          768: {  //当屏幕宽度大于等于768 
            // loop: slideLength > 1,
            autoplay: slideLength > 1 ? {delay: 5000} : false,
            slidesPerView: perView - 0.7,
          },
          1024: {  //当屏幕宽度大于等于1280
            slidesPerView: perView,
            spaceBetween: 60,
            centeredSlides: center === 'true',
            // loop: slideLength > perView,
            autoplay: slideLength > perView ? {delay: 5000} : false,
          },
        },
        navigation: {
          nextEl: $comColSwiper.parent('.hotel-col-swiper-box').find('.hotel-col-swiper-btns .swiper__btn__next')[0],
          prevEl: $comColSwiper.parent('.hotel-col-swiper-box').find('.hotel-col-swiper-btns .swiper__btn__prev')[0],
        },
        scrollbar: {
          el: $comColSwiper.find('.swiper-scrollbar')[0],
          draggable: true,
        },
        on: {
          init: (swiper) => {
            const $comBannerSwiper = $('.room-swiper')
            if($comBannerSwiper.length){
              console.log('$comBannerSwiper', $comBannerSwiper.length)
              $comBannerSwiper.each((index, item) => {
                const slideLength = $(item).find('.swiper-slide').length
                let swiperConfig = {}
                if(slideLength > 1){
                  swiperConfig = {
                    ...swiperConfig,
                    loop: true,
                    pagination: {
                      el: $(item).find('.swiper-pagination')[0],
                      clickable :true,
                    },
                    navigation: {
                      nextEl: $(item).find('.swiper-button-next')[0],
                      prevEl: $(item).find('.swiper-button-prev')[0],
                    },
                  }
                } else {
                  $(item).find('.swiper-button-next, .swiper-button-prev').remove()
                  swiperConfig = {
                    ...swiperConfig,
                    allowTouchMove: false,
                  }
                }
                const comBannerSwiper = new Swiper (item, swiperConfig)
              })
            }
            const $roomGallery = $('.room-gallery')
            if($roomGallery.length){
              $roomGallery.each((index, item) => {
                const lightbox = new PhotoSwipeLightbox({
                  gallery: item,
                  children: 'a',
                  pswpModule: () => import('photoswipe'),
                  showHideAnimationType: 'none',
                  closeOnVerticalDrag: false,
                  errorMsg: '图片无法加载',
                });
                lightbox.on('afterInit', () => {
                  lightboxAfterInit()
                });
                lightbox.init();
              })
            }
          }
        }
      })
    }
  }
  initMap() {
    const $gmap = $('#gmap')
    if($gmap.length){
      const latitude = Number($gmap.data('latitude'))
      const longitude = Number($gmap.data('longitude'))
      const title = $gmap.data('title')
      const countryCode = $gmap.data('countrycode')
      const zipcode = $gmap.data('zipcode')
      const address = $gmap.data('address')
      const addressEn = $gmap.data('addressen')
      const cityName = $gmap.data('cityname')
      const cityNameEn = $gmap.data('citynameen')
      const countryName = $gmap.data('countrymame')
      const countryNameEn = $gmap.data('countrymameen')
      // console.log('latitude', latitude, 'longitude', longitude)
      let addressInfo = `${address || addressEn || ''}`
      if(countryCode!="CN"){
        addressInfo = `${addressInfo}${cityNameEn ? `, ${cityNameEn}` : ''}${zipcode ? `, ${zipcode}` : ''}${countryNameEn ? `, ${countryNameEn}` : ''}`
      }else{
        addressInfo = `${addressInfo}${cityName ? `, ${cityName}` : ''}${zipcode ? `, ${zipcode}` : ''}${countryName ? `, ${countryName}` : ''}`
      }
      // $("#addressInfo").html(addressInfo)
      // console.log('addressInfo', addressInfo)
      mapboxgl.accessToken = MAPBOX_KEY;
      const _locale = locale == 'zh_TW' ? 'zh-Hans' : 'zh-Hans'
      const map = new mapboxgl.Map({
        container: 'gmap',
        style: 'mapbox://styles/mapbox/streets-v12',
        center: [longitude, latitude],
        zoom: 12,
        language: _locale,
        locale: _locale,
      });
      map.addControl(new mapboxgl.NavigationControl());
      map.on('load', () => {
        // 创建标记点
        const el = document.createElement('img'); //这里可以是任意标签
        el.className = 'slh-marker';
        el.alt = title;
        el.src = `${path}/static/assets/imgs/location.png`; //图片路径赋值
        // 创建 Popup 对象
        const infoPopup = new mapboxgl.Popup({
          offset: new mapboxgl.Point(-15, -36),
          closeButton: true,
          closeOnClick: true,
        });
        // 设置弹出窗口的内容
        const popupContent = `
          <div class="hotle_map_popup">
            <h3>${title}</h3><p>${addressInfo}</p>
          </div>
          `;
        infoPopup.setHTML(popupContent);
        // 添加自定义标记点
        new mapboxgl.Marker({
          anchor: 'center',
          element: el,
          offset: new mapboxgl.Point(-15, -18),
        }).setLngLat([longitude, latitude])
          .setPopup(infoPopup)
          .addTo(map);
        // 默认显示 Popup
        const scrollPosition = window.scrollY; // 保存当前滚动位置
        infoPopup.addTo(map);
        window.scrollTo(0, scrollPosition); // 恢复滚动位
      });
    }
    const $locationInfoInner = $('#locationInfoInner')
    if($locationInfoInner){
      const containerHight = $locationInfoInner.height()
      let innerHeight = 0
      $locationInfoInner.children().map((index, item) => {
        innerHeight+= $(item).outerHeight(true)
      })
      if(containerHight < innerHeight){
        $locationInfoInner.addClass('is_scroll')
      }
    }
  }
  onePageNavInit() {
    const $pageNav = $('#pageNav')
    if($pageNav.length){
      $(document).ready(function() {
        $('#pageNav').onePageNav();
      });
    }
  }
  onScroll(){
    const $bookBox = $('#bookBox')
    if($bookBox.length){
      const $openBtn = $('#openBtn')
      const bookBoxTop = $bookBox.offset().top
      $openBtn.click(function(){
        $bookBox.toggleClass('is__open')
      })
      const handleClick = (e) => {
        const isOpen = $bookBox.hasClass('is__open');
        const isRoomInfoConfimBtn = e.target?.id && e.target?.id === 'roomInfoConfimBtn'
        if(isOpen && !$bookBox[0].contains(e.target) && !isRoomInfoConfimBtn){
          $bookBox.removeClass('is__open')
        }
      }
      $(window).on('scroll', () => {
        const scrollTop = $(window).scrollTop()
        // console.log('scroll==scrollTop', scrollTop)
        if(scrollTop >= bookBoxTop){
          $bookBox.addClass('is__sticky')
          const isOpen = $bookBox.hasClass('is__open');
          if(isOpen){
            $bookBox.removeClass('is__open')
          }
        } else {
          $bookBox.removeClass('is__sticky')
        }
      })
      document.addEventListener("click", handleClick);
    }
  }
  init() {
    this.onScroll()
    this.activelySwiperInit()
    // this.galleryInit()
    this.roomSwiperInit()
    this.onePageNavInit()
    this.initMap()
    console.log('initMap === end')
  }
}

let SlhHotelDetail = new slhHotelDetail()
window.SlhHotelDetail = SlhHotelDetail
SlhHotelDetail.init()

$(function() {
  // 获取资源链接
  var buildDataUrl = function () {
    var getFolderName = function(pid, len) {
      var path = String(pid);
      var folderName = path.substring(path.length - len);
      if (path.length >= len) {
        return folderName;
      } else {
        for (var i = 0; i < (len - path.length); i++) {
          folderName = "0" + folderName;
        }
        return folderName;
      }
    }
    var $hotelLeoScript = $('#hotelLeoScript')
    var sourcePath = $hotelLeoScript.attr("data-sourcePath") || ''
    var lang = $hotelLeoScript.attr("data-lang") || ''
    var ids = $hotelLeoScript.attr("data-ids") || ''
    var pid = $hotelLeoScript.attr("data-pid") || ''
    // console.log('sourcePath ===', sourcePath)
    // console.log('lang ===', lang)
    // console.log('ids ===', ids)
    // console.log('pid ===', pid)
    var idsConfigPath = sourcePath.substring(0, sourcePath.indexOf("player")) + "clients/";
    var dataPath = idsConfigPath + ids+ "/data/";
    var detalPid;
    if (pid.length > 20) {
      detalPid = pid;
    } else {
      detalPid = getFolderName(pid, 20);
    }
    var dataUrl = dataPath + getFolderName(pid, 1) + "/" + getFolderName(pid, 3) + "/" + getFolderName(pid, 5) + "/" + detalPid + "/" + lang + "_data.json?v=01.23";
    return dataUrl;
  }
  var getBestSize = function(encodings, availWidth, availHeight, fill){
    var encs = encodings;
    var max = Math.max(availWidth, availHeight);
    var measure = (max !== availWidth) ? "width" : "height";
    var optimal, suboptimal;
    encs.comparator = function (a, b) { return b.width - a.width || b.height - a.height; };
    encs.sort(encs.comparator);
    encs.forEach(function (enc) {
      var w = enc.width;
      var h = enc.height;
      var imgRatio = measure === "width" ? w / h : h / w;
      if (fill === "cover") {
        if (w >= availWidth && h >= availHeight && imgRatio < 1.25) {
          optimal = enc;
        } else {
          if ((w >= availWidth || h >= availHeight) && !optimal) {
            optimal = enc;
          } else {
            if (!suboptimal) {
              suboptimal = enc;
            }
          }
        }
      } else {
        if ((w >= availWidth || h >= availHeight)) {
          optimal = enc;
        } else {
          if (!optimal) {
            optimal = enc;
          }
        }
      }
    });
    return optimal || suboptimal
  }
  function ensureHttps(url) {
    if (url.startsWith('http://')) {
      return url.replace('http://', 'https://');
    }
    return url;
  }

  function proxyImg(url){
    return `${window.IMG_PROXY_URL}?url=${encodeURIComponent(url)}`
  }

  var getJSON = function(jsonData){
    var rcoData = jsonData.rcoData
    var rcos = jsonData.tabegoryData.tabCategories[0].rcos

    var rcosLength = rcos.length
    if(rcosLength){
      var bestGallery = ``
      var smallGallery = ``
      $.each(rcos, function(index, value) {
        var item = rcoData[value]
        if(item.rcoType === 'photo'){
          var title = item.caption
          var encodings = item.encodings
          var bestSize = getBestSize([...encodings], screen.width, screen.height, "cover");
          var smallSize = encodings[0]
          const bestImgUrl = proxyImg(ensureHttps(bestSize.url))
          const smallImgUrl = proxyImg(ensureHttps(smallSize.url))
          // console.log('bestSize', bestSize)
          // console.log('smallSize', smallSize)
          bestGallery += '<a href="'+bestImgUrl+'" data-pswp-width="'+bestSize.width+'" data-pswp-height="'+bestSize.height+'"><img src="'+smallImgUrl+'" alt="'+title+'" /></a>'
          // console.log('item', index, item)
          bestGallery += `<a class="swiper-slide" href="${bestImgUrl}" data-pswp-width="${bestSize.width}"
            data-pswp-height="${bestSize.height}" target="_blank">
            <div class="gallery__main__img">
              <img class="aspect-ele" src="${bestImgUrl}" loading="lazy" alt="${title}"/>
            </div>
            <i class="iconfont icon-search1"></i>
          </a>`;
          smallGallery += `<div class="swiper-slide">
            <div class="gallery__thumb__img">
              <img class="aspect-ele" src="${smallImgUrl}" loading="lazy" alt="${title}"/>
            </div>
          </div>`
        } else {
          // console.log('item 不是图片类型', item)
        }
      });

      $("#bestGalleryWrapper").append(bestGallery)
      $("#smallGalleryWrapper").append(smallGallery)
      $("#section3").show()
      SlhHotelDetail.galleryInit()
    }
  }
  window.getJSON = getJSON
  var dataUrl = buildDataUrl()
  console.log('dataUrl===', dataUrl)
  $.ajax({
    url: dataUrl,  // JSONP 请求的 URL
    dataType: 'jsonp',  // 指定数据类型为 JSONP
    callback: "getJSON",
  });
})