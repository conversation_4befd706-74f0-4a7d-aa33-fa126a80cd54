import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ScreenEnum } from '@/enums/appEnums'

const useAppStore = defineStore('app', () => {
  const rootPath = ref(window.path) // 根路径地址
  const isMobile = ref(document.body.offsetWidth <= ScreenEnum.MD) // 是否是移动端
  const elementPlusSize = ref('default') // element-plus 组件尺寸大小
  const setMobile = (value) => {
    isMobile.value = value
    elementPlusSize.value = value ? 'default' : 'large';
  }
  return { rootPath, isMobile, elementPlusSize, setMobile }
})

export default useAppStore