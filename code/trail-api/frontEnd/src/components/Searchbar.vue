<template>
  <div class="slh_searchbar" :class="[`role__${role}`]">
    <div class="searchbar__card" v-if="role === 'hotel'">
      <!-- 酒店详情页面搜索框 -->
      <h3 class="card__title">{{ $t('searchbar.user_info') }}</h3>
      <div class="card__row cursor-pointer">
        <div class="card__cell">
          <p class="cell__lable">{{ $t('searchbar.checkin') }}</p>
          <p class="cell__value">{{ queryParams.indate }}</p>
        </div>
        <div class="card__cell">
          <p class="cell__lable">{{ $t('searchbar.checkout') }}</p>
          <p class="cell__value">{{ queryParams.outdate }}</p>
        </div>
        <el-date-picker
          v-model="checkDate"
          type="daterange"
          range-separator="To"
          :start-placeholder="$t('searchbar.check_indate')"
          :end-placeholder="$t('searchbar.check_outdate')"
          :format="DATE_FORMAT"
          :value-format="DATE_FORMAT"
          :teleported="true"
          :style="{
            width: '100%',
            height: '100%',
            position: 'absolute',
            left:0,
            bottom:0,
            opacity: 0,
          }"
          @change="checkDateChange"
        />
      </div>
      <div class="card__row">
        <div class="card__cell">
          <p class="cell__lable">{{ $t('searchbar.nights') }}</p>
          <p class="cell__value">{{ nights }}</p>
        </div>
        <div class="card__cell cursor-pointer" @click="roomInfoDropdow=true" ref="roomsRefHotel">
          <p class="cell__lable">{{ $t('searchbar.guest') }}</p>
          <p class="cell__value">
            <!-- <span>{{ queryParams.rooms }}{{ $t('searchbar.rooms') }}</span>, -->
            <!-- <span>{{ guests }}{{ $t('searchbar.guest') }}</span> -->
            <span>{{ queryParams.adultsPerRoom }}{{ $t('common.aldult') }}</span>
            <span v-if="queryParams.childrenPerRoom">{{ queryParams.childrenPerRoom }}{{ $t('common.child') }}</span>
          </p>
          <div
            class="absolute top-16.5 right-0 w-80 lg:w-96 bg-white shadow-lg z-10"
            v-if="roomInfoDropdow"
          >
            <RoomInfo
              :params="searchParams"
              @handleConfim="handleRoomInfoConfim"
            />
          </div>
        </div>
      </div>
      <div class="w-full pt-3">
        <button class="w-full slh-btn-brand" @click="handleSearchBtnClick">
          {{ $t('searchbar.booking_hotel') }}
        </button>
      </div>
    </div>
    <div v-else class="w-full flex items-center h-15 lg:h-16.5 xl:h-17.5 bg-white/95">
      <div
        class="w-[27%] lg:w-[24%] slh-searchbar-item px-3 lg:px-5 xl:px-7"
        ref="sreachRef"
        :class="{'cursor-not-allowed': disabledKeyword}"
      >
        <p class="slh-searchbar-lable">{{ $t('searchbar.destination.label') }}</p>
        <input
          v-model="keyword"
          class="slh-searchbar-input pl-0"
          :class="{'cursor-not-allowed': disabledKeyword}"
          type="text"
          :disabled="disabledKeyword"
          :placeholder="$t('searchbar.destination.placeholder')"
          @focus="keywordFocus = true"
        >
        <!-- 推荐目的地 -->
        <div
          class="dropdown__box absolute top-15 lg:top-16.5 xl:top-17.5 left-0 w-130 bg-white border-t border-line shadow-lg z-10"
          v-if="keywordFocus"
        >
          <div class="w-full search-result h-[300px] overflow-y-scroll"  v-if="keyword">
            <div class="w-full" v-if="searchResult && (searchResult.cities.length || searchResult.countries.length || searchResult.hotels.length)">
              <div class="result_setion"
                v-for="(item, key) in searchResult"
                :key="key"
              >
                <template v-if="item.length">
                  <h3 class="result__title">{{$t(`searchbar.${key}`)}}</h3>
                  <div
                    class="result__item"
                    v-for="(cell, cell_index) in item"
                    :key="cell_index"
                    @click="handleSearchClick(key, key === 'hotels' ? cell.hotelName : cell.title, cell)"
                  >
                    <div v-if="key === 'hotels'">
                      <p class="item__title">{{ cell.hotelName }}</p>
                      <p class="item__title">{{ cell.hotelAliasEn || cell.hotelNameEn }} </p>
                      <p class="item__info">{{ cell.countryName }} {{ cell.cityName }}</p>
                    </div>
                    <template  v-else>
                      <span class="item__title">{{ cell.title }}</span>
                      <span class="item__num">{{$t('searchbar.hotels_num', {num: cell.count})}}</span>
                    </template>
                  </div>
                </template>
              </div>
            </div>
            <div v-else>
              <el-empty :image-size="100" />
            </div>
          </div>
          <el-tabs v-else v-model="activeTab">
            <el-tab-pane
              v-for="(item , index) in destination"
              :key="index"
              :label="item.label"
              :name="item.key"
            >
              <div class="h-[300px] overflow-y-scroll">
                <div class="px-7 py-5 space-y-5">
                  <template v-if="item.key === 'other'">
                    <div
                      v-for="(continent, continent_index) in item.continents"
                      :index="continent_index"
                      class="space-y-4"
                    >
                      <h2 class="text-sm text-primary pb-2 border-b border-line">{{continent.title}}</h2>
                      <div v-if="continent.cities && continent.cities.length">
                        <h3 class="text-sm text-primary mb-2">{{$t('searchbar.hot_city')}}</h3>
                        <div class="flex flex-wrap -mx-2">
                          <span
                            class="block text-text-light text-sm cursor-pointer px-2 py-1 hover:text-primary"
                            v-for="(city , city_index) in continent.cities"
                            :key="city_index"
                            @click="handleSearchClick('cities', city.cityName, city)"
                          >
                            {{city.cityName}}
                          </span>
                        </div>
                      </div>
                      <div v-if="continent.countries && continent.countries.length">
                        <h3 class="text-sm text-primary mb-2">{{$t('searchbar.countries')}}</h3>
                        <div class="flex flex-wrap -ml-2 -mr-2">
                          <span
                            class="block text-text-light text-sm cursor-pointer px-2 py-1 hover:text-primary"
                            v-for="(country , country_index) in continent.countries"
                            :key="country_index"
                            @click="handleSearchClick('countries', country.countryName, country)"
                          >
                            {{country.countryName}}
                          </span>
                        </div>
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <div v-if="item.cities && item.cities.length">
                      <h3 class="text-sm text-primary mb-2">{{$t(`searchbar.${item.key === 'China' ?  'cities' : 'hot_city'}`)}}</h3>
                      <div class="flex flex-wrap -mx-2">
                        <span
                          class="block text-text-light text-sm cursor-pointer px-2 py-1 hover:text-primary"
                          v-for="(city , city_index) in item.cities"
                          :key="city_index"
                          @click="handleSearchClick('cities', item.key === 'hot' ? city.name : city.cityName, city)"
                        >
                          {{item.key === 'hot' ? city.name : city.cityName}}
                        </span>
                      </div>
                    </div>
                    <div v-if="item.countries && item.countries.length">
                      <h3 class="text-sm text-primary mb-2">{{$t(`searchbar.${item.key === 'hot' ? 'hot_country' : 'countries'}`)}}</h3>
                      <div class="flex flex-wrap -mx-2">
                        <span
                          class="block text-text-light text-sm cursor-pointer px-2 py-1 hover:text-primary"
                          v-for="(country , country_index) in item.countries"
                          :key="country_index"
                          @click="handleSearchClick('countries', item.key === 'hot' ? country.name : country.countryName, country)"
                        >
                          {{item.key === 'hot' ? country.name : country.countryName}}
                        </span>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="w-[43%] lg:w-[46%] slh-searchbar-item flex-row cursor-pointer">
        <div class="w-[35%] slh-searchbar-cell">
          <span class="block w-0 h-full border-line border-l absolute t-0 -left-px"></span>
          <p class="slh-searchbar-lable">{{ $t('searchbar.checkin') }}</p>
          <p class="slh-searchbar-value">{{ queryParams.indate }}</p>
        </div>
        <div class="w-[35%] slh-searchbar-cell">
          <span class="block w-0 h-full border-line border-l absolute t-0 -left-px"></span>
          <p class="slh-searchbar-lable">{{ $t('searchbar.checkout') }}</p>
          <p class="slh-searchbar-value">{{ queryParams.outdate }}</p>
        </div>
        <div class="w-[30%] slh-searchbar-cell">
          <span class="block w-0 h-full border-line border-l absolute t-0 -left-px"></span>
          <p class="slh-searchbar-lable">{{ $t('searchbar.nights') }}</p>
          <p class="slh-searchbar-value">{{ nights }}</p>
        </div>
        <el-date-picker
          v-model="checkDate"
          type="daterange"
          range-separator="To"
          :start-placeholder="$t('searchbar.check_indate')"
          :end-placeholder="$t('searchbar.check_outdate')"
          :format="DATE_FORMAT"
          :value-format="DATE_FORMAT"
          :disabled-date="(time) => time.getTime() < Date.now() - 8.64e7"
          :teleported="true"
          :style="{
            width: '100%',
            height: '100%',
            position: 'absolute',
            left:0,
            bottom:0,
            opacity: 0,
          }"
          @change="checkDateChange"
        />
      </div>
      <div class="w-[14%] lg:w-[14%] slh-searchbar-item cursor-pointer" ref="roomsRef">
        <div class="w-full slh-searchbar-cell" @click="roomInfoDropdow=true">
          <i class="block w-0 h-full border-line border-l absolute t-0 -left-px"></i>
          <p class="slh-searchbar-lable">{{ $t('searchbar.guest') }}</p>
          <div class="slh-searchbar-value">
            <!-- <span>{{ queryParams.rooms }}{{ $t('searchbar.rooms') }}</span>, -->
            <!-- <span>{{ guests }}{{ $t('searchbar.guest') }}</span> -->
            <span>{{ queryParams.adultsPerRoom }}{{ $t('common.aldult') }}</span>
            <span v-if="queryParams.childrenPerRoom">{{ queryParams.childrenPerRoom }}{{ $t('common.child') }}</span>
          </div>
        </div>
        <div
          class="dropdown__box absolute top-15 lg:top-16.5 xl:top-17.5 right-0 w-80 lg:w-96 bg-white border-t border-line shadow-lg z-10"
          v-if="roomInfoDropdow"
        >
          <RoomInfo
            :params="searchParams"
            @handleConfim="handleRoomInfoConfim"
          />
        </div>
      </div>
      <div class="w-[16%] lg:w-[16%] h-full border-6 border-white">
        <button class="w-full h-full slh-btn-brand" @click="handleSearchBtnClick">
          <i class="iconfont icon-search text-base lg:text-xl xl:text-2xl"></i>
          <span class="text-sm lg:text-base pl-1 lg:pl-2">{{$t(`searchbar.search_${role === 'booking' ? 'room' : 'hotel'}`)}}</span>
        </button>
      </div>
    </div>
  </div>
</template>
<script setup>
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
import { DATE_FORMAT, CHILD_MAX_AGE } from '@/config/index'
import { ref, computed, reactive, onMounted, onUnmounted, onBeforeUnmount, watch, } from 'vue'
import { watchDebounced } from '@vueuse/core'
import RoomInfo from '@/components/RoomInfo.vue'
import {searchDestination} from '@/utils/util'

const { t } = useI18n()
const props = defineProps({
  queryParams: Object,
  destination: {
    type: Object,
    default: () => [],
  },
  role: {
    type: String,
    default: '',
  },
  disabledKeyword: {
    type: Boolean,
    default: false,
  },
})

const searchParams = reactive({
  rooms: props.queryParams.rooms,
  adultsPerRoom: props.queryParams.adultsPerRoom,
  childrenPerRoom: props.queryParams.childrenPerRoom,
  childrenAges: props.queryParams.childrenAges,
  keyword: props.queryParams.keyword,
})

const childMaxAge = CHILD_MAX_AGE;
const checkDate = ref([dayjs(`${searchParams.indate } 00:00`), dayjs(`${searchParams.outdate} 00:00`)])
const keywordFocus = ref(false)
const roomInfoDropdow = ref(false)
const sreachRef = ref()
const roomsRef = ref()
const roomsRefHotel = ref()
const searchResult = ref(null) // 搜索数据
const keyword = ref(searchParams.keyword || '') // 搜索数据

watch(
  props,
  (value) => {
    if(value?.queryParams){
      if(value.queryParams?.keyword){
        keyword.value = value.queryParams?.keyword
      }
      if(value.queryParams?.childrenAges){
        searchParams.childrenAges = value.queryParams?.childrenAges
      }
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

// 客人数
const guests = computed(() => {
  return props.queryParams.adultsPerRoom + props.queryParams.childrenPerRoom;
});

const nights = computed(() => {
  return props.queryParams.outdate && props.queryParams.indate ? dayjs(props.queryParams.outdate).diff(dayjs(props.queryParams.indate), 'day') : 0
});

const activeTab = ref(props.destination.length ? props.destination[0].key : '')

let ageOption = []
for(var i = 0; i <= childMaxAge; i++){
  ageOption.push({
    value: i,
    label: i === 0 ? t('searchbar.age_down', {age: 1}) : t('searchbar.age', {age: i}),
  })
}

const handleClick = (e) => {
  if(sreachRef.value && !sreachRef.value.contains(e.target) && keywordFocus.value){
    keywordFocus.value = false
    keyword.value = ''
  }
  if(roomsRefHotel.value && !roomsRefHotel.value.contains(e.target) && roomInfoDropdow.value){
    roomInfoDropdow.value = false
  }
  if(roomsRef.value && !roomsRef.value.contains(e.target) && roomInfoDropdow.value){
    roomInfoDropdow.value = false
  }
}

onMounted(() => {
  document.addEventListener("click", handleClick);
})
onUnmounted(() => {
  document.removeEventListener("click", handleClick);
})

const emits = defineEmits(['update:queryParams', 'update:destination', 'search'])

// 所选时间变化
const checkDateChange = (val) => {
  if(val){
    const [indate, outdate] = val
    if(indate && outdate){
      const nights = dayjs(outdate).diff(dayjs(indate), 'day')
      emits('update:queryParams', {
        indate,
        outdate,
        nights
      })
    }
  }
}

const handleRoomInfoConfim = (params) => {
  roomInfoDropdow.value = false
  emits('update:queryParams', {
    ...searchParams,
    ...params,
  })
}

watch(
  keyword,
  (value) => {
    const result = searchDestination(value)
    searchResult.value = result
    searchParams.keyword = value
    // console.log('keyword!', value, 'result', result)
  },
  {
    immediate: true,
  },
)

const handleSearchClick = (type, name, item) => {
  // console.log('type, keyword, item', type, name, item)
  keywordFocus.value = false
  keyword.value = name
  emits('update:destination', {
    type: type,
    data: item,
  })
  emits('update:queryParams', {
    ...searchParams,
    keyword: name,
  })
}

const handleSearchBtnClick = () => {
  emits('search')
  console.log('role', props.role)
  if(props.role !== 'booking'){
    setTimeout(() => {
      keyword.value = ''
    })
  }
}

</script>

<style lang="scss">
.slh_searchbar{
  // background-color:rgba(#fff, 0.85);
  .el-tabs{
    .el-tabs__header{
      padding-left:1.75rem;
      padding-right:1.75rem;
      background-color:var(--slh-bg_gary-light_2);
      margin-bottom:0;
      .el-tabs__nav-wrap{
        &::after{
          display:none;
        }
      }
    }
  }
  .el-collapse{
    @apply border-none mt-3;
    .el-collapse-item__header{
      @apply px-4 flex h-[50px];
      .el-collapse-item__arrow{
        @apply text-base font-bold;
      }
    }
    .el-collapse-item__content{
      @apply pb-2;
    }
    .el-collapse-item{
      +.el-collapse-item{
        @apply mt-3;
      }
    }
  }
  .el-input-number{
    width:94px;
    $btnSize:28px;
    @apply text-black;
    .el-input{
      .el-input__wrapper{
        padding-left:$btnSize;
        padding-right:$btnSize;
        @apply shadow-none;
        &:hover,
        &.is-focus{
          @apply shadow-none;
        }
      }
    }
    .el-input-number__decrease,
    .el-input-number__increase{
      width:$btnSize;
      height:$btnSize;
      @apply border border-line flex items-center justify-center rounded-full shadow-none;
      .el-icon{
        @apply font-bold;
      }
      &:hover{
        ~.el-input{
          .el-input__wrapper{
            @apply shadow-none;
          }
        }
      }
    }
  }
  &.role__hotel{
    .searchbar__card{
      background-color:rgba(#fff, 0.95);
      @apply  p-5 py-5;
    }
    .card__title{
      @apply text-lg text-text mb-1;
    }
    .card__row{
      @apply grid grid-cols-2 gap-5 pt-3 relative;
    }
    .card__cell{
      @apply text-base relative;
    }
    .cell__lable{
      @apply text-text;
    }
    .cell__value{
      @apply py-2  text-text-light border-b border-line;
    }
  }
  // 搜索结果
  .search-result{
    .result_setion{
      .result__title{
        @apply py-3 px-4 bg-primary-light_3 text-base text-primary border-t border-b border-line-light_5;
      }
      .result__item{
        @apply flex px-4 py-3 justify-between text-sm text-text cursor-pointer;
      }
      .item__title{
        @apply flex-1 pr-2;
      }
      .item__info{
        @apply text-xs text-text-light;
      }
      .result__num{

      }
    }
  }
}
</style>