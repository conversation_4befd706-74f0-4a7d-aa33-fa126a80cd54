<template>
  <svg aria-hidden="true" :style="styles">
    <use :xlink:href="symbolId" fill="currentColor" />
  </svg>
</template>

<script setup>
import { addUnit } from '@/utils/util'
import { computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  size: {
    type: [Number, String],
    default: 14
  },
  color: {
    type: String,
    default: 'inherit'
  }
})
const symbolId = computed(() => `#${props.name}`)
const styles = computed(() => {
  return {
    width: addUnit(props.size),
    height: addUnit(props.size),
    color: props.color
  }
})
</script>
