import * as ElementPlusIcons from '@element-plus/icons-vue'
const localIconsName = ['air-con', 'fine-dining', 'free-wifi', 'pet-friendly', 'pool', 'spa']

export const LOCAL_ICON_PREFIX = 'local-icon-'
export const EL_ICON_PREFIX = 'el-icon-'

const elIconsName = []

for (const [, component] of Object.entries(ElementPlusIcons)) {
  elIconsName.push(`${EL_ICON_PREFIX}${component.name}`)
}

export function getElementPlusIconNames() {
  return elIconsName
}
export function getLocalIconNames() {
  return localIconsName.map(name => (`${LOCAL_ICON_PREFIX}${name}`))
}