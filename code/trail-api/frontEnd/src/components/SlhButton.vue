<template>
  <template v-if="href"> 
    <a
      :href="href"
      :target="target"
      :class="[typeClassNames, disabled ? 'pointer-events-none cursor-not-allowed opacity-60' : 'cursor-pointer']"
    >
      <slot></slot>
    </a>
  </template>
  <template v-else>
    <span
      v-loading="loading"
      :class="[typeClassNames, disabled ? 'pointer-events-none cursor-not-allowed opacity-60' : 'cursor-pointer']"
    >
      <slot></slot>
    </span>
  </template>

</template>
<script setup>
import { ref, reactive, watch, computed, } from 'vue'
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  href: {
    type: String,
    default: '',
  },
  target: {
    type: String,
    default: '_self',
  },
  type: {
    type: String,
    default: 'default',
  },
})

const typeClassNames = computed(() => {
  let classNames = 'h-11 lg:h-12.5 px-4 text-sm md:text-base border-none inline-flex items-center justify-center text-black hover:no-underline'
  switch(props.type){
    case 'brand':
      classNames+=' bg-brand hover:bg-brand-dark'
      break;
    default:
      classNames+=' bg-bg_gary-light_3 hover:bg-bg_gary-dark_3'
      break;
  }
  return classNames
})

</script>
