<template>
  <el-steps
    class="slh_register_steps w-full"
    :active="active"
    align-center
  >
    <el-step :title="$t('common.register_steps.1')"/>
    <el-step :title="$t('common.register_steps.2')"/>
    <el-step :title="$t('common.register_steps.3')"/>
  </el-steps>
</template>
<script setup>
const props = defineProps({
  active: {
    type: Number,
    default: 0,
  }
})
</script>

<style lang="scss">
.slh_register_steps{
  .el-step{
    font-size: 14px;
    color: #fff;
    &.is-horizontal{
      .el-step__line{
        height:1px;
        background-color: #f4f4f4;
      }
    }
    .el-step__icon{
      width:24px;
      height:24px;
      font-size: 12px;
      border-color: #f4f4f4;
    }
    .el-step__title{
      font-size: 12px;
      color: var(--slh-white-color);
    }
    .is-finish,
    .is-process{
      .el-step__icon{
        color:var(--slh-white-color);
        border-color: var(--slh-primary-color);
        background-color: var(--slh-primary-color);
      }
      &.el-step__title{
        color: #fff;
      }
    }
    .is-finish{
      .el-step__line{
        background-color: var(--slh-primary-color);
      }
    }
    @media (min-width: 768px){
      &.is-horizontal{
        .el-step__line{
          height:2px;
        }
      }
      .el-step__icon{
        width:30px;
        height:30px;
        font-size: 14px;
      }
      .el-step__title{
        font-size: 14px;
        color: var(--slh-text-second-color);
      }
      .is-finish,
      .is-process{
        &.el-step__title{
          color:var(--slh-primary-color);
        }
      }
    }
  }
}
</style>


