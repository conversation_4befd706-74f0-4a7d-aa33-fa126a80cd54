<template>
  <div class="gallery w-full relative">
    <swiper
      @swiper="onSwiper"
    >
      <swiper-slide v-for="(item, item_index) in data" :key="item_index">
        <div class="img__box">
          <img class="aspect-ele"  :src="item" alt="">
        </div>
      </swiper-slide>
    </swiper>
    <div
      class="flex items-center bg-white absolute left-0 bottom-0 z-10 divide-x"
      v-if="roomImgsSwiper">
      <div class="flex items-center">
        <i
          class="iconfont icon-arrow-left text-primary p-1 leading-4"
          :class="!roomImgsSwiper.isBeginning ? ['cursor-pointer', 'hover:text-primary-dark'] : ['cursor-not-allowed']"
          @click="!roomImgsSwiper.isBeginning && roomImgsSwiper.slidePrev()"></i>
        <span class="text-black px-2 w-10 text-sm text-center italic">{{roomImgsSwiper.realIndex + 1}}/{{roomImgsSwiper.slides.length}}</span>
        <i
          class="iconfont icon-arrow-right text-primary p-1 leading-4 cursor-pointer"
          :class="!roomImgsSwiper.isEnd ? ['cursor-pointer', 'hover:text-primary-dark'] : ['cursor-not-allowed']"
          @click="!roomImgsSwiper.isEnd && roomImgsSwiper.slideNext()"></i>
      </div>
      <div>
        <button
          class="text-primary border-none leading-4 p-1 px-4 cursor-pointer hover:text-primary-dark"
          @click="showPreview">
          <i class="iconfont icon-search"></i>
        </button>
      </div>
    </div>
    <preview
      :urlList="data"
      v-model="previewVisible"
      :initialIndex="roomImgsSwiper.realIndex"
      @switch="previewSwitch"
      v-if="roomImgsSwiper && previewVisible"
    />
  </div>
</template>
<script setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { ref, } from 'vue';
import Preview from 'vue3-preview'

const props = defineProps({
  data: {
    type: Array,
    default: []
  },
})

const roomImgsSwiper = ref(null)
const previewVisible = ref(false)

const onSwiper = (swiper) => {
  roomImgsSwiper.value = swiper
};

const showPreview = () => {
  previewVisible.value = true
  console.log('showPreview', previewVisible.value)
  console.log('showPreview', props.data)
}

const previewSwitch = (index) => {
  if(roomImgsSwiper){
    roomImgsSwiper?.value?.slideTo(index)
  }
}
</script>
<style lang="scss" scoped>
.gallery{
  .img__box{
    @apply aspect-w-4 aspect-h-3;
  }
}
</style>
