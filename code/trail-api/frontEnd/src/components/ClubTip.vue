<template>
  <div class="flex p-3 w-full bg-bg_clube items-center justify-center md:p-4">
    <span class="inline-flex items-center justify-center w-7 h-7 bg-white mr-2 text-primary rounded-full">
      <i class="iconfont icon-club leading-4 text-base"></i>
    </span>
    <div class="text-xs md:text-sm md:inline-flex">
      {{ $t('common.members_tip') }}
      <div>
        <a
          class="px-2 cursor-pointer underline underline-offset-4 text-primary hover:text-primary-dark"
          :href="`${rootPath}/auth/login`">
          {{ $t('common.register') }}/{{ $t('common.login') }}
        </a>
        {{ $t('common.join_members') }}
      </div>
    </div>
  </div>
</template>
<script setup>
import useAppStore from '@/stores/app'
const {rootPath} = useAppStore()
</script>  
