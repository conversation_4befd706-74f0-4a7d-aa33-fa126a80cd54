<template>
  <div
    class="page-header h-65 md:h-125 relative"
    :style="{backgroundImage: `url(${hotelInfo?.hotelBanner || ''})`}"
    v-loading.fullscreen.lock="loading"
  >
    <div class="pt-15 md:pt-25 pb-8 md:pb-20 flex flex-col absolute w-full h-full inset-0">
      <div class="container 2xl:max-w-7xl ml-auto mr-auto flex flex-col flex-1">
        <div class="flex-1 flex flex-col items-center justify-center text-white text-center">
          <h1 class="text-2xl md:text-4xl leading-none">{{ hotelInfo?.hotelName }}</h1>
          <h2 class="text-lg md:text-xl leading-none mt-1">{{ hotelInfo?.hotelNameEn }}</h2>
          <a
            class="inline-flex items-center mt-1 text-white leading-none underline underline-offset-4 text-sm md:text-base"
            v-if="hotelInfo?.hotelLink"
            :href="`/trail-api/hotel/${hotelInfo?.hotelLink}`"
          >
            {{ $t('common.view_hotel_detail') }}
            <i class="iconfont icon-arrow-right2 leading-none text-sm md:text-base"></i>
          </a>
        </div>
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  hotelInfo: Object,
  loading: {
    type: Boolean,
    default: false,
  },
})
</script>
<style lang="scss" scoped>
.page-header{
  width:100%;
  background-color:#000;
  // background-image:url('@/imgs/banner_demo.jpg');
  background-repeat:no-repeat;
  background-size:cover;
  background-position:center center;
  position: relative;
  &::before,
  &::after{
    content:'';
    display:block;
    width:100%;
    position:absolute;
    left:0;
    right:0;
  }
  &::before{
    top:0;
    height:100%;
    opacity:0.62;
    background: linear-gradient(to bottom, rgba(#000, 1), rgba(#000, 0));
  }
  &::after{
    bottom:0;
    height:80px;
    background: linear-gradient(to bottom, rgba(#fff, 0), rgba(#fff, 1));
    @media (min-width: var(--slh-screen-md)){
      height:140px;
    }
  }
}
</style>

