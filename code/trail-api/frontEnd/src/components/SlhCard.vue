<template>
  <div
    class="p-2 md:p-3 lg:p-4 xl:p-5"
    :class="{'bg-bg_gary-light': !noBg}"
  >
    <div
      class="px-3 md:px-5 lg:px-6 xl:px-7 pt-4 lg:pt-5 xl:pt-6 pb-5 md:pb-6 lg:pb-8 xl:pb-9 bg-white"
      v-if="hasInner"
    >
      <h3 class="text-sm md:text-base lg:text-lg text-black mb-4" v-if="title">{{title}}</h3>
      <slot></slot>
    </div>
    <slot v-else></slot>
  </div>
</template>
<script setup>
const props = defineProps({
  title: String,
  noBg: {
    type: Boolean,
    default: false,
  },
  hasInner: {
    type: Boolean,
    default: true,
  },
})
</script>

