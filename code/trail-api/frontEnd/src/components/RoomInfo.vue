
<template>
  <div class="room-info">
    <!-- <div class="info__item">
      <div class="flex-1">
        <p class="text-black text-base leading-none">{{ $t('searchbar.room_amount') }}</p>
      </div>
      <el-input-number class="w-24" v-model="searchParams.rooms" :min="1" :max="roomMaxCont"/>
    </div> -->
    <div class="info__item">
      <div class="flex-1">
        <p class="text-black text-base leading-none">{{ $t('searchbar.adults_amount') }}</p>
        <p class="text-text-light text-xs leading-none mt-2">
          {{ $t('searchbar.ages_up_tip', {"age": childMaxAge}) }}
        </p>
      </div>
      <el-input-number class="w-24" v-model="searchParams.adultsPerRoom" :min="1" :max="adultMaxCont"/>
    </div>
    <div class="border border-line">
      <div class="flex items-center px-3 lg:px-4 xl:px-5 py-3 lg:py-4">
        <div class="flex-1">
          <p class="text-black text-base leading-none">{{ $t('searchbar.children_amount') }}</p>
          <p class="text-text-light text-xs leading-none mt-2">
            {{ $t('searchbar.ages_down_tip', {"age": childMaxAge}) }}
          </p>
        </div>
        <el-input-number class="w-24" v-model="searchParams.childrenPerRoom" :min="0" :max="childMaxCont"/>
      </div>
      <template v-if="childrenAges.length > 0">  
        <div
          class="age__select"
          v-for="(ageItem, index) in childrenAges"
          :key="ageItem.id"
        >
          <div class="flex-1">
            <p class="text-black text-base leading-none">{{ $t('searchbar.children_ages') }} {{ index + 1 }}</p>
          </div>
          <div>
            <el-select
              class="w-32 text-black"
              v-model="ageItem.value"
              :placeholder="$t('searchbar.select_children_ages')"
            >
              <el-option
                v-for="item in ageOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
      </template>
    </div>
    <button id="roomInfoConfimBtn" class="slh-btn-brand w-full" @click="handleRoomInfoConfim">
      {{ $t('searchbar.confirm') }}
    </button>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { ROOM_MAX_CONT, ADULT_MAX_CONT, CHILD_MAX_AGE, CHILD_MAX_CONT } from '@/config/index'
import { reactive, watch, ref } from 'vue'

const { t } = useI18n()
const roomMaxCont = ROOM_MAX_CONT;
const adultMaxCont = ADULT_MAX_CONT;
const childMaxAge = CHILD_MAX_AGE;
const childMaxCont = CHILD_MAX_CONT;

let ageOption = []
for(var i = 0; i <= childMaxAge; i++){
  ageOption.push({
    value: i,
    label: i === 0 ? t('searchbar.age_down_one') : t('searchbar.age_number', {age: i}),
  })
}

const props = defineProps({
  params: Object,
})

const searchParams = reactive(props.params)

const emits = defineEmits(['update:queryParams', 'handleConfim'])

// 确定房间信息
const handleRoomInfoConfim = () => {
  emits('handleConfim', {
    ...searchParams,
    childrenAges: childrenAges.value.map(item => (item.value)).join(','),
  })
}

const childrenAges = ref([])
const childrenAgesClear = ref(false)
watch(
  searchParams,
  (val) => {
    const childrenPerRoom = val.childrenPerRoom
    const _childrenAges = val?.childrenAges || ''
    const realChildrenAges = _childrenAges.split(',')
    let nextChildrenAges = []
    for(let i = 0; i < childrenPerRoom; i++){
      const itemValue = !childrenAgesClear.value && realChildrenAges[i] ? realChildrenAges[i] : ''
      nextChildrenAges.push(itemValue)
    }
    if(childrenPerRoom > 0){
      childrenAges.value = nextChildrenAges.map((item, index) => ({
        id: index + 1,
        value: item,
      }))
    } else {
      childrenAgesClear.value = true
      childrenAges.value = []
    }
  },
  {
    deep: true,
    immediate: true,
  }
)

</script>
<style lang="scss" scoped>
.room-info{
  @apply px-5 lg:px-6 xl:px-7 py-3 lg:py-4 xl:py-5 space-y-2 lg:space-y-3 xl:space-y-4;
  .info__item{
    @apply flex items-center border border-line px-3 lg:px-4 xl:px-5 py-3 lg:py-4;
  }
  .age__select{
    @apply flex items-center border-t border-line px-3 lg:px-4 xl:px-5 py-3 lg:py-4;
  }
}
</style>

