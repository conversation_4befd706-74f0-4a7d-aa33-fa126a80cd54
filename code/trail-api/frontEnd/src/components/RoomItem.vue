<template>
  <SlhCard>
    <div class="flex flex-col md:flex-row">
      <div class="item_left md:w-72 lg:w-80">
        <gallery :data="item?.imageList" v-if="item?.imageList.length > 0" />
        <!-- <ul class="room_features hidden md:block mt-5">
          <li class="flex items-center py-1.5">
            <icon name="local-icon-airo-con" :size="20"/>
            <span class="pl-2 text-sm">空调</span>
          </li>
          <li class="flex items-center py-1.5" v-if="item?.freeWifi"><span class="pl-2 text-sm">免费无线上网</span></li>
        </ul> -->
        <ul
          class="grid grid-cols-2 gap-2 list-disc pl-4 text-sm text-[#999] overflow-hidden relative"
          :class="{'mt-4': item?.imageList.length > 0, 'h-auto': isShowAllFeature, 'h-20': !isShowAllFeature}"
        >
          <template
            v-for="(feature, feature_index) in item?.rmaCodeDesc"
            :key="feature_index"
          >
            <li v-if="feature_index < 5">
              <span
                v-for="(_feature, _feature_index) in feature"
                :key="_feature_index"
              >
                {{ _feature }}
              </span>
            </li>
          </template>
          <span
            class="flex items-center space-x-1 cursor-pointer text-[#666]"
            @click="isShowAllFeature = true"
            v-if="item?.rmaCodeDesc && item?.rmaCodeDesc.length > 5 && !isShowAllFeature"
          >
            <span>{{$t("view_all")}}</span>
            <i class="iconfont icon-arrow-down"></i>
          </span>
          <template
            v-for="(feature, feature_index) in item?.rmaCodeDesc"
            :key="feature_index"
          >
            <li v-if="feature_index >= 5">
              <span
                v-for="(_feature, _feature_index) in feature"
                :key="_feature_index"
              >
                {{ _feature }}
              </span>
            </li>
          </template>
          <span
            class="flex items-center space-x-1 cursor-pointer text-[#666]"
            @click="isShowAllFeature = false"
            v-if="item?.rmaCodeDesc && item?.rmaCodeDesc.length > 5 && isShowAllFeature"
          >
            <span>{{$t("collapse")}}</span>
            <i class="iconfont icon-arrow-up"></i>
          </span>
        </ul>
      </div>
      <div class="item_right flex-1 md:pl-6.5">
        <div class="px-3 py-5 md:p-0 md:pb-5">
          <h3 class="room_title text-xl">{{ item?.roomName || item?.roomNameEn || item?.roomDescription }}</h3>
          <div class="pt-3"></div>
          <div
            class="flex flex-wrap items-center divide-x pb-4 md:py-5 space-x-1 md:space-x-2 text-xs md:text-sm"
            v-if="item?.maxOccupancy || item?.rollawayCount"
          >
            <!-- <span class="text-primary">{{ $t('rate_list.only_rooms_tip', {"area": 2}) }}</span> -->
            <span
              class="flex items-center text-text-light leading-4 pl-1 md:pl-2 "
              v-if="item?.maxOccupancy"
            >
              <i class="iconfont icon-user text-sm md:text-base pr-1"></i>
              {{ $t('rate_list.capacity', {"area": item?.maxOccupancy}) }}
            </span>
            <span
              class="flex items-center text-text-light leading-4 pl-1 md:pl-2"
              v-if="item?.rollawayCount"
            >
              <i class="iconfont icon-chuang text-sm md:text-base pr-1"></i>
              {{ item?.rollawayCount }}
            </span>
            <!-- <span
              class="flex items-center text-text-light leading-4 pl-1 md:pl-2"
            >
              <i class="iconfont icon-daxiao text-sm md:text-base pr-1"></i>
              {{ $t('rate_list.area', {"area": 50}) }}
            </span> -->
          </div>
          <!-- <div class="text-text-light_2 text-sm leading-6 md:leading-7">{{ item?.roomText }}</div> -->
          <div class="text-text-light_2 text-sm leading-6 md:leading-7 relative text-justify">
            <div
              ref="roomTextRef"
              class="overflow-hidden"
              :class="{'h-auto': isShowAllDetail, 'h-15': !isShowAllDetail}"
              v-html="item?.roomText"
            >
            </div>
            <span
              class="flex items-center space-x-1 cursor-pointer text-primary absolute right-0 -bottom-6 bg-white pl-2"
              @click="isShowAllDetail = !isShowAllDetail"
              v-if="isShowFoldBnt && item?.roomText"
            >
              <span>{{$t(!isShowAllDetail ? "detail" : "collapse")}}</span>
              <i class="iconfont" :class="{'icon-arrow-down': !isShowAllDetail, 'icon-arrow-up': isShowAllDetail}"></i>
            </span>
          </div>
        </div>
        <ul class="package_list">
          <template
            v-for="(roomRate, roomRate_index)  in item?.roomRateList"
            :key="roomRate_index"
          >
            <li
              class="package_item px-2.5 md:px-0 py-3.5 md:py-5"
              :class="[isSLHClubCodes(roomRate?.rateCode) ? 'bg-bg_clube px-2.5 md:px-4 py-3.5 md:py-5' : '']"
              v-if="!isSLHClubCodes(roomRate?.rateCode)"
            >
              <h3 class="package_title text-base md:text-xl text-primary" v-if="!isSLHClubCodes(roomRate?.rateCode)">{{ roomRate?.rateName }}</h3>
              <div class="package_features py-3 md:py-4">
                <div class="feature_item text-left justify-start">
                  <span
                    class="flex items-center text-text-light text-xs md:text-sm"
                    v-if="roomRate?.breakfastInclusive && roomRate?.breakfastInclusive === 'true'"
                  >
                    <i class="iconfont icon-breakfast leading-4 pr-2 text-black text-sm md:text-base"></i>
                    {{ $t('rate_list.with_breakfast') }}
                  </span>
                  <span
                    class="flex items-center text-text-light text-xs md:text-sm"
                    v-if="roomRate?.freeWifi && roomRate?.freeWifi === 'true'"
                  >
                    <i class="iconfont icon-wifi leading-4 pr-2 text-black text-sm md:text-base"></i>
                    {{ $t('rate_list.free_wifi') }}
                  </span>
                  <span
                    class="flex items-center text-text-light text-xs md:text-sm"
                    v-if="roomRate?.guaranteePolicy"
                  >
                    <i class="iconfont icon-creditcard leading-4 pr-2 text-black text-sm md:text-base"></i>
                    {{ 
                      roomRate?.guaranteePolicy === 'GCC' ? $t(`rate_list.guaranteePolicy.GCC`) : 
                      roomRate?.guaranteePolicy.includes('DCC') ? $t(`rate_list.guaranteePolicy.DCC`) :
                      roomRate?.guaranteePolicy
                    }}
                  </span>
                  <p class="text-text-light_2 mt-2 text-sm leading-6" v-html="roomRate?.rateText"></p>
                </div>
              </div>
              <div class="flex flex-col md:flex-row items-end  md:pr-4">
                <div class="flex-1 mb-3 md:mb-0 text-right md:text-left">
                  <span class="text-black text-sm leading-none" v-if="isSLHClubCodes(roomRate?.rateCode)">{{$t('rate_list.member_price')}}</span>
                  <p class="text-primary text-xl md:text-2xl leading-none">
                    <SlhPrice :price="roomRate?.cnyAverageRate100" />
                  </p>
                  <p class="mt-1 text-xs md:text-sm">
                    <span class="text-black pr-1">{{ $t('rate_list.every_night') }}</span>
                    <span class="text-text-light_2">{{ $t('rate_list.included_taxes') }}</span>
                  </p>
                </div>
                <SlhButton
                  class="w-full md:w-45"
                  :type="isChecked(roomRate) ? '' : 'brand'"
                  @click="emits('hadleBooking', item, roomRate)"
                >
                  {{ isChecked(roomRate) ? $t('order.checked') : "" }}{{ isSLHClubCodes(roomRate?.rateCode) ? $t('rate_list.member_price_reservation') : $t('rate_list.book_now') }}
                </SlhButton>
              </div>
            </li>
          </template> 
        </ul>
      </div>
    </div>
  </SlhCard>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import gallery from '@/components/Gallery.vue'
import SlhButton from '@/components/SlhButton.vue'
import SlhCard from '@/components/SlhCard.vue'
import SlhPrice from '@/components/SlhPrice.vue'
import {isSLHClubCodes} from '@/utils/util'

const props = defineProps({
  item: Object,
  checkedRooms: {
    type: Array,
    default: () => []
  }
})
const emits = defineEmits(['hadleBooking'])

const isShowAllFeature = ref(false) // 是否显示全部特色
const isShowAllDetail = ref(true) // 是否显示全部房型介绍
const isShowFoldBnt = ref(false) // 是否显示折叠按钮
const roomTextRef = ref(null) // 房间信息

// 判断是否已选中
const isChecked = (item) => {
  const curItem = props.checkedRooms.find(cell => cell?.roomRate?.roomCode === item.roomCode && cell?.roomRate?.rateCode === item.rateCode)
  return curItem ? true : false
}

onMounted(() => {
  if(roomTextRef.value.offsetHeight > 56){
    isShowFoldBnt.value = true
    isShowAllDetail.value = false
  }
})

</script>
<style lang="scss">
.room_item{
  +.room_item{
    margin-top:22px;
  }
}
</style>

<style lang="scss" scoped>
.room_item{
  // .room_imgs{
  //   width:260px;
  // }
  .room_features{
    li{
      &::before{
        content:'';
        display:block;
        width:3px;
        height:3px;
        background-color:var(--slh-black-color);
        margin-right:0.5em;
      }
    }
  }
}
</style>


