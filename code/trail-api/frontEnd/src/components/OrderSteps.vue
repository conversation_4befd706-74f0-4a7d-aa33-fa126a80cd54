<template>
  <el-steps
    class="slh_order_steps w-full lg:w-147.5"
    :active="active"
    align-center
  >
    <el-step :title="$t('common.order_steps.1')" />
    <el-step :title="$t('common.order_steps.2')" />
    <el-step :title="$t('common.order_steps.3')" />
    <el-step :title="$t('common.order_steps.4')" />
  </el-steps>
</template>
<script setup>
const props = defineProps({
  active: {
    type: Number,
    default: 0,
  }
})
</script>

<style lang="scss">
.slh_order_steps{
  .el-step{
    font-size: 14px;
    color: #787878;
    &.is-horizontal{
      .el-step__line{
        height:1px;
        background-color: #f4f4f4;
      }
    }
    .el-step__icon{
      width:24px;
      height:24px;
      font-size: 12px;
      border-color: #f4f4f4;
    }
    .el-step__title{
      font-size: 12px;
      color: #787878;
    }
    .is-finish,
    .is-process{
      .el-step__icon{
        color:var(--slh-white-color);
        border-color: var(--slh-primary-color);
        background-color: var(--slh-primary-color);
      }
      &.el-step__title{
        color: var(--slh-primary-color);
      }
    }
    .is-finish{
      .el-step__line{
        background-color: var(--slh-primary-color);
      }
    }
    @media (min-width: 768px){
      &.is-horizontal{
        .el-step__line{
          height:2px;
        }
      }
      .el-step__icon{
        width:30px;
        height:30px;
        font-size: 14px;
      }
      .el-step__title{
        font-size: 14px;
      }
    }
  }
}
</style>


