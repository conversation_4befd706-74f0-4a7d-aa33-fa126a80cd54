<template>
  <div
    class="calendar__cell"
    :class="{'is_checked': isCheckIn || isCheckOut, 'is_today': isToday, 'is_disabled': isDisabled}"
    @click="!isDisabled && emits('dayClick', data)"
  >
    <span class="text-sm">{{ isToday ? $t('searchbar.today') : day }}</span>
    <span class="text-xs" v-if="isCheckIn">{{ $t('searchbar.checkin') }}</span>
    <span class="text-xs" v-if="isCheckOut">{{ $t('searchbar.checkout') }}</span>
  </div>
</template>
<script setup>
import dayjs from 'dayjs'
import { computed, } from 'vue'

const props = defineProps({
  data: Object,
  checkIn: String,
  checkOut: String,
})

const emits = defineEmits(['dayClick'])
const isToday = computed(() => dayjs(props.data?.day).isSame(dayjs(), 'day'));
const isDisabled = computed(() => dayjs(props.data?.day).isBefore(dayjs(), 'day'));
const isCheckIn = computed(() => dayjs(props.data?.day).isSame(dayjs(props.checkIn), 'day'));
const isCheckOut = computed(() => dayjs(props.data?.day).isSame(dayjs(props.checkOut), 'day'));
const day = computed(() => dayjs(props.data?.day).format('DD'));
</script>
<style lang="scss" scoped>
.calendar__cell{
  @apply w-full h-full flex flex-col items-center justify-center;
  &.is_today{
    background-color: var(--el-calendar-selected-bg-color);
  }
  &.is_checked{
    @apply bg-brand text-black;
  }
  &.is_disabled{
    color: var(--el-text-color-placeholder);
    @apply cursor-not-allowed;
  }
}
</style>

