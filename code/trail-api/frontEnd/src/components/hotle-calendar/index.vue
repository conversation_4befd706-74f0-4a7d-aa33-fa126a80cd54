<template>
  <div class="hotle-calendar">
    <el-calendar ref="calendar">
      <template #header="{ date }">
        <div class="w-full py-4 flex items-center">
          <span class="space-x-4">
            <span class="text-text-light" @click="selectDate('prev-year')">
              {{ $t('searchbar.prev_year') }}
            </span>
            <span class="text-text-light" @click="selectDate('prev-month')">
              {{ $t('searchbar.prev_month') }}
            </span>
          </span>
          <span class="flex-1 text-sm text-black px-1 text-center">{{ date }}</span>
          <span class="space-x-4">
            <span class="text-text-light" @click="selectDate('next-month')">
              {{ $t('searchbar.next_month') }}
            </span>
            <span class="text-text-light" @click="selectDate('next-year')">
              {{ $t('searchbar.next_year') }}
            </span>
          </span>
        </div>
      </template>
      <template #date-cell="{ data }">
        <day-cell
          :data="data"
          :checkIn="checkInValue"
          :checkOut="checkOutValue"
          @dayClick="handleDayClick"
        />
      </template>
    </el-calendar>
  </div>
</template>
<script setup>
import dayjs from 'dayjs'
import { ref, computed } from 'vue'
import DayCell from './DayCell.vue'

const props = defineProps({
  checkIn: String,
  checkOut: String,
})

const emits = defineEmits(['update:checkIn', 'update:checkOut'])

const checkInValue = computed({
  get: () => props.checkIn,
  set: (value) => {
    emits('update:checkIn', value)
  }
})
const checkOutValue = computed({
  get: () => props.checkOut,
  set: (value) => {
    emits('update:checkOut', value)
  }
})

const calendar = ref()
const selectDate = (val) => {
  if (!calendar.value) return
  calendar.value.selectDate(val)
}

// 处理日历中日期点击
const handleDayClick = (data) => {
  const {type, day, } = data
  // type: 'prev-month' | 'current-month' | 'next-month'
  if(type === 'current-month'){
    if(!checkInValue.value && !checkOutValue.value){
      checkInValue.value = day
    } else {
      if(checkInValue.value && checkOutValue.value){
        checkInValue.value = day
        checkOutValue.value = ''
      } else {
        const checkIn = checkInValue.value
        if(dayjs(day).isBefore(dayjs(checkIn))){
          checkInValue.value = day
          checkOutValue.value = checkIn
        } else {
          checkOutValue.value = day
        }
      }
    }
  }
}
</script>
<style lang="scss">
.hotle-calendar{
  .el-calendar{
    .el-calendar__header{
      @apply p-0;
    }
    .el-calendar__body{
      @apply px-0;
    }
    .el-calendar-table{
      .el-calendar-day{
        height: 50px;
        @apply p-0;
      }
    }
  }
}
</style>