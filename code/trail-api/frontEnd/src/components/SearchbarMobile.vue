<template>
  <div class="w-full">
    <div class="w-full bg-white flex items-center border-b border-line-light_2" v-if="role === 'hotel'">
      <div class="flex-1 px-3 flex items-center h-12.5">
        <div class="flex items-center justify-between text-text text-lg pr-3 space-x-2 flex-shrink-0" @click="handleDrawerOpen('2')">
          <span class="text-sm">{{ formatIndate }}</span>
          <span class="text-xs text-text-light px-2 py-1 leading-none rounded-full border border-line-light_2">
            {{ nights }}{{ $t('common.night') }}
          </span>
          <span class="text-sm">{{ formatOutdate }}</span>
        </div>
        <div class="flex-1 flex items-center justify-center text-sm space-x-1 pl-3 border-l border-line-light_2" @click="handleDrawerOpen('1')">
          <span>{{ searchParams.adultsPerRoom }}{{ $t('common.aldult') }}</span>
          <span v-if="searchParams.childrenPerRoom">{{ searchParams.childrenPerRoom }}{{ $t('common.child') }}</span>
        </div>
      </div>
      <button class="slh-btn-brand px-5 h-12.5" @click="handleConfim()">
        {{$t(`view.hotel_detail.booking`)}}
      </button>
    </div>
    <div class="w-full p-5 bg-white shadow" v-else>
      <div class="pb-4 border-b border-line-light_2" v-if="!disabledKeyword">
        <p class="slh-searchbar-lable text-text-light">{{ $t('searchbar.destination.label') }}</p>
        <div class="flex pt-1" @click="openSearchDrawer">
          <input
            v-model="keyword"
            :disabled="disabledKeyword"
            class="slh-searchbar-input flex-1 pl-0"
            type="text"
            :placeholder="$t('searchbar.destination.placeholder')"
          >
          <i class="iconfont icon-search text-lg text-text-light"></i>
        </div>
      </div>
      <div>
        <div class="w-full flex items-center py-5 border-b">
          <div class="flex-1 flex items-center justify-between text-text text-base pr-3" @click="handleDrawerOpen('2')">
            <div>
              <p class="slh-searchbar-lable text-text-light">{{ $t('searchbar.checkin') }}</p>
              {{ formatIndate }}
            </div>
            <div class="flex-1 flex justify-center">
              <span class="text-xs text-text-light px-2 py-1 leading-none rounded-full border border-line-light_2">
                {{ nights }}{{ $t('common.night') }}
              </span>
            </div>
            <div>
              <p class="slh-searchbar-lable text-text-light">{{ $t('searchbar.checkout') }}</p>
              {{ formatOutdate }}
            </div>
          </div>
          <div class="pl-3 border-l border-line-light_2">
            <p class="slh-searchbar-lable text-text-light">{{ $t('searchbar.guest') }}</p>
            <div class="flex items-center text-sm space-x-1 text-text-light_2" @click="handleDrawerOpen('1')">
              <!-- <span class="text-text">{{ searchParams.rooms }}{{ $t('searchbar.rooms') }}</span> -->
              <!-- <span>{{ guests }}{{ $t('searchbar.guest') }}</span> -->
              <span>{{ searchParams.adultsPerRoom }}{{ $t('common.aldult') }}</span>
              <span v-if="searchParams.childrenPerRoom">{{ searchParams.childrenPerRoom }}{{ $t('common.child') }}</span>
            </div>
          </div>
        </div>
        <button class="w-full slh-btn-brand" @click="handleConfim()">
          {{$t(`searchbar.search_${role === 'booking' ? 'room' : 'hotel'}`)}}
        </button>
      </div>
    </div>
    <el-drawer
      class="searchbar_drawer"
      v-model="drawer"
      :title="$t('searchbar.select_stay_title')"
      direction="ltr"
      size="100%"
      :append-to-body="true"
      :before-close="handleDrawerClose"
    >
      <el-collapse v-model="activeName">
        <el-collapse-item :title="$t('searchbar.guest_amount')" name="1">
          <template #title>
            <div class="flex-1 flex items-center pr-1">
              <span class="flex-1">{{ $t('searchbar.room_info') }}</span>
              <span class="flex items-center text-sm">
                <!-- <span class="text-primary">{{ searchParams.rooms }}{{ $t('searchbar.rooms') }}</span> -->
                <!-- <span class="text-primary">{{ guests }}{{ $t('searchbar.guest') }}</span> -->
                <span>{{ searchParams.adultsPerRoom }}{{ $t('common.aldult') }}</span>
                <span v-if="searchParams.childrenPerRoom">{{ searchParams.childrenPerRoom }}{{ $t('common.child') }}</span>
              </span>
            </div>
          </template>
          <div class="divide-y divide-line-light_2 px-4">
            <!-- <div class="flex items-center py-3">
              <span class="flex-1 text-black text-sm leading-none inline-flex items-center">
                {{ $t('searchbar.room_amount') }}
              </span>
              <el-input-number class="!w-[100px]" v-model="searchParams.rooms" :min="1" :max="roomMaxCont"/>
            </div> -->
            <div class="flex items-center py-3">
              <span class="flex-1 text-black text-sm leading-none inline-flex items-center">
                {{ $t('searchbar.adults_amount') }}
                <span class="text-text-light text-xs leading-none ml-1">
                  {{ $t('searchbar.ages_up_tip', {'age': childMaxAge}) }}
                </span>
              </span>
              <el-input-number class="!w-[100px]" v-model="searchParams.adultsPerRoom" :min="1" :max="adultMaxCont"/>
            </div>
            <div class="flex items-center py-3">
              <span class="flex-1 text-black text-sm leading-none inline-flex items-center">
                {{ $t('searchbar.children_amount') }}
                <span class="text-text-light text-xs leading-none ml-1">
                  {{ $t('searchbar.ages_down_tip', {'age': childMaxAge}) }}
                </span>
              </span>
              <el-input-number class="!w-[100px]" v-model="searchParams.childrenPerRoom" :min="0" :max="childMaxCont"/>
            </div>
            <div class="py-3 space-y-3" v-if="childrenAges.length > 0">
              <div
                class="flex items-center"
                v-for="(ageItem, index) in childrenAges"
                :key="ageItem.id"
              >
                <span class="flex-1 text-black text-sm leading-none inline-flex items-center">
                  {{ $t('searchbar.children_ages') }} {{ index + 1 }}
                </span>
                <div>
                  <el-select
                    v-model="ageItem.value"
                    class="w-32 text-black"
                    :placeholder="$t('searchbar.select_children_ages')"
                  >
                    <el-option
                      v-for="item in ageOption"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </div>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item :title="$t('searchbar.checkin_date')" name="2">
          <template #title>
            <div class="flex-1 flex items-center pr-1">
              <span class="flex-1">{{ $t('searchbar.checkin_date') }}</span>
              <span class="flex items-center text-sm space-x-2">
                <span class="text-primary">{{ searchParams.indate }}</span>
                <span class="text-xs text-primary px-2 py-1 leading-none rounded-full border border-line-light_2">
                  {{nights}}{{ $t('common.night') }}
                </span>
                <span class="text-primary">{{ searchParams.outdate }}</span>
              </span>
            </div>
          </template>
          <div class="px-4">
            <HotleCalendar
              v-model:checkIn="searchParams.indate"
              v-model:checkOut="searchParams.outdate"
            />
          </div>
        </el-collapse-item>
      </el-collapse>
      <template #footer>
        <button class="w-full slh-btn-brand" @click="handleDrawerConfim">
          {{ $t(role === 'hotel' ? 'searchbar.booking_hotel' : 'common.confirm') }}
        </button>
      </template>
    </el-drawer>
    <el-drawer
      class="searchbar_drawer"
      v-model="searchDrawer"
      :title="$t('searchbar.search_destination')"
      direction="ltr"
      size="100%"
      :append-to-body="true"
      :before-close="searchDrawerClose"
      :show-close="false"
    >
      <template #header="{ close, titleId, titleClass }">
        <div class="w-full flex items-center">
          <button class="border-none" @click="searchDrawerClose">
            <i class="iconfont icon-arrow-left text-lg text-text-light"></i>
          </button>
          <div class="flex-1 flex">
            <input
              v-model="keyword"
              class="slh-searchbar-input flex-1"
              type="text"
              :placeholder="$t('searchbar.destination.placeholder')"
            >
            <i class="iconfont icon-search text-lg text-text-light"></i>
          </div>
        </div>
      </template>
      <div class="search-result" v-if="keyword">
        <div class="w-full" v-if="searchResult && (searchResult.cities.length || searchResult.countries.length || searchResult.hotels.length)">
          <div class="result_setion"
            v-for="(item, key) in searchResult"
            :key="key"
          >
            <template v-if="item.length">
              <h3 class="result__title">{{$t(`searchbar.${key}`)}}</h3>
              <div
                class="result__item"
                v-for="(cell, cell_index) in item"
                :key="cell_index"
                @click="handleSearchClick(key, cell.title, cell)"
              >
                <div v-if="key === 'hotels'">
                  <p class="item__title">{{ cell.title }}</p>
                  <p class="item__info">{{ cell.hotelAliasEn || cell.hotelNameEn}} </p>
                  <p class="item__info">{{ cell.countryName }} {{ cell.cityName }}</p>
                </div>
                <template  v-else>
                  <span class="item__title">{{ cell.title }}</span>
                  <span class="item__num">{{$t('searchbar.hotels_num', {num: cell.count})}}</span>
                </template>
              </div>
            </template>
          </div>
        </div>
        <div v-else>
          <el-empty :image-size="100" />
        </div>
      </div>
      <el-tabs tab-position="left" class="w-full" v-else>
        <el-tab-pane
          v-for="item in destination"
          :label="item.label"
          :key="item.key"
        >
          <div class="p-2 space-y-4">
            <template v-if="item.key === 'other'">
              <div
                v-for="(continent, continent_index) in item.continents"
                :index="continent_index"
                class="space-y-4"
              >
                <h2 class="text-sm text-black pb-2 border-b border-line">{{continent.title}}</h2>
                <div v-if="continent.cities && continent.cities.length">
                  <h3 class="text-black text-sm mb-3">{{$t('searchbar.hot_city')}}</h3>
                  <div class="grid grid-cols-2 gap-3">
                    <el-tag
                      v-for="(city , city_index) in continent.cities"
                      :key="city_index"
                      @click="handleSearchClick('cities', city.cityName, city)"
                      effect="light"
                    >
                      {{city.cityName}}
                    </el-tag>
                  </div>
                </div>
                <div v-if="continent.countries && continent.countries.length">
                  <h3 class="text-black text-sm mb-3">{{$t('searchbar.countries')}}</h3>
                  <div class="grid grid-cols-2 gap-3">
                    <el-tag
                      v-for="(country , country_index) in continent.countries"
                      :key="country_index"
                      @click="handleSearchClick('countries', country.countryName, country)"
                    >
                      {{country.countryName}}
                    </el-tag>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              <div v-if="item.cities && item.cities.length">
                <h3 class="text-black text-sm mb-3">{{$t(`searchbar.${item.key === 'China' ?  'cities' : 'hot_city'}`)}}</h3>
                <div class="grid grid-cols-2 gap-3">
                  <el-tag
                    v-for="(city , city_index) in item.cities"
                    :key="city_index"
                    @click="handleSearchClick('cities', item.key === 'hot' ? city.name : city.cityName, city)"
                    effect="light"
                  >
                    {{item.key === 'hot' ? city.name : city.cityName}}
                  </el-tag>
                </div>
              </div>
              <div v-if="item.countries && item.countries.length">
                <h3 class="text-black text-sm mb-3">{{$t(`searchbar.${item.key === 'hot' ? 'hot_country' : 'countries'}`)}}</h3>
                <div class="grid grid-cols-2 gap-3">
                  <el-tag
                    v-for="(country , country_index) in item.countries"
                    :key="country_index"
                    @click="handleSearchClick('countries', item.key === 'hot' ? country.name : country.countryName, country)"
                  >
                    {{item.key === 'hot' ? country.name : country.countryName}}
                  </el-tag>
                </div>
              </div>
            </template>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>
<script setup>
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
import { CHILD_MAX_AGE, DATE_FORMAT4, ROOM_MAX_CONT, ADULT_MAX_CONT, CHILD_MAX_CONT, } from '@/config/index'
import { ref, reactive, computed, watch, } from 'vue'
import { ElMessage } from 'element-plus'
import HotleCalendar from '@/components/hotle-calendar/index.vue'
import {searchDestination} from '@/utils/util'

const roomMaxCont = ROOM_MAX_CONT;
const adultMaxCont = ADULT_MAX_CONT;
const childMaxAge = CHILD_MAX_AGE;
const childMaxCont = CHILD_MAX_CONT;

const { t } = useI18n()
const props = defineProps({
  queryParams: Object,
  destination: {
    type: Object,
    default: () => [],
  },
  role: {
    type: String,
    default: 'hotle',
  },
  disabledKeyword: {
    type: Boolean,
    default: false,
  },
})

const searchParams = reactive({
  indate: props.queryParams.indate,
  outdate: props.queryParams.outdate,
  rooms: props.queryParams.rooms,
  adultsPerRoom: props.queryParams.adultsPerRoom,
  childrenPerRoom: props.queryParams.childrenPerRoom,
  childrenAges: props.queryParams.childrenAges,
  keyword: props.queryParams.keyword,
})

const emits = defineEmits(['update:destination', 'search'])
const drawer = ref(false) // 选择入住条件抽屉
const activeName = ref(['1', '2'])
const searchDrawer = ref(false) // 搜索目的地抽屉
const keyword = ref(searchParams.keyword || '') // 搜索框聚焦
const searchResult = ref(null) // 搜索数据

watch(
  props,
  (value) => {
    if(value?.queryParams && value?.queryParams?.keyword){
      keyword.value = value?.queryParams?.keyword
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

// 客人数
const guests = computed(() => {
  const guests = searchParams.adultsPerRoom + searchParams.childrenPerRoom;
  return guests;
});

const nights = computed(() => {
  const nights = searchParams.outdate && searchParams.indate ? dayjs(searchParams.outdate).diff(dayjs(searchParams.indate), 'day') : 0;
  return nights;
});

const formatIndate = computed(() => {
  return searchParams.indate ? dayjs(searchParams.indate).format(DATE_FORMAT4) : '-';
});

const formatOutdate= computed(() => {
  return searchParams.outdate ? dayjs(searchParams.outdate).format(DATE_FORMAT4) : '-';
});

let ageOption = []
for(var i = 0; i <= childMaxAge; i++){
  ageOption.push({
    value: i,
    label: i === 0 ? t('searchbar.age_down', {'age': 1}) : t('searchbar.age', {'age': i}),
  })
}

const handleDrawerOpen = (id) => {
  // activeName.value = id
  drawer.value = true
}

const handleDrawerClose = () => {
  drawer.value = false
}
const handleConfim = () => {
  drawer.value = false
  const data = {
    ...searchParams,
    nights: nights.value,
  }
  if(!searchParams.outdate){
    ElMessage({
      message: t('outdate_cannot_empty'),
      type: 'warning',
    })
    return
  }
  emits('search', data)
  setTimeout(() => {
    keyword.value = ''
  })
}

const openSearchDrawer = () => {
  if(props.disabledKeyword){
    return
  }
  searchDrawer.value = true
}

const searchDrawerClose = () => {
  searchDrawer.value = false
}

watch(
  keyword,
  () => {
    const result = searchDestination(keyword.value)
    searchResult.value = result
    // console.log('keyword!', keyword.value, 'result', result)
  },
  {
    immediate: true,
  },
)

const childrenAges = ref([])
const childrenAgesClear = ref(false)
watch(
  searchParams,
  (val) => {
    const childrenPerRoom = val.childrenPerRoom
    const _childrenAges = val?.childrenAges || ''
    const realChildrenAges = _childrenAges.split(',')
    let nextChildrenAges = []
    for(let i = 0; i < childrenPerRoom; i++){
      const itemValue = !childrenAgesClear.value && realChildrenAges[i] ? realChildrenAges[i] : ''
      nextChildrenAges.push(itemValue)
    }
    if(childrenPerRoom > 0){
      childrenAges.value = nextChildrenAges.map((item, index) => ({
        id: index + 1,
        value: item,
      }))
    } else {
      childrenAgesClear.value = true
      childrenAges.value = []
    }
  },
  {
    deep: true,
    immediate: true,
  }
)

const handleSearchClick = (type, name, item) => {
  keyword.value = name
  searchDrawerClose()
  emits('update:destination', {
    type: type,
    data: item,
  })
  searchParams.keyword = name
}

const handleDrawerConfim = () => {
  if(props?.role === 'hotel'){
    handleConfim()
  } else {
    if(!searchParams.outdate){
      ElMessage({
        message: t('outdate_cannot_empty'),
        type: 'warning',
      })
      return
    }
    handleDrawerClose()
  }
}

</script>

<style lang="scss">
.searchbar_drawer{
  .el-drawer__header{
    @apply mb-0 px-4 py-3;
  }
  .el-drawer__title{
    @apply text-center;
  }
  .el-drawer__body{
    background-color:#f6f7f9; 
    @apply p-0;
  }
  .el-collapse{
    @apply border-none mt-3;
    .el-collapse-item__header{
      @apply px-4 flex h-[50px];
      .el-collapse-item__arrow{
        @apply text-base font-bold;
      }
    }
    .el-collapse-item__content{
      @apply pb-2;
    }
    .el-collapse-item{
      +.el-collapse-item{
        @apply mt-3;
      }
    }
  }
  .el-input-number{
    width:90px;
    $btnSize:28px;
    @apply text-black;
    .el-input{
      .el-input__wrapper{
        padding-left:$btnSize;
        padding-right:$btnSize;
        @apply shadow-none;
        &:hover,
        &.is-focus{
          @apply shadow-none;
        }
      }
    }
    .el-input-number__decrease,
    .el-input-number__increase{
      width:$btnSize;
      height:$btnSize;
      @apply border border-line flex items-center justify-center rounded-full shadow-none;
      .el-icon{
        @apply font-bold;
      }
      &:hover{
        ~.el-input{
          .el-input__wrapper{
            @apply shadow-none;
          }
        }
      }
    }
  }
  // 搜索结果
  .search-result{
    .result_setion{
      .result__title{
        @apply py-3 px-3 bg-primary-light_3 text-base text-primary border-t border-b border-line-light_5;
      }
      .result__item{
        @apply flex px-3 py-3 justify-between text-sm text-text;
      }
      .item__title{
        @apply flex-1 pr-2;
      }
      .item__info{
        @apply text-xs text-text-light;
      }
      .result__num{

      }
    }
  }
}
</style>