@import 'element-plus/dist/index.css';
// @import 'swiper/css/bundle';
@import '__variable';
@import '__slh_layout';
@import './components/com-page-header';
@import './components/com-banner-swiper';
@import './components/com-tabs';
@import './components/com-col-swiper';
@import './components/com-ellipsis';

input{
  &[type="text"]{
    &:focus{
      box-shadow:none;
    }
  }
}

html,body{
  width:100%;
  min-height:100%;
  background-color: #fff;
  // background-color: darken($color: #f5f5f5, $amount: 10) !important;
  // background-color: mix($black-color, $brand-color, 10%) !important;
}

#app{
  width:100%;
  // min-height:100vh;
}

.text-shadow{
  text-shadow:1px 1px 4px rgba(0, 0, 0, 0.5);
}

#app{
  // element-plus 样式调整
  .el-form-item{
    margin-bottom:1rem;
  }
  .el-button{
    height:var(--el-component-size);
  }
  .el-button--large{
    height:var(--el-component-size-large);
  }
  .el-radio-group{
    --el-color-primary:var(--slh-brand-color);
    --el-color-white:var(--slh-black-color);
    .el-radio-button{
      .el-radio-button__inner{
        width:100%;
        height:var(--el-component-size);
        line-height:var(--el-component-size);
        padding-top: 0;
        padding-bottom: 0;
        font-weight: normal;
        &:hover{
          --el-color-primary:var(--slh-black-color);
        }
      }
      &.el-radio-button--large{
        .el-radio-button__inner{
          height:var(--el-component-size-large);
          line-height:var(--el-component-size-large);
        }
      }
    }
  }
}

// 查看图片
.pswp{
  .pswp__item{
    opacity:0;
  }
  img{
    // width:100vw !important;
    // height:100vh !important;
    object-fit:contain;
  }
}

/* 仅针对iOS设备的样式 */
@media (max-width: 768px){
  @supports (-webkit-box-orient: vertical) {
    .ellipsis__content{
      -webkit-line-clamp: 1 !important;
      p{
        display:inline-block;
      }
    }
  }
}

#footer{
  &.has_fixed{
    @apply pb-16;
  }
}

#gotopBtn{
  @apply m-0 p-0 w-10 h-10 fixed bottom-14 md:bottom-5 right-2 rounded-full bg-[#282828] z-10 flex items-center justify-center text-white cursor-pointer;
  &._hidden{
    @apply hidden;
  }
  .iconfont{
    font-size:20px;
  }
  &:hover{
    @apply bg-[#f4cc2c] text-[#282828];
  }
}

// mapbox popup 样式
.mapboxgl-popup{
  .mapboxgl-popup-close-button{
    @apply text-xl py-1 px-1.5 text-text-light border-none focus-visible:outline-none;
    line-height:1;
  }
  .hotle_map_popup{
    min-width: 200px;
    h3{
      @apply text-base text-black pr-5;
    }
    p{
      @apply text-sm mt-1 text-text-light;
    }
  }
}

