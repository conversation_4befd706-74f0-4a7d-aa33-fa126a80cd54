.com-banner-swiper{
  // 首页-顶部baner
  &.theme__1{
    --swiper-navigation-color: #000;
    --swiper-navigation-size: 20px;
    --swiper-pagination-bullet-inactive-opacity:0.2;
    --swiper-pagination-bullet-inactive-color:#fff;
    .swiper-button-next,
    .swiper-button-prev{
      margin-top:-18px;
      @apply bg-white rounded-full w-9 h-9 p-2 hidden md:flex;
    }
    .swiper-button-next{
      @apply right-10;
    }
    .swiper-button-prev{
      @apply left-10;
    }
    .swiper-pagination{
      @apply bottom-14 md:bottom-24;
      .swiper-pagination-bullet{
        margin:0 3px;
        width:20px;
        height:3px;
        @apply bg-white rounded-none md:h-1;
        &-active{
          width:50px;
          @apply opacity-100;
        }
      }
    }
  }
  // 首页-推荐酒店
  &.theme__2{
    --swiper-navigation-color: #fff;
    --swiper-navigation-size: 20px;
    --swiper-pagination-bullet-inactive-opacity:0.4;
    --swiper-pagination-bullet-inactive-color:#fff;
    .swiper-pagination{
      width:auto;
      height:auto;
      bottom:initial;
      left:initial;
      @apply flex items-center top-4 right-3;
      .swiper-pagination-bullet{
        margin:0 3px;
        width:6px;
        height:6px;
        @apply bg-white;
        &-active{
          width:9px;
          height:9px;
          @apply opacity-100;
        }
      }
    }
  }
  // 酒店详情-推荐酒店幻灯片
  &.theme__3{
    .swiper__slide{
      @apply md:px-8 lg:px-12 xl:px-15;
      .slide__inner{
        @apply flex flex-col md:flex-row items-stretch md:bg-white p-4 md:p-5 xl:p-7 text-xs md:text-sm text-text-light;
      }
      .hotel__img__box{
        @apply w-full md:w-[281px];

      }
      .hotel__img{
        @apply w-full h-full;
        .aspect-ele{
          @apply w-full h-full;
        }
      }
      .hotel__name{
        @apply text-lg md:text-xl lg:text-2xl text-black;
      }
      .hotel__info{
        @apply w-full md:w-auto flex-1 pt-4  md:pl-5 bg-white p-4 md:px-0;
      }
      .hotel__en__name{
        @apply mt-1 text-xs lg:text-sm;
      }
      .hotel__desc{
        @apply text-sm text-text-light mt-2 line-clamp-1 hidden md:block;
      }
      .hotel__address{
        @apply flex items-center leading-none mt-2 md:mt-3;
        .address__icon{
          @apply text-base lg:text-xl leading-none;
        }
      }
      .hotel__actions{
        @apply flex space-x-2 mt-3;
      }
    }
    --swiper-navigation-size: 20px;
    .swiper-button-next,
    .swiper-button-prev{
      margin-top:-15px;
      @apply text-primary bg-white p-2 rounded-full w-[30px] h-[30px];
    }
    .swiper-button-next{
      @apply right-0;
    }
    .swiper-button-prev{
      @apply left-0;
    }
    .swiper-pagination{
      @apply w-auto left-auto right-20 bottom-5 md:bottom-5;
      .swiper-pagination-bullet{
        margin:0 3px;
        width:20px;
        height:3px;
        @apply bg-primary rounded-none md:h-1;
        &-active{
          width:50px;
          @apply opacity-100;
        }
      }
    }
    @media (min-width: 768px){
      --swiper-navigation-size: 36px;
      .swiper-button-next,
      .swiper-button-prev{
        margin-top:-18px;
        @apply bg-transparent w-[36px] h-[36px];
      }
    }
  }
}