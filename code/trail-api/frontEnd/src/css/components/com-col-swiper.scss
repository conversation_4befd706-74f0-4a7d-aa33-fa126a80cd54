.com-col-swiper{
  @apply md:pt-6 pb-3 md:pb-10;
  --swiper-navigation-color: #000;
  --swiper-navigation-size: 20px;
  --swiper-pagination-bullet-inactive-opacity:0.4;
  --swiper-pagination-bullet-inactive-color:#fff;
  .swiper-button-next,
  .swiper-button-prev{
    margin-top:-18px;
    @apply bg-white rounded-full w-9 h-9 p-2 hidden md:flex;
  }
  .swiper-button-next{
    @apply right-10;
  }
  .swiper-button-prev{
    @apply left-10;
  }
  .swiper-pagination{
    @apply bottom-2;
    .swiper-pagination-bullet{
      margin:0 3px;
      width:20px;
      height:3px;
      @apply bg-brand rounded-none md:h-1;
      &-active{
        width:50px;
        @apply opacity-100;
      }
    }
  }
}
.com-col-swiper-btns{
  @apply flex;
  .swiper__btn{
    @apply cursor-pointer flex items-center justify-center w-[28px] h-[28px] md:w-[36px] md:h-[36px] bg-primary text-white hover:bg-primary-dark;
    .btn__icon{
      @apply leading-none text-base;
    }
    &.swiper-button-disabled,
    &.__disabled{
      @apply bg-black bg-opacity-10 cursor-not-allowed;
    }
  }
}
