.com-page-header{
  @apply w-full relative bg-black;
  &::before,
  &::after{
    content:'';
    display:block;
    width:100%;
    position:absolute;
    left:0;
    right:0;
    height:50%;
    z-index:2;
  }
  &::before{
    top:0;
    background: linear-gradient(to bottom, rgba(#000, 0.5), rgba(#000, 0.1));
  }
  &::after{
    bottom:0;
    background: linear-gradient(to bottom, rgba(#000, 0.1), rgba(#000, 0.5));
  }
  &.no__cover{
    &::before,
    &::after{
      display:none;
    }
  }
  .header__banner{
    @apply w-full aspect-w-750 aspect-h-420 md:aspect-w-1680 md:aspect-h-420 object-cover md:min-h-90;
    background:#ba9550  url('@/assets/imgs/loadingImg.gif') no-repeat center center;
    @apply bg-cover md:bg-auto;
    // 其他酒店列表
    &.other__hotel__list{
      @apply w-full aspect-w-750 aspect-h-330 md:aspect-w-1680 md:aspect-h-280 object-cover md:min-h-70;
    }
    // 会员
    &.role__club{
      @apply w-full aspect-w-750 aspect-h-836 md:aspect-w-1680 md:aspect-h-600 object-cover md:min-h-120;
    }
    &.role__hotel{
      @apply w-full aspect-w-750 aspect-h-500 md:aspect-w-1680 md:aspect-h-480 object-cover md:min-h-120;
    }
    //优惠活动 // 深思精选酒店
    &.role__offers,
    &.role__considerate{
      @apply w-full aspect-w-750 aspect-h-500 md:aspect-w-1680 md:aspect-h-600 object-cover md:min-h-120;
    }
  }
  .header__cover{
    @apply pt-15 md:pt-25 flex flex-col absolute top-0 left-0 w-full h-full inset-0 z-10;
  }
  .cover__title{
    @apply font-semibold text-2xl md:text-2xl lg:text-4xl leading-none;
    span{
      @apply relative;
      &::before,
      &::after{
        content:'';
        @apply block w-[1em]  h-[1px] absolute top-1/2 bg-white;
      }
      &::before{
        @apply right-full -translate-x-4;
      }
      &::after{
        @apply left-full translate-x-4;
      }
    }
  }
  .cover__subtitle{
    @apply text-sm lg:text-base xl:text-lg leading-none mt-1;
  }
  // 首页样式
  &.page__home{
    &::before,
    &::after{
      content:'';
      @apply block absolute left-0 right-0 h-1/2;
    }
    &::before{
      @apply top-0 bg-gradient-to-b from-gradient-black-start to-gradient-black-end;
    }
    &::after{
      @apply bottom-0 right-0 bg-gradient-to-t from-gradient-black-start to-gradient-black-end;
    }
    .header__banner{
      @apply w-full aspect-w-750 aspect-h-836 md:aspect-w-1680 md:aspect-h-668 object-cover;
    }
    .header__cover{
      @apply pb-20 md:pb-39 bg-transparent;
    }
    .cover__subtitle{
      @apply text-base lg:text-lg xl:text-xl leading-none mt-1;
    }
  }
}

html[lang="zh_TW"]{
  .com-page-header{
    .header__banner{
      background-image:url('@/assets/imgs/loadingImg-tw.gif');
    }
  }
}