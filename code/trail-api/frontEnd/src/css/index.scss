.page-index{
  .slh_searchbar{
    box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.4);
  }
  //目的地卡片
  .tabs__content{
    @apply md:aspect-w-1680 md:aspect-h-550;
  }
  .list-cards{
    @apply flex flex-col md:flex-row;
    .hotel__card{
      @apply relative block flex-1 transition delay-150;
      .card__cover{
        @apply absolute left-0 top-0 w-full h-full bg-black bg-opacity-40 flex flex-col justify-center items-center md:py-12 transition;
      }
      .card__img{
        @apply w-full h-full;
        .aspect-ele{
          @apply w-full h-full;
        }
      }
      .cover__title{
        @apply text-lg md:text-xl lg:text-2xl xl:text-3xl text-white font-semibold md:w-[1em];
      }
      .cover__btn{
        @apply hidden md:flex;
      }
      @media (min-width: 768px){
        &:hover{
          @apply flex-none lg:w-125 xl:w-172.5 2xl:w-187.5;
          .card__cover{
            @apply bg-opacity-0 pt-0 px-6 items-start;
          }
          .cover__title{
            padding-left:0.5em;
            padding-right:0.5em;
            @apply w-[2em] pt-12 pb-6 bg-primary relative;
            &::after{
              content:'';
              @apply block w-full h-16 absolute left-0 top-full bg-gradient-to-b from-gradient-primary-start to-gradient-primary-end;
            }
          }
          .cover__btn{
            @apply w-44;
          }
        }
      }
    }
  }
  // 活动卡片
  .activity-card{
    @apply w-full px-2 xl:px-8;
    .card__inner{
      max-width:340px;
      @apply w-full bg-white border border-line-light_3;
    }
    .card__img{
      @apply w-full aspect-w-380 aspect-h-256 object-cover;
    }
    .card__content{
      @apply flex-1 text-center p-8;
    }
    .card__title{
      @apply text-lg lg:text-xl xl:text-2xl mb-6;
    }
    .card__des{
      @apply text-xs lg:text-sm text-text-light line-clamp-3;
      line-height:1.85;
    }
    .card__ft{
      @apply text-center pb-8;
    }
    .card__btn{
      @apply block underline underline-offset-4 text-xs md:text-sm lg:text-base;
    }
    // &.swiper-slide-active,
    &:hover{
      @apply md:scale-110;
      .card__ft{
        @apply pb-0;
      }
      .card__btn{
        @apply bg-brand py-3 md:py-5 text-black hover:bg-brand-dark leading-none no-underline;
      }
    }
  }
  // 酒店卡片
  .hotel-card{
    .card__inner{
      padding:0 5px;
    }
    .hotel__cell{
      max-width:800px;
      @apply block cursor-pointer relative;
      .cell__img{
        @apply w-full aspect-w-650 aspect-h-550 object-cover;
        &.__style1{
          @apply w-full aspect-w-650 aspect-h-270;
        }
        &.__style2{
          @apply aspect-w-320 aspect-h-270;
        }
      }
      .cell__cover{
        @apply w-full h-full absolute left-0 bottom-0 z-10 p-1 md:p-5 text-white bg-black bg-opacity-30;
      }
      .cover__inner{
        @apply w-full h-full p-2 md:p-4 border border-white border-opacity-0 flex flex-col items-center md:items-start justify-center md:justify-end;
      }
      .cell__title{
        @apply font-semibold text-sm md:text-base lg:text-lg xl:text-xl 2xl:text-2xl md:mb-2 text-center;
      }
      .more_btn{
        @apply text-xs md:text-sm lg:text-base underline underline-offset-4;
      }
      @media (min-width: 768px){
        &:hover{
          .cover__inner{
            @apply border-opacity-100;
          }
        }
      }
    }
    &.__theme2{
      .card__inner{
        @apply grid grid-cols-2 gap-2.5;
        .hotel__cell{
          &:nth-child(1){
            @apply col-span-2;
          }
        }
      }
    }
    &.__theme3{
      .card__inner{
        @apply grid grid-cols-2 gap-2.5;
        .hotel__cell{
          &:nth-child(3){
            @apply col-span-2;
          }
        }
      }
    }
  }
}

