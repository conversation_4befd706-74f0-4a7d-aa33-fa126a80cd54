.slh_layout{
  width:100%;
  overflow-x: hidden;
  $asideSize: 340px;
  .layout_aside{
    width:$asideSize;
    max-height:100vh;
    position: absolute;
    left: -$asideSize;
    transition: transform 0.3s ease;
    .menu_item,
    .sub_menu_item{
      a{
        display:block;
        cursor: pointer;
        color: #282828;
        line-height:1;
        &:hover{
          text-decoration:none;
          background-color: #f7f0ea;
        }
      }
    }
    .menu_item{
      a{
        font-size:18px;
        padding:1em 70px;
      }
    }
    .sub_menu_item{
      a{
        font-size: 16px;
        padding:1em 70px; 
      }
      .slogan{
        line-height:1.25;
        font-size:14px;
        .shl_button{
          letter-spacing:5px;
        }
      }
    }
  }
  .layout_main{
    width:100vw;
    min-height:100vh;
    position:relative;
    transition: transform 0.3s ease;
    &::after{
      content:'';
      position:absolute;
      top:0;
      left:0;
      right:0;
      bottom:0;
      background-color:#282828;
      opacity:0;
      // transition: opacity 0.3s ease;
      z-index:-1;
    }
  }
  .layout_content{
    @apply flex-1;
  }
  &.aside_open{
    overflow: hidden;
    .layout_aside{
      transform:translate($asideSize, 0);
    }
    .layout_main{
      height:100vh;
      overflow: hidden;
      transform:translate($asideSize, 0);
      &::after{
        z-index:20;
        opacity:0.5;
      }
    }
  }
  @media (min-width: var(--slh-screen-md)){
    $asideSize: 85%;
    .layout_aside{
      width:$asideSize;
    }
    &.aside_open{
      .layout_aside{
        transform:translate($asideSize, 0);
      }
      .layout_main{
        transform:translate($asideSize, 0);
      }
    }
  }
  // 页面头部
  .slh_header{
    @apply pt-3.5 md:pt-5 w-full absolute top-0 right-0 z-30 text-white;
    .hd__container{
      @apply container xl:max-w-300 mx-auto px-3 md:px-0 flex items-start relative;
    }
    .hd__left{
      @apply flex-1 space-x-4;
    }
    .hd__logo{
      @apply block w-16 md:w-25;
      .white__logo{
        @apply block;
      }
      .black__logo{
        @apply hidden;
      }
    }
    .hd__right{
      @apply flex-1 flex justify-end;
    }
    .menu__btn{
      @apply cursor-pointer border-none leading-none;
      .iconfont{
        @apply leading-none text-xl md:text-3xl
      }
      .btn__text{
        @apply  hidden md:block text-sm leading-none;
      }
    }
    // 手机右侧导航
    .mobile__nav{
      @apply md:hidden;
      .user__btn{
        @apply cursor-pointer border-none leading-4;
        .iconfont{
          @apply leading-4 text-xl md:text-3xl;
        }
      }
    }
    // pc 右侧导航
    .pc__nav{
      @apply hidden md:block space-y-2 md:space-y-3;
      .nav__row{
        @apply flex items-center justify-end space-x-2 divide-x divide-line-light;
      }
    }
    &.dark{
      // 黑色主题
      @apply text-black;
      .hd__logo{
        @apply block w-12.5 md:w-25;
        .white__logo{
          @apply hidden;
        }
        .black__logo{
          @apply block;
        }
      }
    }
    &.hide__search{
      #slh_hd_searchbtn{
        @apply hidden;
      }
    }
    .hd__searchbar{
      @apply w-full px-2 md:px-0 md:w-[768px] lg:w-[1024px] xl:w-[1200px] absolute top-[30px] md:top-[50px] left-0 hidden;
      &.__show{
        @apply block;
      }
    }
  }
}

