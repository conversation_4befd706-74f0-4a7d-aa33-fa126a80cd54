/* 引入tailwindcss */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

// 使用 tailwindcss 自定义组件类
@layer components {
  .slh-font-sunti{
    font-family: 'Source Han Serif', '思源宋体', '思源宋体 VF', Sim<PERSON>un, '宋体', sans-serif, 'PingFang SC';
  }
  .slh-font-heiti{
    font-family: 'Source Han Sans', '思源黑体', '思源黑体 VF', <PERSON><PERSON><PERSON><PERSON>, '黑体', Microsoft YaHei, '微软雅黑', sans-serif, 'PingFang SC';
  }
  // 固定容器
  .slh-container{
    @apply container xl:max-w-300 mx-auto px-4 md:px-0;
  }
  // 普通按钮
  .slh-btn{
    @apply cursor-pointer h-[34px] px-2 lg:px-4 text-sm lg:text-base rounded inline-flex items-center justify-center hover:no-underline border border-line text-text-light;
    &.primary{
      @apply  text-white bg-primary border-primary hover:bg-primary-dark;
    }
    &.brand{
      @apply  text-black bg-brand border-brand hover:bg-brand-dark;
    }
  }
  // 按钮组
  .slh-btn-group{

  } 
  // 默认按钮
  .slh-btn-default{
    @apply cursor-pointer h-[40px] lg:h-[50px] px-2 lg:px-3 lg:text-base border-none inline-flex items-center justify-center text-black hover:no-underline bg-bg_gary-light_3 hover:bg-bg_gary-dark_3;
  }
  // 品牌按钮
  .slh-btn-brand{
    @apply cursor-pointer h-[40px] lg:h-[50px] px-2 lg:px-3 text-sm lg:text-base border-none inline-flex items-center justify-center text-black hover:no-underline bg-brand hover:bg-brand-dark;
  }
  .slh-btn-white{
    @apply cursor-pointer h-[40px] px-2 lg:px-4 text-sm lg:text-base border border-white inline-flex items-center justify-center text-black hover:no-underline bg-white hover:bg-gray-100;
    &[plain]{
      @apply bg-black bg-opacity-10 text-white hover:bg-opacity-20;
    }
  }
  .slh-input{
    @apply w-full text-sm h-[40px] lg:h-[50px] px-4 py-3 rounded-none border border-line focus:outline-none focus:rounded-none focus:border-primary text-black placeholder:text-text-light;
  }
  .slh-label{
    @apply text-sm text-text-light;
  }
  .slh-form-item{
    @apply w-full space-y-2 flex flex-col;
    &[required]{
      .slh-label{
        @apply after:content-['*'] after:ml-0.5 after:text-error;
      }
    }
  }
  // 搜索框
  .slh-searchbar-item{
    @apply py-2.5 h-full flex flex-col justify-center hover:bg-white relative;
  }
  .slh-searchbar-cell{
    @apply px-3 lg:px-5 xl:px-7 relative h-full flex flex-col justify-center;
  }
  .slh-searchbar-lable{
    @apply text-text leading-none text-xs lg:text-sm;
  }
  .slh-searchbar-value{
    @apply text-text text-sm lg:text-base pt-1 lg:pt-2 leading-none;
  }
  .slh-searchbar-input{
    @apply text-black text-sm lg:text-base rounded-none py-1 leading-none border-none focus:border-none focus:outline-none bg-transparent;
  }
  // 详情侧边栏查看
  .slh-detail-sidebar{
    line-height:1.15;
    @apply hidden md:flex w-[2em] fixed bottom-20 right-10 px-2 py-3 z-20 flex-col items-center justify-center text-black no-underline text-sm bg-brand hover:bg-brand-dark rounded translate-x-full;
  }
  // 订单侧边栏编辑按钮
  .slh-edit-btn{
    @apply w-6 lg:w-6.5 h-6 lg:h-6.5 inline-flex items-center justify-center border-none bg-bg_gary-light_3 hover:bg-white rounded-full hover:text-primary;
  }
  .slh-section{
    @apply mb-10 md:mb-16 lg:mb-20 xl:mb-24;
  }
  // 区域头部
  .slh-section-hd{
    @apply flex flex-col md:flex-row items-center justify-center md:justify-start mb-5 md:mb-12;
  }
  // 区域标题
  .slh-section-title{
    @apply slh-font-sunti font-semibold tracking-widest text-lg md:text-3xl lg:text-3xl xl:text-4xl text-center md:text-left;
    &.has__line{
      &::after{
        content:'';
        width:1.5em;
        @apply block border-t border-primary mx-auto my-3 md:my-4 lg:my-5 xl:my-6;
      }
      &.line__stlye2{
        @apply text-left;
        &::after{
          border-color:#cccccc;
          @apply ml-0 my-4 md:my-5 lg:my-6 xl:my-8;
        }
      }
    }
  }
  // 文字两端对齐
  .slh-justify{
    text-align-last: justify;
    @apply text-justify;
  }
  // 段落文字中有br
  .slh-newline{
    br{
      @apply hidden md:block;
    }
  }
  // 详情页内容
  .slh-detail-content{
    line-height:1.5;
    @apply text-text text-sm lg:text-base text-justify;
    a{
      @apply text-primary;
      &:hover{
        @apply underline underline-offset-4;
      }
    }
    p{
      +p{
        margin-top: 1rem;
      }
      img, svg, video, canvas, audio, iframe, embed, object{
        @apply inline-block;
      }
    }
  }

  // 酒店导航
  .slh-navs{
    @apply w-full sticky top-0 left-0 z-10 overflow-x-scroll md:overflow-x-hidden bg-white;
    .navs__inner{
      @apply flex items-center md:justify-center w-[120%] md:w-full;
    }
    .nav__item{
      @apply md:flex-none text-sm md:text-base p-2 md:py-4 px-3 md:px-4 lg:px-5 xl:px-6 cursor-pointer text-text-light  border-b-2 border-b-transparent flex items-center justify-center 2xl:justify-end break-all;
      &.current{
        @apply text-primary border-b-primary;
      }
    }
  }

  // app中的加载中
  .app-loading{
    min-height:400px;
    @apply flex items-center justify-center;
    .loading-img{
      @apply max-w-[40px];
    }
  }

  // 比例媒体中的元素
  .aspect-ele{
    @apply object-cover;
  }

  /* 地图上标记点图标样式 */
  .slh-marker{
    width: 30px !important;
    overflow:hidden;
    // padding:12px 3px 0;
    position: relative;
    img{
      width:100%;
    }
    @apply cursor-pointer object-cover;
    // &.is-active{
    //   padding:0;
    // }
  }

  // 图标为加载出来时设置一个默认背景
  .slh-empty-img{
    background:url('@/assets/imgs/hotel_img_loading.jpg') no-repeat center center;
    background-size:cover;
  }

  // 页面标题
  .slh-page-title{
    margin-bottom:1em;
    @apply text-xl md:text-2xl xl:text-3xl text-primary text-center font-semibold;
    span{
      @apply inline-block;
      &::after{
        content:'';
        width:2em;
        margin-top:0.5em;
        @apply block border-gray-200 border-b mx-auto;
      } 
    }
    &.text-white{
      @apply border-white;
    }
  }
}

html,body{
  @apply slh-font-heiti;
}
