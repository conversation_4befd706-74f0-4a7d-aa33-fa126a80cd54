// 文章列表和文章详情页面
.page-article{
  .page-container{
    @apply -mt-10 md:-mt-14 lg:-mt-15 relative z-10 pb-8 md:pb-10 lg:pb-12 xl:pb-14;
    .container__inner{
      @apply bg-white border border-line-light_3;
      .inner__box{
        @apply pb-4 md:pb-5 lg:pb-6 xl:pb-8 px-4 lg:px-8 space-y-3 md:space-y-4 lg:space-y-5 xl:space-y-6;
      }
    }
  }
  // 页面介绍
  .page__introduce{
    @apply text-center pt-4 md:pt-6 lg:pt-8 xl:pt-10 pb-1 md:pb-2 lg:pb-3 xl:pb-4;
    .page__desc{
      @apply text-sm text-text-light leading-6;
    }
  }
  // 文章列表
  .article__list{
    @apply grid grid-cols-1 md:grid-cols-2 gap-5 md:gap-6 lg:gap-7;
    .article__item{
      @apply border border-gray-200;
    }
    .article__img{
      @apply w-full object-cover aspect-w-525 aspect-h-210;
    }
    .article__info{
      @apply space-y-1.5 md:space-y-2 lg:space-y-3 py-3 md:py-4 lg:py-6 px-4 md:px-6 lg:px-8;
    }
    .article__title{
      @apply text-base md:text-lg xl:text-xl text-black line-clamp-2 font-semibold;
    }
    .article__desc{
      @apply text-text-light text-sm leading-6 line-clamp-2;
    }
  }
  // 联系方式
  .contact__ways{
    @apply md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-5 lg:gap-7 xl:gap-8;
    &.style__2{
      @apply md:grid-cols-6;
    }
    .way__item{
      @apply p-3 md:p-4 lg:p-5 bg-bg_gary-light;
    }
    .item__inner{
      @apply p-3 md:p-4 lg:p-5 bg-white h-full;
    }
    .way__title{
      @apply text-lg md:text-xl lg:text-2xl text-primary mb-3 md:mb-4 lg:mb-5;
    }
    .way__content{
      @apply text-sm text-text-light leading-6 text-left;
    }
    .info__row{
      @apply flex;
      .info__lable{
        @apply flex-shrink-0;
      }
      .info__content{
        @apply flex-1;
      }
    }
  }
  // 详情页主要信息
  .article__main{
    @apply pb-6 md:pb-10 xl:pb-15;
  }
  //关于我们
  .page__aboutus{
    // 我们是谁
    .section_who_we_are{
      @apply md:w-[850px] mx-auto pb-16 md:pb-20 xl:pb-32;
    }
    .our__features{
      @apply grid grid-cols-2 gap-2 md:gap-4 mb-5 md:mb-7;
      .feature__item{
        background-size: auto 70%;
        background-image: url('@/assets/imgs/maisui.png');
        @apply font-semibold text-xs md:text-sm xl:text-xl py-4 md:py-5 xl:py-7 text-center text-primary bg-no-repeat bg-center bg-[#fcf7f4];
      }
    }
    .our_honor{
      @apply text-center text-black text-sm md:text-base;
      line-height:2;
    }
    .our_introduce{
      @apply py-4 md:py-6 mt-4 md:mt-6 space-y-4 text-xs md:text-sm text-[#666] text-center border-t border-gray-100;
      line-height:2;
    }
    // 我们的方式
    .section_our_approach{
      background-image: url('@/assets/imgs/about_us/fangshi.png');
      @apply  text-sm md:text-base py-10 md:py-16 xl:py-20 text-center text-white bg-cover bg-no-repeat bg-center bg-[#fcf7f4];
      line-height:2;
    }
    // 我们的旗下酒店
    .section_our_hotels{
      @apply pb-12 md:pb-20 xl:pb-28 pt-10 md:pt-16 xl:py-20;
      .hotels_features{
        @apply flex  text-xs md:text-sm text-center text-[#666] space-x-1 pt-5 md:pt-10;
        .feature__item{
          @apply flex-1 bg-gradient-to-r  from-[#fafafa] to-[#f6f6f6] p-6 md:p-12 pt-8 md:pt-16 relative;
        }
        .feature__title{
          @apply text-sm md:text-base text-black mb-4 md:mb-5;
        }
        .feature__icon{
          @apply w-10 h-10 md:w-15 md:h-15 text-xl md:text-4xl flex items-center justify-center bg-primary text-white rounded-full absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2;
        }
      }
    }
    // 我们的方式
    .section_our_customers{
      background-image: url('@/assets/imgs/about_us/kehu.png');
      @apply  text-sm md:text-base py-10 md:py-16 xl:py-20 text-center text-white bg-cover bg-no-repeat bg-center bg-[#fcf7f4];
      line-height:2;
    }
  }
  // 业主故事
  .page__stories{
    .page__introduce{
      @apply pt-0 w-full md:w-[866px] mx-auto;
    }
    .story__list{
      @apply grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4 lg:gap-7;
      .article__item{
        @apply flex flex-col border border-gray-200;
      }
      .article__info{
        @apply flex-1 flex flex-col justify-between;
      }
      .article__img{
        @apply w-full object-cover aspect-w-380 aspect-h-255;
      }
    }
  }
  // 联系我们
  .page__contactus{
    .contactus__ways{
      @apply grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 lg:gap-8 text-xs md:text-sm text-[#666];
      line-height:2;
      .way__item{
        @apply p-4 md:p-6 lg:p-8 bg-[#fcf7f4] rounded-[30px] md:rounded-[40px] lg:rounded-[60px] overflow-hidden rounded-tr-none rounded-bl-none  relative;
        .bg__icon{
          @apply absolute bottom-0 right-0 text-[80px] md:text-[120px] lg:text-[156px] text-white opacity-50 translate-y-2;
          line-height:1;
        }
        .item__inner{
          @apply relative z-10 space-y-4 md:space-y-5 lg:space-y-6;
        }
        .way__title{
          @apply text-base md:text-xl lg:text-2xl text-primary font-semibold flex items-center space-x-3 md:space-x-4 lg:space-x-5;
          line-height:1;
          em{
            font-style: normal;
          }
        }
        .title__icon{
          @apply w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 text-base md:text-xl lg:text-2xl rounded-full bg-primary text-white flex items-center justify-center;
        }
        a{
          @apply text-primary px-1 font-semibold;
        }
        &:nth-child(2n+2){
          @apply bg-[#f7f7f7] md:bg-[#fcf7f4];
        }
        &:nth-child(4n+2),
        &:nth-child(4n+3){
          @apply md:bg-[#f7f7f7];
        }
      }
    }
  }
}