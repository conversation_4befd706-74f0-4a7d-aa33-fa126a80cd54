.page-hotel-detail{
  .detail__section{
    @apply pt-8 md:pt-12 lg:pt-14 xl:pt-16;
  }
  .section__hd{
    @apply flex mb-4 md:mb-6 lg:mb-8 xl:mb-10 text-center md:text-left;
    .section__title{
      @apply tracking-widest flex-1 text-lg md:text-xl lg:text-2xl xl:text-3xl text-text;
    }
  }
  .section__en__title{
    font-family: 'Stay Classy';
    @apply text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-primary leading-none;
  }
  .com-page-header{
    .header__cover{
      .cover__subtitle{
        font-family: Avenir, sans-serif;
        text-transform: uppercase;
      }
    }
  }
  // 预订框
  .book__box{
    @apply w-full md:w-[304px] lg:w-[360px] md:absolute top-32 md:left-1/2 z-20 md:translate-x-20 lg:translate-x-38 xl:translate-x-60;
    .open__btn{
      @apply hidden w-full bg-white bg-opacity-90 p-5 py-2 cursor-pointer;
    }
    &.is__sticky{
      @apply fixed top-0 z-20;
      .open__btn{
        @apply md:block;
      }
      .box__main{
        @apply md:hidden;
      }
    }
    &.is__open{
      @apply shadow-lg;
      .open__btn{
        @apply hidden;
      }
      .box__main{
        @apply block;
      }
    }
  }
  // 设施列表
  .facility__list{
    @apply grid grid-cols-2 md:grid-cols-3 gap-2.5 md:gap-4;
    .list__item{
      @apply inline-flex items-center space-x-2 text-sm text-black;
    }
    .item__tip{
      @apply w-4 h-4 border border-black leading-none mr-2 flex items-center justify-center;
    }
    .tip__icon{
      @apply leading-none text-xs font-bold;
    }
  }
  .hotel-col-swiper{
    @apply pb-3 md:pb-10 items-stretch;
    .swiper-wrapper{
      align-content: stretch;
    }
    --swiper-navigation-color: #fff;
    --swiper-navigation-size: 24px;
    --swiper-pagination-bullet-inactive-opacity:0.4;
    --swiper-pagination-bullet-inactive-color:#fff;
    .swiper-button-next,
    .swiper-button-prev{
      margin-top:-14px;
      @apply hidden md:block w-7 h-7;
    }
    .swiper-button-next{
      @apply right-2.5;
    }
    .swiper-button-prev{
      @apply left-2.5;
    }
    .swiper-pagination{
      @apply bottom-8 md:bottom-16;
      .swiper-pagination-bullet{
        margin:0 3px;
        width:6px;
        height:6px;
        @apply bg-white opacity-80;
        &-active{
          width:9px;
          height:9px;
          @apply opacity-100;
        }
      }
    }
  }
  .hotel-col-swiper-btns{
    @apply flex;
    .swiper__btn{
      @apply cursor-pointer flex items-center justify-center w-[28px] h-[28px] md:w-[36px] md:h-[36px] bg-primary text-white hover:bg-primary-dark;
      .btn__icon{
        @apply leading-none text-base;
      }
      &.swiper-button-disabled{
        @apply bg-black bg-opacity-10 cursor-not-allowed;
      }
    }
  }
  //酒店房型
  .room__card{
    max-width:571px;
    @apply w-full h-auto flex flex-col;
    .room__img{
      @apply block w-full object-cover aspect-w-480 aspect-h-275;
    }
    .imgs__icon{
      @apply inline-block absolute top-3 right-3 z-10 text-white leading-none text-xl;
    }
    .room__info{
      @apply bg-white p-2.5 lg:p-5 z-10 relative text-center border border-gray-200 flex-1;
      .room__title{
        @apply text-base md:text-xl lg:text-2xl xl:text-3xl text-black mb-2 md:mb-3 lg:mb-4 xl:mb-5;
      }
      .room__features__box{
        @apply flex justify-center;
      }
      .room__features{
        @apply grid grid-cols-1 md:grid-cols-2 gap-1 md:gap-2 lg:gap-3 xl:gap-4 text-sm text-text-light;
      }
      .feature__icon{
        @apply text-lg;
      }
      .room__desc{
        @apply text-text-light text-xs md:text-sm leading-5 md:leading-6 text-left mb-4;
      }
    }
  }
  // 酒店画廊
  .hotel__gallery{
    .gallery__main__img{
      @apply w-full object-cover aspect-w-2 aspect-h-1  md:aspect-w-1200 md:aspect-h-480;
    }
    .gallery__thumb__img{
      @apply w-full object-cover aspect-w-130 aspect-h-100;
    }
    .mainSwiper{
      @apply relative;
      .swiper-slide{
        @apply relative block cursor-pointer;
        .iconfont{
          @apply absolute bottom-0 right-0 bg-white md:bg-transparent text-primary md:text-white md:bottom-4 md:right-4 leading-none py-2.5 md:py-0 px-3 md:px-0 text-base md:text-xl;
        }
      }
      .swiper-controllers{
        @apply absolute right-9 md:right-1/2 bottom-0 bg-white py-2.5 px-3 md:py-2.5 md:px-3 flex items-center z-10 md:translate-x-1/2 border-r md:border-r-0 border-line-light_2;
        .swiper-pagination{
          position: static;
          @apply text-black px-4 text-xs md:text-sm;
        }
        .swiper-button-prev,
        .swiper-button-next{
          position: static;
          @apply w-auto h-auto mt-0 leading-none;
          .iconfont{
            @apply text-primary text-base md:text-lg leading-none;
          }
          &::after{
            @apply hidden;
          }
        }
      }
    }
    .thumbSwiper{
      @apply pt-2 md:pt-4;
      .swiper-slide{
        @apply cursor-pointer w-[74px] md:w-[138px] border-3 md:border-4 border-transparent;
        &.swiper-slide-thumb-active{
          @apply border-primary;
        }
      }
    }

  }
  .actively-col-swiper{
    @apply pb-5;
  }
  // 可持续发展卡片
  .actively__card{
    max-width:340px;
    @apply shadow;
    .card__img{
      @apply w-full object-cover  aspect-w-1 aspect-h-1;
    }
    .card__info{
      @apply bg-white px-3 md:px-5 pt-3 md:pt-5 pb-10;
    }
    .card__title{
      @apply  text-xl lg:text-2xl text-black mb-2 lg:mb-4 text-center;
    }
    .card__desc{
      @apply text-sm text-text-light;
      line-height:1.85;
    }
  }
  // 酒店基本信息
  .hotel__info{
    @apply p-2.5 md:p-3 lg:p-4 xl:p-5 border border-bg_clube-dark;
    .info__inner{
      @apply px-4 md:px-0 py-5 md:py-8 lg:py-10 xl:py-14 flex flex-col md:flex-row border border-bg_clube-dark divide-y md:divide-y-0 md:divide-x;
    }
    .hotle_des{
      @apply  p-2.5 md:p-5 bg-bg_clube-dark text-sm md:text-base mb-4;
    }
    // 酒店品质
    .hotle_guelities{
      @apply pl-5 mb-5 text-sm text-primary;
      ul{
        @apply list-disc space-y-2;
      }
    }
    // 内容详情
    .detail__content{
      @apply flex-1 pl-0 md:pl-8 lg:pl-10 xl:pl-14 pr-0 md:pr-10 lg:pr-16 xl:pr-20 pb-12;
    }
    // 详情其他信息
    .detail__info{
      @apply px-0 md:px-6 lg:px-7 xl:px-8 pt-6 md:pt-0 flex-shrink-0 w-auto md:w-[300px] lg:w-[360px] flex flex-col;
    }
    .hotel__msgs{
      @apply grid grid-cols-3 gap-1.5 md:gap-3 text-xs md:text-sm text-black;
      .msg__item{
        @apply bg-bg_clube-dark border border-bg_clube-dark space-x-1 md:space-x-0 xl:space-x-2 p-1 flex items-center justify-center leading-none;
        .iconfont{
          @apply leading-none md:hidden xl:inline-block;
        }
      }
    }
    .hotel__features{
      @apply pt-4 md:pt-5 xl:pt-6;
      @apply grid grid-cols-3 md:grid-cols-2 gap-2.5 md:gap-5 text-sm text-black;
      .feature__item{
        @apply flex flex-col items-center;
      }
      .feature__img{
        @apply w-full object-contain aspect-w-122 aspect-h-75 max-w-[60px] md:max-w-[80px] xl:max-w-[100px];
        .aspect-ele{
          @apply object-contain;
        }
      }
      .feature__name{
        @apply pt-2 m-0 text-center;
      }
    }
  }
  // 酒店导航
  .hotel__navs{
    @apply 2xl:absolute top-0 2xl:top-16 left-0 xl:pr-7 2xl:-translate-x-full z-10 overflow-x-scroll md:overflow-x-hidden;
    .navs__inner{
      @apply bg-white flex 2xl:flex-col w-[180%] md:w-full 2xl:w-[142px] 2xl:shadow;
    }
    .nav__item{
      @apply flex-1 md:flex-none p-3 md:py-5 md:px-3 lg:px-4 2xl:px-2 cursor-pointer text-text-light border-t border-line-light_3 border-b border-b-transparent flex items-center justify-center 2xl:justify-end break-all;
      &::after{
        content:'';
        @apply invisible hidden 2xl:block w-0 h-0 border-6 border-r-0 border-transparent border-l-primary ml-2;
      }
      &:last-child{
        @apply border-b-line-light_3;
      }
      &.current{
        @apply text-primary border-b-primary;
        &::after{
          @apply 2xl:visible;
        }
      }
    }
  }
  // 位置信息
  .hotel__location{
    @apply flex flex-col md:flex-row bg-primary bg-opacity-10 items-stretch;
    .location__map{
      @apply w-full md:w-auto md:flex-1 min-h-[300px] md:min-h-[400px];
    }
    .location__info{
      @apply w-full md:w-[300px] lg:w-[360px] xl:w-[400px] bg-white border border-line;
      .info__inner{
        @apply min-h-full md:max-h-[400px] overflow-y-hidden px-4 md:px-8 lg:px-10 xl:px-12.5 py-6 md:py-10 lg:py-12 xl:py-14 space-y-4 md:space-y-6 xl:space-y-8;
        &.is_scroll{
          @apply overflow-y-scroll;
        }
      }
      .info__title{
        @apply text-lg lg:text-xl xl:text-2xl text-black mb-2 md:mb-3 lg:mb-4 xl:mb-6;
      }
      .info__cells{
        @apply space-y-1 md:space-y-2 text-text-light text-sm leading-6;
        .info__cell{
          @apply flex space-x-1 md:space-x-2 items-stretch;
          .cell__lable{
            @apply text-right;
          }
          .cell__info{
            @apply flex-1;
          }
        }
      }
    }
  }
  // 酒店紧急通知
  .hotel_emergency_notice{
    line-height: 1.5em;
    @apply text-center p-2 md:p-5 mb-2 md:mb-4 text-base text-black font-medium w-full md:w-7/12 mx-auto;
    p{
      margin-bottom: 1em;
    }
    hr{
      @apply border-none;
      margin-bottom: 1em;
    }
  }
}
