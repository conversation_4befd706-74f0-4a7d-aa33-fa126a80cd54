<template>
  <div class="slh_register w-full h-full pt-[100px] md:pt-[150px] lg:pt-[200px] pb-6 md:pb-8 lg:pb-11 bg-cover bg-center-top bg-no-repeat bg-[url('@/assets/imgs/login_bg_m.png')] md:bg-[url('@/assets/imgs/login_bg_pc.png')]">
    <div class="w-full md:w-187.5 md:bg-white md:py-12.5 md:px-17.5 mx-auto">
      <h1 class="text-xl  md:2xl lg:2.2xl mb-6 md:mb-10 text-white md:text-black text-center">{{$t('register.title')}}</h1>
      <div class="w-full"><register-steps :active="step"/></div>
      <div class="w-full p-6 md:px-0">
        <div class="px-5 py-4 md:px-0 md:py-0 bg-white">
          <el-form
            ref="step1FormRef"
            :model="step1Form"
            :rules="step1Rules"
            label-width="auto"
            label-position="top"
            require-asterisk-position="right"
            :size="elementPlusSize"
            v-show="step === 0"
          >
            <el-form-item
              :label="$t('common.phone.label')"
              prop="phone"
            >
              <div class="w-full flex space-x-3 md:space-x-5">
                <el-select v-model="step1Form.phoneRefix" class="w-25 md:w-45">
                  <el-option
                    v-for="item in COUNTRIES"
                    :key="item.name"
                    :label="item.code"
                    :value="item.code"
                  />
                </el-select>
                <div class="flex-1">
                  <el-input
                    v-model="step1Form.phone"
                    :placeholder="$t('common.phone.placeholder')"
                  />
                </div>
              </div>
            </el-form-item>
            <el-form-item
              :label="$t('common.verify_code.label')"
              prop="verifyCode"
            >
              <div class="flex w-full space-x-3 md:space-x-5">
                <div class="flex-1">
                  <el-input
                    v-model.number="step1Form.verifyCode"
                    :placeholder="$t('common.verify_code.placeholder')"
                  />
                </div>
                <el-button
                  type="primary"
                  :size="elementPlusSize"
                  :loading="sendCodeing"
                  :disabled="sendCodeing || isActive"
                  @click="sendCode"
                >
                  {{
                    isActive ?
                    `${coolingTime}${$t('common.verify_code.retry')}` : 
                      sendCodeed ?
                      $t('common.verify_code.resend_code') :
                      $t('common.verify_code.send_code')
                  }}
                </el-button>
              </div>
            </el-form-item>
            <el-form-item>
              <SlhButton
                class="w-full"
                @click="submitStep1"
                type="brand"
                :loading="submitLoading"
                :disabled="submitLoading"
              >
                {{ $t('register.next_step') }}
              </SlhButton>
            </el-form-item>
          </el-form>
          <el-form
            ref="step2FormRef"
            :model="step2Form"
            :rules="step2Rules"
            label-width="auto"
            label-position="top"
            require-asterisk-position="right"
            :size="elementPlusSize"
            v-show="step === 1"
          >
            <div class="grid gap-3 grid-cols-2 mb-4">
              <div>
                <el-form-item
                  class="relative z-10"
                  :label="$t('common.last_name.label')"
                  prop="FamilyName"
                >
                  <el-input
                    v-model="step2Form.FamilyName"
                    :placeholder="$t('common.last_name.placeholder')"
                    @change="(value) => nameChange('FamilyName', value)"
                  />
                </el-form-item>
                <div class="-mt-2"><el-input v-model="step2Form.FamilyNameEn" disabled/></div>
              </div>
              <div>
                <el-form-item
                  class="relative z-10"
                  :label="$t('common.first_name.label')"
                  prop="FirstName"
                >
                  <el-input
                    v-model="step2Form.FirstName"
                    :placeholder="$t('common.first_name.placeholder')"
                    @change="(value) => nameChange('FirstName', value)"
                  />
                </el-form-item>
                <div class="-mt-2"><el-input v-model="step2Form.FirstNameEn" disabled/></div>
              </div>
            </div>
            <el-form-item
              :label="$t('common.email.label')"
              prop="email"
            >
              <el-input
                v-model="step2Form.email"
                :placeholder="$t('common.email.placeholder_regiter')"
              />
            </el-form-item>
            <el-form-item 
              :label="$t('register.birthday.label')"
              prop="birthday"
            >
              <el-date-picker
                :style="{width:'100%'}"
                v-model="step2Form.birthday"
                type="date"
                :placeholder="$t('register.birthday.placeholder')"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              :label="$t('register.country.label')"
              prop="country"
            >
              <el-select
                class="w-full"
                v-model="step2Form.country"
                :placeholder="$t('register.country.placeholder')"
              >
                <el-option
                  v-for="item in COUNTRIES"
                  :key="item.name"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              :label="$t('common.password.label')"
              prop="password"
            >
              <el-input
                type="password" 
                v-model="step2Form.password"
                :placeholder="$t('common.password.placeholder')"
                @change="passwordChange"
              />
            </el-form-item>
            <el-form-item
              :label="$t('common.confirm_password.label')"
              prop="confirmPassword"
            >
              <el-input
                type="password"
                v-model="step2Form.confirmPassword"
                :placeholder="$t('common.confirm_password.placeholder')"
              />
            </el-form-item>
            <el-form-item>
              <p class="text-black text-xs md:text-sm text-center inline-flex items-center space-x-1">
                <el-checkbox v-model="agreeTerms">{{ $t('register.agree_tip') }}</el-checkbox>
                <span class="text-primary cursor-pointer underline underline-offset-4">{{ $t('register.terms') }}</span>
              </p>
            </el-form-item>
            <el-form-item>
              <SlhButton
                class="w-full"
                @click="submitStep2"
                type="brand"
                :loading="submit2Loading"
                :disabled="submit2Loading"
              >
                {{ $t('register.register_now') }}
              </SlhButton>
            </el-form-item>
          </el-form>
          <p class="text-black pt-2 text-xs md:text-sm text-center" v-show="[0, 1].includes(step)">
            {{ $t('register.have_account') }},
            <a :href="`${rootPath}/auth/login`" class="text-primary cursor-pointer underline underline-offset-4">{{ $t('register.to_login') }}</a>
          </p>
          <div class="space-y-4" v-show="step === 2">
            <div class="py-4 flex flex-col justify-center text-center">
              <i class="iconfont icon-copy text-6xl lg:text-7xl leading-none text-success"></i>
              <h3 class="text-xl lg:text-2xl text-black my-4">{{ $t('register.register_success') }}</h3>
              <div class="text-sm">
                <p class="text-text">{{ $t('register.send_success') }}</p>
                <p class="text-primary"><EMAIL></p>
              </div>
            </div>
            <p class="text-black pt-5 md:pt-8 xl:pt-10 lg:pt-12.5 text-xs md:text-sm text-center">
              <span class="text-primary">{{ jumpCountdown }}</span> {{ $t('common.after_second') }},
              <a href="#" class="text-black cursor-pointer underline underline-offset-4">{{ $t('common.back_prev') }}</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import pinyin from 'pinyin'
import { useI18n } from 'vue-i18n'
import { useTimeoutFn, useIntervalFn } from '@vueuse/core'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, computed, } from 'vue'
import { COUNTRIES, COOLING_TIME, JUMP_COUNTDOWN } from '@/config/index'
import useAppStore from '@/stores/app'

const { t } = useI18n()
const { elementPlusSize, rootPath } = useAppStore()
const step = ref(0)
const step1FormRef = ref(null);
const coolingTime = ref(COOLING_TIME);
const sendCodeing = ref(false);
const sendCodeed = ref(false);
const submitLoading = ref(false);

const step1Form = reactive({
  phoneRefix:COUNTRIES[0].code,
  phone:'',
  verifyCode:'',
})

const step1Rules = {
  phone: [
    {required: true, message: t('common.phone.required'), trigger: ['blur', 'change'],},
    {pattern: /^(?:(?:\+|00)86)?1\d{10}$/, message: t('common.phone.error'), trigger: ['blur', 'change']},
  ],
  verifyCode: [
    {required: true, message: t('common.verify_code.required'), trigger: ['blur', 'change'],},
    { type: 'number', message: t('common.verify_code.error') },
  ],
}

// 发动验证码计时器
const { pause, resume, isActive } = useIntervalFn(() => {
  coolingTime.value = coolingTime.value - 1
  if(coolingTime.value <= 0){
    pause()
    coolingTime.value = COOLING_TIME
    console.log('倒计时结束')
  }
}, 1000)
pause()

const sendCode = () => {
  step1FormRef.value.validateField('phone', (valid) => {
    if(valid){
      sendCodeing.value = true
      useTimeoutFn(() => {
        sendCodeing.value = false
        sendCodeed.value = true
        resume()
      }, 2000)
    }
  });
}

const submitStep1 = () => {
  step1FormRef.value.validate(valid => {
    if(valid){
      console.log('提交订单')
      submitLoading.value = true
      step.value = 1
    }
  });
}

const step2FormRef = ref(null);
const submit2Loading = ref(false);
const agreeTerms = ref(false);

const step2Form = reactive({
  FamilyName:'',
  FirstName:'',
  FamilyNameEn:'',
  FirstNameEn:'',
  email:'',
  birthday:'',
  country: COUNTRIES[0].name,
  password:'',
  confirmPassword:'',
})

const checkPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error(t('register.enter_password_again')));
  } else if (value !== step2Form.password) {
    callback(new Error(t('register.password_donot_match')));
  } else {
    callback();
  }
}

const step2Rules = {
  FamilyName: [{required: true, message: '姓不能为空', trigger: ['change', 'blur'],}],
  FirstName: [{required: true, message: '名不能为空', trigger: ['change', 'blur'],}],
  email: [
    {required: true, message: t('common.email.required'), trigger: ['change', 'blur'],},
    {pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: t('common.email.error'), trigger: ['change', 'blur']},
  ],
  birthday: [{required: true, message: t('register.birthday.required'), trigger: ['change', 'blur'],}],
  password: [
    {required: true, message: t('common.password.required'), trigger: ['blur', 'change'],},
    { min: 8, max: 16, message: t('common.password.error_length'), trigger: ['blur', 'change'] },
    { pattern: /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/, message: t('common.password.error'), trigger: ['blur', 'change'] }
  ],
  confirmPassword: [
    {required: true, message: t('common.confirm_password.required'), trigger: 'blur',},
    { validator: checkPassword, trigger: 'blur' }
  ],
  // agreeTerms: [{required: true, message: '请先阅读条款及细则', trigger: 'blur',}],
}

const nameChange = (key, value) => {
  const pinyinName = pinyin(value, {style: pinyin.STYLE_NORMAL}).join('');
  const englishName = pinyinName.charAt(0).toUpperCase() + pinyinName.slice(1);
  switch(key){
    case 'FamilyName':
      step2Form.FamilyNameEn = englishName
      break;
    case 'FirstName':
      step2Form.FirstNameEn = englishName
      break;
  }
}

const passwordChange = () => {
  step2FormRef.value.validateField('confirmPassword')
}

const jumpCountdown = ref(JUMP_COUNTDOWN) 
const { pause:jumpPause, resume:jumpResume } = useIntervalFn(() => {
  jumpCountdown.value = jumpCountdown.value - 1
  if(jumpCountdown.value <= 0){
    jumpPause()
    window.location.href = '/user_center'
    console.log('倒计时结束')
  }
}, 1000)
jumpPause()

const submitStep2 = () => {
  step2FormRef.value.validate(valid => {
    if(valid){
      if(!agreeTerms.value){
        ElMessage.warning(t('register.read_terms_tip'))
        return
      }
      ElMessage.warning('正在开发中，敬请期待...')
      return false
      console.log('提交订单')
      submit2Loading.value = true
      useTimeoutFn(() => {
        submit2Loading.value = false
        step.value = 2
        jumpResume()
      }, 2000)
    }
  });
}

</script>