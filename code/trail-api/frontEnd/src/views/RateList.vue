<template>
  <PageHeader
    id="pageHeader"
    :hotelInfo="hotelDetail"
    :loading="hotelInfoLoading"
  >
    <div class="hidden md:block" v-show="step === 0">
      <SearchbarApp
        :role="searchRole"
        :disabled-keyword="disabledKeyword"
        :keyword="hotelDetail?.hotelName || hotelDetail?.hotelName"
        @search="hadleSearch"
      />
    </div>
  </PageHeader>
  <div class="slh-detail-sidebar">
    <a
      class="sidebar-link"
      v-if="hotelDetail"
      :href="`${rootPath}/hotel/${hotelDetail?.hotelLink}`"
    >
      {{ $t('common.view_hotel_detail') }}
      <i class="iconfont icon-arrow-right2 leading-none"></i>
    </a>
  </div>
  <div class="flex-1 pb-10 md:pb-20">
    <div class="slh_hotel_rooms container 2xl:max-w-7xl ml-auto mr-auto relative">
      <div class="md:hidden" v-show="step === 0">
        <SearchbarApp
          :role="searchRole"
          :disabled-keyword="disabledKeyword"
          :keyword="hotelDetail?.hotelName || hotelDetail?.hotelName"
          @search="hadleSearch"
        />
      </div>
      <div class="flex mt-4 md:mt-0 mb-4 items-end flex-col md:mb-6 lg:mb-11  md:flex-row">
        <div class="w-full md:w-auto md:flex-1">
          <order-steps :active="step"/>
        </div>
        <!-- <ClubTip class="w-full mb-4 md:hidden"/> -->
      </div>
      <template v-if="step === 0">
        <!-- <ClubTip class="mb-6 hidden md:flex"/> -->
        <div class="rooms px-3 sm:px-0 min-h-[100px]" v-loading="isLoading">
          <template v-if="!isLoading">
            <el-result
              icon="error"
              :title="$t('request_error')"
              :sub-title="error"
              v-if="error"
            >
              <template #extra>
                <el-button type="primary" @click="roomsRateRetry">{{$t('retry')}}</el-button>
              </template>
            </el-result>
            <template v-else>
              <template v-if="hotelRooms?.length">
                <RoomItem
                  v-for="(item, item_index) in hotelRooms"
                  :key="item_index"
                  :item="item"
                  :checkedRooms="bookingInfo.rooms"
                  @hadleBooking="hadleBooking"
                />
              </template>
              <el-empty :description="$t('rate_list.no_rooms_tip')" v-else/>
            </template>
          </template>
        </div>
      </template>
      <template v-else>
        <h1 class="text-lg md:text-xl lg:text-2xl text-black mb-5 leading-none">
          <template v-if="step === 1">{{ $t('order.checkin_title') }}</template>
          <template v-if="step === 2">{{ $t('order.from_title') }}</template>
        </h1>
        <div class="flex flex-col md:flex-row">
          <div class="flex-1">
            <el-form
              ref="step1FormRef"
              :model="step1Form"
              :rules="step1Rules"
              label-width="auto"
              label-position="top"
              require-asterisk-position="right"
              :size="elementPlusSize"
              v-show="step === 1"
            >
              <div class="space-y-3 lg:space-y-4">
                <SlhCard
                  v-for="(room, index) in step1Form.rooms"
                  :key="index"
                >
                  <div class="flex mb-5">
                    <div class="w-29 md:w-32 lg:w-40 xl:w-45 shrink-0" v-if="room?.roomInfo?.imageList[0] || `${imgHost}${room?.roomInfo?.roomSmallCoverUrl}`">
                      <img :src="room?.roomInfo?.imageList[0] || `${imgHost}${room?.roomInfo?.roomSmallCoverUrl}`" alt="">
                    </div>
                    <div class="flex-1 pl-3 md:pl-4 lg:pl-5 xl:pl-6">
                      <div class="h-full flex flex-col">
                        <p class="text-xs xl:text-xs text-text-light" v-if="step1Form.rooms.length">{{ $t('order.room') }}{{ index+1 }}：</p>
                        <p class="flex-1 text-sm xl:text-base flex flex-col lg:flex-row">
                          {{ room?.searchParams?.indate }} {{ $t('common.to') }} {{ room?.searchParams?.outdate }}
                          <span class="lg:ml-2">{{ room?.searchParams?.nights }} {{ $t('common.night') }}</span>
                        </p>
                        <p class="text-sm xl:text-base flex flex-row lg:flex-col pt-4">
                          {{ room?.roomInfo?.roomName || room?.roomInfo?.roomNameEn || room?.roomInfo?.roomDescription  }}
                          <span class="text-xs text-text-light lg:mt-2" v-if="room?.roomRate?.rateCode === 'SLHINV'">{{ $t('order.member_price') }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <h3 class="flex-1 text-sm lg:text-base text-black">{{ $t('order.checkin_info') }}</h3>
                    <span class="text-sm text-primary cursor-pointer" @click="toggleOpen(index, !room.open)">
                      <i 
                        class="iconfont icon-arrow-up inline-block text-base mr-0.5 leading-none transition-transform transform-origin-center"
                        :class="{'rotate-180': room.open}"
                      >
                      </i>
                      {{$t(`order.${room.open ? 'close' : 'open'}`)}}
                    </span>
                  </div>
                  <div class="pt-5" v-show="room.open">
                    <div class="grid gap-3 grid-cols-2 mb-4">
                      <div>
                        <el-form-item
                          class="relative z-10"
                          :label="$t('common.last_name.label')"
                          :prop="`rooms.${index}.userInfo.lastNameZh`"
                          required
                          :rules="step1Rules.lastNameZh"
                        >
                          <el-input
                            v-model="room.userInfo.lastNameZh"
                            :placeholder="$t('common.last_name.placeholder')"
                            @change="(value) => nameChange('lastNameZh', index, value)"
                          />
                        </el-form-item>
                        <div class="pt-1"><el-input v-model="room.userInfo.lastName" disabled/></div>
                      </div>
                      <div>
                        <el-form-item
                          class="relative z-10"
                          :label="$t('common.first_name.label')"
                          :prop="`rooms.${index}.userInfo.firstNameZh`"
                          required
                          :rules="step1Rules.firstNameZh"
                        >
                          <el-input
                            v-model="room.userInfo.firstNameZh"
                            :placeholder="$t('common.first_name.placeholder')"
                            @change="(value) => nameChange('firstNameZh', index, value)"
                          />
                        </el-form-item>
                        <div class="pt-1"><el-input v-model="room.userInfo.firstName" disabled/></div>
                      </div>
                    </div>
                    <el-form-item
                      :label="$t('common.email.label')"
                      :prop="`rooms.${index}.userInfo.email`"
                      required
                      :rules="step1Rules.email"
                    >
                      <el-input
                        v-model="room.userInfo.email"
                        :placeholder="$t('common.email.placeholder_order')"
                      />
                    </el-form-item>
                    <el-form-item 
                      :label="$t('common.country.label')"
                      :prop="`rooms.${index}.userInfo.country`"
                      required
                    >
                      <el-select
                        class="w-full"
                        v-model="room.userInfo.country"
                        :placeholder="$t('common.country.placeholder')"
                      >
                        <el-option
                          v-for="item in COUNTRIES"
                          :key="item.name"
                          :label="item.name"
                          :value="item.name"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :label="$t('common.address.label')"
                      :prop="`rooms.${index}.userInfo.address`"
                      required
                      :rules="step1Rules.address"
                    >
                      <el-input
                        v-model="room.userInfo.address"
                        :placeholder="$t('common.address.placeholder')"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t('common.phone.label')"
                      :prop="`rooms.${index}.userInfo.phone`"
                      required
                      :rules="room.userInfo.phoneRefix === '+86' ? step1Rules.phone_zh : step1Rules.phone"
                    >
                      <div class="w-full flex space-x-3 md:space-x-5">
                        <el-select v-model="room.userInfo.phoneRefix" class="w-25 md:w-45">
                          <el-option
                            v-for="item in COUNTRIES"
                            :key="item.name"
                            :label="`${item.name}(${item.code})`"
                            :value="item.code"
                          />
                        </el-select>
                        <div class="flex-1">
                          <el-input
                            v-model="room.userInfo.phone"
                            :placeholder="$t('common.phone.placeholder')"
                          />
                        </div>
                      </div>
                    </el-form-item>
                    <el-form-item 
                      :label="$t('order.remark.label')"
                      :prop="`rooms.${index}.userInfo.remark`"
                    >
                      <el-input
                        v-model="room.userInfo.remark"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                        type="textarea"
                        :placeholder="$t('order.remark.placeholder')"
                      />
                    </el-form-item>
                    <div class="flex">
                      <i class="iconfont icon-notice text-base leading-5 text-primary"></i>
                      <div class="flex-1 pl-1.5">
                        <h3 class="text-sm text-primary mb-1">{{ $t('order.cancel_tip') }}</h3>
                        <p class="text-xs lg:text-sm text-text-light">{{room?.roomRate?.cancelRuleString}}</p>
                      </div>
                    </div>
                  </div>
                </SlhCard>
                <SlhCard :no-bg="true" :has-inner="false">
                  <div class="space-y-4">
                    <SlhButton
                      class="w-full"
                      @click="step = 0"
                      type="defaut"
                      v-if="step1Form.rooms.length <= 0"
                    >
                      {{ $t('order.back_add_room') }}
                    </SlhButton>
                    <SlhButton
                      class="w-full"
                      type="brand"
                      :disabled="!step1Form.rooms.length"
                      @click="gotoNextStep(2)"
                      v-else
                    >
                      {{ $t('order.next_step_credit_card') }}
                    </SlhButton>
                  </div>
                </SlhCard>
              </div>
            </el-form>
            <el-form
              ref="step2FormRef"
              :model="step2Form"
              :rules="step2Rules"
              label-width="auto"
              label-position="top"
              require-asterisk-position="right"
              :size="elementPlusSize"
              v-show="step === 2"
            >
              <div class="space-y-3 lg:space-y-4">
                <SlhCard>
                  <div class="flex">
                    <i class="iconfont icon-notice text-base leading-5 text-primary"></i>
                    <div class="flex-1 pl-1.5">
                      <h3 class="text-sm text-primary mb-2">{{ $t('order.guarantee_des') }}</h3>
                      <p class="text-xs lg:text-sm text-text-light">{{ $t('order.guarantee_tip') }}</p>
                      <h3 class="text-sm text-black mt-4">
                        {{ $t('order.following_credit_cards', {hotel_name: hotelDetail?.hotelName || hotelDetail?.hotelName}) }}
                      </h3>
                      <div class="flex flex-wrap -mx-2.5 pt-1.5 space-x-2">
                        <span
                          v-for="(card, card_index) in hotelDetail?.acceptedCreditCard"
                          :key="card_index"
                          class="credit_card inline-block h-8 w-8 lg:w-10 lg:h-10 bg-no-repeat bg-center bg-contain"
                          :class="[`__${card.ccCode}`]"
                        >
                        </span>
                      </div>
                    </div>
                  </div>
                </SlhCard>
                <SlhCard>
                  <el-form-item
                    :label="$t('order.select_credit_card_type')"
                    prop="creditCardCode"
                  >
                    <el-radio-group
                      v-model="step2Form.creditCardCode"
                    >
                      <el-radio
                        v-for="(card, card_index) in hotelDetail?.acceptedCreditCard"
                        :key="card_index"
                        :label="card.ccCode"
                      >
                        <template #default>
                          <span class="inline-flex items-center leading-none space-x-2">
                            {{ card.ccCode }}
                            <span
                              class="inline-block h-5 w-5 lg:w-7 lg:h-7 bg-no-repeat bg-center bg-contain"
                              :style="{backgroundImage: `url('${rootPath}/static/assets/imgs/banks/${card.ccCode}.png')`}"
                            >
                            </span>
                          </span>
                        </template>
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item
                    :label="$t('order.card_number.label')"
                    prop="cardNumber"
                  >
                    <el-input
                      v-model.number="step2Form.cardNumber"
                      :placeholder="$t('order.card_number.placeholder')"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t('order.card_expire_date.label')"
                    prop="cardExpireDate"
                  >
                    <el-date-picker
                      v-model="step2Form.cardExpireDate"
                      type="month"
                      :disabled-date="getCardExpireDateDisabledDate"
                      :placeholder="$t('order.card_expire_date.placeholder')"
                      :format="MONTH_FORMAT"
                      :value-format="MONTH_FORMAT"
                      style="width: 100%"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t('order.card_cvv.label')"
                    prop="cardCVV"
                  >
                    <el-input
                      v-model.number="step2Form.cardCVV"
                      :label="$t('order.card_cvv.placeholder')"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t('order.card_holder_name.label')"
                    prop="cardHolderName"
                  >
                    <el-input
                      v-model="step2Form.cardHolderName"
                      :placeholder="$t('order.card_holder_name.placeholder')"
                    />
                  </el-form-item>
                </SlhCard>
                <SlhCard>
                  <div class="space-y-4 md:space-y-6">
                    <h2 class="text-base">{{ $t('order.policy') }}</h2>
                    <div
                      class="space-y-3 md:space-y-4"
                      v-for="(room, room_index) in step1Form.rooms"
                      :key="room_index"
                    >
                      <h3 class="text-primary text-base">{{ $t('order.room') }} {{ room_index + 1}} {{room?.roomInfo?.roomName || room?.roomInfo?.roomNameEn || room?.roomInfo?.roomDescription}} </h3>
                      <div class="space-y-3 md:space-y-4">
                        <div class="flex items-center space-x-8">
                          <div class="space-y-1">
                            <h3 class="text-sm text-black">{{ $t('order.room_checkin') }}</h3>
                            <p class="text-sm text-text-light">{{ $t('order.time_after', {time: room?.rateInfo?.checkRule?.checkInAfter}) }}</p>
                          </div>
                          <div class="space-y-1">
                            <h3 class="text-sm text-black">{{ $t('order.room_checkout') }}</h3>
                            <p class="text-sm text-text-light">{{ $t('order.time_before', {time: room?.rateInfo?.checkRule?.checkOutBefore}) }}</p>
                          </div>
                        </div>
                      </div>
                      <div class="space-y-1">
                        <h3 class="text-sm text-black">{{ $t('order.guarantee_policy') }}</h3>
                        <p class="text-sm text-text-light">{{ room?.rateInfo?.guaranteeRuleText }}</p>
                      </div>
                      <div class="space-y-1">
                        <h3 class="text-sm text-black">{{ $t('order.cancel_policy') }}</h3>
                        <p class="text-sm text-text-light">{{ room?.rateInfo?.cancelRuleText }}</p>
                      </div>
                    </div>
                  </div>
                </SlhCard>
                <SlhCard :no-bg="true" :has-inner="false">
                  <div class="space-y-4">
                    <SlhButton
                      class="w-full"
                      @click="submitOrder"
                      type="brand"
                      :loading="reservationIsLoading || reservationRoomsIsLoading || rateInfoLoading"
                      :disabled="reservationIsLoading || reservationRoomsIsLoading || rateInfoLoading"
                    >
                      {{ $t('order.continue_booking') }}
                    </SlhButton>
                    <SlhButton
                      class="w-full"
                      @click="step = 1"
                      type="defaut"
                      v-if="!(reservationIsLoading || reservationRoomsIsLoading)"
                    >
                      {{ $t('order.back_prve_step') }}
                    </SlhButton>
                  </div>
                </SlhCard>
              </div>
            </el-form>
            <div class="space-y-3 lg:space-y-4" v-if="step===3">
              <SlhCard>
                <div class="py-4 flex flex-col justify-center text-center">
                  <i class="iconfont icon-copy text-6xl lg:text-7xl leading-none text-success"></i>
                  <h3 class="text-xl lg:text-2xl text-black mt-4 mb-2">{{ $t(`order.${isEdit ? 'edit_success' : 'book_success'}`) }}</h3>
                  <template v-if="step1Form.rooms.length > 1">
                    <div
                      class="text-sm mt-2 flex space-x-1 justify-center"
                      v-for="(order, index) in reservationoOrderInfo"
                      :key="order?.confirmation || index"
                    >
                      <span>{{ $t('order.room') }}{{ index + 1}} </span>
                      <div
                        class="space-y-1 text-left"
                      >
                        <p class="text-text">
                          {{ $t('order.send_to_email') }}
                        </p>
                        <p class="text-primary">{{ order?.email }}</p>
                        <p class="text-text">
                          {{ $t('confirm_num') }} {{ order?.confirmation }}
                        </p>
                      </div>
                    </div>
                  </template>
                  <div
                    class="text-sm mt-2 flex space-x-1 justify-center"
                    v-else
                  >
                    <div class="space-y-1 text-center">
                      <p class="text-text">
                        {{ $t('order.send_to_email') }}
                      </p>
                      <p class="text-primary">{{room?.userInfo?.email }}</p>
                    </div>
                  </div>
                </div>
                <div class="mt-6">
                  <div class="border-t border-b border-line-light py-1">
                    <div class="flex items-center justify-center">
                      <div
                        class="flex items-center text-sm divide-x divide-line-light space-x-1"
                        v-if="step1Form.rooms.length > 1"
                      >
                        <div class="text-text-light flex-1 lg:flex-none pr-3">
                          <span class="lg:flex-1">{{ $t('order.order_no') }}</span>
                          <a
                            class="text-primary cursor-pointer text-sm lg:text-base underline underline-offset-4"
                            :href="`${rootPath}/booking/order_info?reservationId=${reservationoOrderInfo[0]?.itinerary}&lastName=${reservationoOrderInfo[0]?.lastName}`">
                            {{reservationoOrderInfo[0]?.itinerary}}
                          </a>
                        </div>
                        <span
                          class="text-sm text-text-light hover:text-text inline-flex items-center pl-3 cursor-pointer"
                          v-if="isSupported"
                          @click="copyText(reservationoOrderInfo?.confirmation)"
                        >
                          <i class="iconfont icon-copy1 leading-none mr-1"></i>
                          {{ $t('order.copy') }}
                        </span>
                      </div>
                      <div
                        class="flex items-center text-sm divide-x divide-line-light space-x-1"
                        v-else
                      >
                        <div class="text-text-light flex-1 lg:flex-none pr-3">
                          <span class="lg:flex-1">{{ $t('confirm_num') }}</span>
                          <a
                            class="text-primary cursor-pointer text-sm lg:text-base underline underline-offset-4"
                            :href="`${rootPath}/booking/order_info?reservationId=${reservationoOrderInfo?.confirmation}&lastName=${reservationoOrderInfo?.lastName}`">
                            {{reservationoOrderInfo?.confirmation}}
                          </a>
                        </div>
                        <span
                          class="text-sm text-text-light hover:text-text inline-flex items-center pl-3 cursor-pointer"
                          v-if="isSupported"
                          @click="copyText(reservationoOrderInfo?.confirmation)"
                        >
                          <i class="iconfont icon-copy1 leading-none mr-1"></i>
                          {{ $t('order.copy') }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <p class="pt-5 text-xs lg:text-sm text-text-dark text-center">
                  {{ $t(`order.order_save_tip${step1Form.rooms.length > 1 ? '' : '2'}`) }}
                </p>
              </SlhCard>
              <SlhCard :no-bg="true" :has-inner="false">
                <div class="space-y-2.5 md:space-y-0 md:space-x-6 md:flex">
                  <SlhButton
                    class="w-full"
                    :href="rootPath || '/'"
                  >
                    {{$t('order_info.return_home')}}
                  </SlhButton>
                </div>
              </SlhCard>
            </div>
          </div>
          <div
            class="w-screen md:w-72 lg:w-80 2xl:w-100 md:ml-3 lg:ml-4 2xl:ml-5 fixed bottom-0 right-0 z-10 md:z-0 md:static"
            ref="bookInfoRef"
          >
            <div
              class="w-full"
              v-loading="rateInfoLoading"
              element-loading-background="rgba(40, 40, 40, 0.8)"
            >
              <div
                class="flex px-4 py-3 items-center bg-bg_blank text-white text-base md:hidden cursor-pointer"
                @click="toggleInfoOpen"
                :class="{'border-b border-line-light': infoOpen}"
              >
                <span class="flex-1">{{ $t('order.checkin_title') }}</span>
                <i 
                  class="iconfont icon-arrow-down inline-block text-base mr-0.5 leading-none transition-transform transform-origin-center"
                  :class="{'rotate-180': infoOpen}"
                >
                </i>
              </div>
              <div
                class="bg-bg_blank h-screen-80 md:h-auto overflow-y-scroll md:overflow-y-visible px-4 md:px-5 lg:px-6 2xl:px-8 py-5 md:py-6 lg:py-7 2xl:py-7.5 divide-y divide-line-dark md:block"
                :class="{'block': infoOpen, 'hidden': !infoOpen}"
              >
                <div>
                  <h3 class="text-lg text-white">{{ hotelDetail?.hotelName }}</h3>
                  <h4 class="text-sm text-white">{{ hotelDetail?.hotelNameEn }}</h4>
                  <div class="py-3 md:py-4 lg:py-5  2xl:py-6">
                    <div class="text-text-light flex items-start text-xs lg:text-sm">
                      <i class="iconfont icon-location text-sm lg:text-base"></i>
                      <p class="flex-1 pl-2">{{ hotelDetail?.address || hotelDetail?.addressEn }}</p>
                    </div>
                    <a
                      class="text-text-light flex items-center text-xs lg:text-sm"
                      :href="`tel:${ hotelDetail?.phone }`">
                      <i class="iconfont icon-phone text-sm lg:text-base"></i>
                      <p class="flex-1 pl-2">{{ hotelDetail?.phone  }}</p>
                    </a>
                  </div>
                </div>
                <div class="lg:py-1">
                  <div
                    class="space-y-3 md:space-y-4 lg:space-y-5 py-3 md:py-4 lg:py-5 2xl:py-6"
                    v-for="(room, room_index) in step1Form.rooms"
                    :key="room_index"
                  >
                    <div class="flex">
                      <div class="flex-1">
                        <SlhTag>{{ $t('order.room') }}{{ room_index + 1}}</SlhTag>
                      </div>
                      <div class="space-x-1.5 lg:space-x-2" v-if="step !== 3">
                        <button
                          class="slh-edit-btn"
                          @click="editRoom(room_index)"
                        >
                          <i class="iconfont icon-edit text-sm leading-none"></i>
                        </button>
                        <button
                          class="slh-edit-btn"
                          @click="deleteRoom(room_index)"
                          v-if="!isEdit"
                        >
                          <i class="iconfont icon-delete text-sm leading-none"></i>
                        </button>
                      </div>
                    </div>
                    <div class="space-y-2 text-white text-sm">
                      <p>{{ $t('order.checkin_date') }}</p>
                      <p>{{ room?.searchParams?.indate }}{{ $t('common.to') }}{{ room?.searchParams?.outdate }} {{ room?.searchParams?.nights }}{{ $t('common.night') }}</p>
                    </div>
                    <div class="flex items-end text-white">
                      <div class="space-y-2 flex-1">
                        <h3 class="text-base">{{ room?.roomInfo?.roomName || room?.roomInfo?.roomNameEn || room?.roomInfo?.roomDescription }}</h3>
                        <p class="text-xs text-text-light_2" v-if="room?.roomRate?.rateCode === 'SLHINV'">
                          {{ $t('order.member_price') }}
                        </p>
                        <p class="text-xs">
                          {{ $t('common.aldult') }}{{ room?.searchParams?.adultsPerRoom }}
                          <span v-if="room?.searchParams?.childrenPerRoom">， {{ $t('common.child') }} {{ room?.searchParams?.childrenPerRoom }}</span>
                          ， {{ room?.searchParams?.nights }}{{ $t('common.night') }}
                        </p>
                      </div>
                      <div class="space-y-1">
                        <p class="text-lg md:text-xl leading-none">
                          <SlhPrice :price="room?.rateInfo?.cnyEstimatedTotal100" />
                        </p>
                        <p class="text-xs text-text-light text-right">{{ $t('order.tax_included') }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="py-6" v-if="step !== 3 && !submitLoading && !isEdit">
                    <SlhButton
                      class="w-full"
                      @click="addRoom"
                    >
                      {{ $t('order.keep_adding') }}
                    </SlhButton>
                  </div>
                </div>
                <div class="pt-5 lg:pt-6 2xl:pt-7 text-xs">
                  <div class="flex justify-between items-center">
                    <span class="text-sm lg:text-base text-white">{{ $t('order.total_price') }}</span>
                    <div class="text-lg lg:text-2.1xl text-brand">
                      <SlhPrice :price="orderTotalPrice" />
                    </div>
                  </div>
                  <!-- <p class="text-right text-text-light_2 mt-2">{{ $t('order.member_price') }}</p> -->
                  <div class="flex justify-between text-text-light_2 mt-2">
                    <div class="group text-sm inline-flex items-center hover:text-white cursor-pointer relative">
                      <i class="iconfont icon-help text-base leading-none"></i>
                      <span class="underline underline-offset-4 pl-1">{{ $t('order.view_detail') }}</span>
                      <div class="hidden group-hover:block absolute w-[248px] lg:w-[272px] bottom-full md:bottom-auto md:top-full left-0 ">
                        <div class="relative pb-3 md:pt-3">
                          <div class="bg-white px-4 py-2 shadow-lg divide-y divide-line">
                            <div
                              class="flex text-xs lg:text-sm py-4"
                              v-for="(room, room_index) in step1Form.rooms"
                              :key="room_index"
                            >
                              <span class="text-text-light">{{ $t('order.room') }}{{ room_index + 1 }}</span>
                              <div class="flex-1 pl-4 space-y-2">
                                <div class="flex text-black space-x-2">
                                  <span class="text-text">{{ $t('order.price') }}</span>
                                  <SlhPrice :price="room?.rateInfo?.cnyTotalRate100" />
                                </div>
                                <div class="flex text-black space-x-2">
                                  <span class="text-text">{{ $t('order.tax') }}</span>
                                  <SlhPrice :price="room?.rateInfo?.cnyEstimatedTotal100 - room?.rateInfo?.cnyTotalRate100" />
                                </div>
                              </div>
                            </div>
                          </div>
                          <span class="block w-2 h-2 bg-white rotate-45 absolute left-4 bottom-2 md:bottom-auto md:top-2"></span>
                        </div>
                      </div>
                    </div>
                    <span class="text-xs lg:text-sm">{{ $t('order.tax_included') }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script setup>
import { getUrlParam } from 'ppo'
import { useI18n } from 'vue-i18n'
import { ref, reactive, watch, computed, onMounted, onUnmounted } from 'vue'
import { useSessionStorage, useClipboard } from '@vueuse/core'
import PageHeader from '@/components/PageHeader.vue'
import SearchbarApp from '@/apps/SearchbarApp.vue'
import RoomItem from '@/components/RoomItem.vue'
import ClubTip from '@/components/ClubTip.vue'
import OrderSteps from '@/components/OrderSteps.vue'
import SlhButton from '@/components/SlhButton.vue'
import SlhTag from '@/components/SlhTag.vue'
import SlhCard from '@/components/SlhCard.vue'
import { SessionKeysEnum } from '@/enums/appEnums'
import { DEFAULT_QUERY_PARAMS, CHAIN_CODE, DATE_FORMAT, DATE_FORMAT5, MONTH_FORMAT, COUNTRIES, } from '@/config/index'
import { useRequest } from '@/utils/request'
import { emailRegExp, phoneRegExp, loosePhoneRegExp, userEnNameRegExp, cardCVVRegExp} from '@/utils/verify'
import useAppStore from '@/stores/app'
import dayjs from 'dayjs'
import pinyin from 'pinyin'
import { ElMessage, ElMessageBox } from 'element-plus'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import { REQUEST_CONFIG } from '@/config/index'

dayjs.extend(isSameOrBefore)

const chainCode = CHAIN_CODE
const { t } = useI18n()
const { rootPath, elementPlusSize } = useAppStore()
const { copy, isSupported } = useClipboard()
const hotelCode = getUrlParam('hotelCode')
const searchRole = ref('booking')
const disabledKeyword = ref(true)
const bookInfoRef = ref(true)
const infoOpen = ref(true)
const reservationoOrderInfo = ref(null) // 预定成功后的订单信息
const imgHost = ref(REQUEST_CONFIG.imgHost) // 图片地址

const toggleInfoOpen = () => {
  infoOpen.value = !infoOpen.value
}

const handleClick = (e) => {
  if(bookInfoRef?.value && bookInfoRef?.value?.contains && !bookInfoRef?.value?.contains(e.target) && infoOpen.value){
    infoOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener("click", handleClick);
})
onUnmounted(() => {
  document.removeEventListener("click", handleClick);
})

// 查询参数
const defaultQueryParams = {
  ...DEFAULT_QUERY_PARAMS,
  orderBy: 'room',
  chainCode,
  hotelCode,
  promotionCodeType: 'none',
  promotionCode: '',
}

// 存储预订信息
const defaultBookingInfo = {
  hotelCode,
  rooms: [],
}
let queryParams = reactive(defaultQueryParams)
const bookingInfo = ref(defaultBookingInfo)
// const bookingInfo = useSessionStorage(SessionKeysEnum.BOOKING_INFO, defaultBookingInfo, sessionStorage, { mergeDefaults: true })
// if(!bookingInfo.value || bookingInfo.value?.hotelCode !== hotelCode){
//   // 酒店不同设置为默认值
//   bookingInfo.value = defaultBookingInfo
// }

const isEdit = ref(false)
const step = ref(0)
const step1FormRef = ref(null)
const step2FormRef = ref(null)
const submitLoading = ref(false)
const editIndex = ref(null)
let step1Form = reactive({
  rooms: [],
})
let step2Form = reactive({
  // creditCardCode: hotelDetail.value?.acceptedCreditCard[0].ccCode,
  creditCardCode: '',
  cardNumber: '',
  cardExpireDate: '',
  cardCVV: '',
  cardHolderName: '',
})

// 默认用户信息
const defaultUserInfo = {
  lastNameZh: '',
  firstNameZh: '',
  lastName: '',
  firstName: '',
  email: '',
  country: COUNTRIES[0].name,
  countryCode: COUNTRIES[0].countryCode,
  address: '',
  phoneRefix:COUNTRIES[0].code,
  phone: '',
  remark: '',
}

const { data: hotelInfo, isLoading: hotelInfoLoading, execute: hotelInfoExecute } = useRequest({
  url: '/cms-web/v1/getHotelDetails',
})
hotelInfoExecute({
  params: {
    chainCode,
    hotelCodes:hotelCode,
  }
})

const hotelDetail = computed(() => {
  const _hotelInfo = hotelInfo.value?.data?.length && hotelInfo.value?.data[0] || {};
  return {
    ..._hotelInfo,
    hotelBanner: _hotelInfo?.banners && _hotelInfo?.banners[0]?.sourceUrl ? `${window.IMG_PROXY_URL}?url=${encodeURIComponent(_hotelInfo?.banners[0]?.sourceUrl)}` : (_hotelInfo?.coverUrl || ''),
  };
});

const { data, isLoading, execute, error} = useRequest({
  url: '/cms-web/v1/getHotelRoomsRate',
})
execute({
  params: queryParams
})

const hotelRooms = computed(() => {
  const _hotelRooms = (data?.value?.data || []).map(item => ({
    ...item,
    imageList: (item?.imageList || []).map(_item => `${window?.IMG_PROXY_URL}?url=${encodeURIComponent(_item)}`)
  }))
  return _hotelRooms;
});
const roomsRateRetry = () => {
  execute({
    params: queryParams
  })
}

const reservationId = ppo.getUrlParam('reservationId') // 修改订单使用子订单号
const reservationMainId = ppo.getUrlParam('reservationMainId') // 修改订单的主订单号
const lastName = ppo.getUrlParam('lastName')
const { data:orderData, isLoading:orderIsLoading, execute: orderExecute} = useRequest({
  url: '/cms-web/v1/getReservationDetailByIdAndLastNameAndSub',
})
if(reservationId && lastName && reservationMainId){
  isEdit.value = true
  orderExecute({
    params: {
      chainCode,
      reservationId: reservationMainId,
      reservationSubId: reservationId,
      lastName,
    }
  })
}

// 从订单中获取用户信息
const getUserInfo = () => {
  const orderInfo = orderData.value?.data
  console.log('getUserInfo === orderInfo', orderInfo)
  if(orderInfo){
    const { guestFirstName, guestFirstNameCN, guestLastName, guestLastNameCN, guestAddress, guestPhone, guestEmail, remarks, guestCountryCode} = orderInfo
    const phoneArry = (guestPhone || '').split(' ')
    const curCountry = COUNTRIES.find(item => item.countryCode === guestCountryCode)
    return {
      lastNameZh: guestLastNameCN || guestLastName || '',
      firstNameZh: guestFirstNameCN || guestFirstName || '',
      lastName: guestLastName || '',
      firstName: guestFirstName || '',
      email: guestEmail || '',
      country: curCountry?.name || '',
      address: guestAddress || '',
      phoneRefix: phoneArry && phoneArry.length >= 2 ? phoneArry[0] : '',
      phone: phoneArry && phoneArry.length >= 2 ? phoneArry[1] : '',
      remark: remarks || '',
      countryCode: guestCountryCode || '',
    }
  } else {
    return null
  }
}

// 初始化订单数据
const initOrderInfo = () => {
  if(data.value && orderData.value){
    const orderInfo = orderData.value?.data
    console.log('initOrderInfo === orderInfo', orderInfo)
    const { roomCode, rateCode } = orderInfo
    let curRoomInfo = null
    let curRoomRate = null
    data.value.data.map(item => {
      item.roomRateList.map(cell => {
        if(cell.roomCode === roomCode && cell.rateCode === rateCode){
          curRoomRate = cell
          curRoomInfo = item
        }
      })
    })
    if(curRoomRate && curRoomInfo){
      const curRoom = {
        searchParams: JSON.parse(JSON.stringify(queryParams)),
        roomRate: curRoomRate,
        roomInfo: curRoomInfo,
      }
      bookingInfo.value = {
        ...bookingInfo.value,
        rooms: [curRoom],
      }
      const orderUserInfo = getUserInfo()
      step1Form.rooms = [
        {
          ...curRoom,
          open: true,
          userInfo: orderUserInfo || defaultUserInfo,
        }
      ]
      // step2Form = {
      //   creditCardCode: '',
      //   cardNumber: '',
      //   cardExpireDate: '',
      //   cardCVV: '',
      //   cardHolderName: '',
      // }
      console.log('orderInfo', orderInfo)
      console.log('curRoom', curRoom)
    }
  }
}

watch(
  data,
  () => {
    // console.log('data === 2', data.value)
    isEdit.value && initOrderInfo()
  },
)

watch(
  orderData,
  () => {
    isEdit.value && initOrderInfo()
  },
)

watch(
  bookingInfo,
  () => {
    console.log('bookingInfo === 2', bookingInfo)
  },
)

watch(
  step,
  () => {
    const footer = document.getElementById('footer')
    const layoutMain = document.getElementById('layout_main')
    const pageHeader = document.getElementById('pageHeader')
    layoutMain.scrollTop = pageHeader.offsetHeight
    console.log('step', step.value)
    if(step.value !== 0 && !footer.classList.contains('has_fixed')){
      footer.classList.add('has_fixed');
    } else {
      footer.classList.remove('has_fixed');
    }
  },
)

const updateQueryParams = (data) => {
  Object.keys(data).map(key => {
    queryParams[key] = data[key]
  })
}

const hadleSearch = (searchData) => {
  searchData && updateQueryParams(searchData)
  execute({
    params: queryParams
  })
}

onUnmounted(() => {
  bookingInfo.value = {}
  queryParams = {}
})

const orderTotalPrice = computed(() => {
  let totallPrice = 0
  step1Form.rooms?.map(room => {
    totallPrice += room?.rateInfo?.cnyEstimatedTotal100
  })
  return totallPrice;
});

const { execute: rateInfoExecute } = useRequest({
  url: '/cms-web/v1/getRateInfo',
})

// 获取房型实时价格
const rateInfoLoading = ref(true)
const getRateInfo = async (callback) => {
  let curRoom = null
  let curRoomIndex = null
  rateInfoLoading.value = true
  step1Form.rooms.map((room, room_index) => {
    if(!curRoom && (!room?.rateInfo || (!room?.reRateInfo && callback))){
      curRoom = room
      curRoomIndex = room_index
    }
  })
  if(curRoom){
    const { data } = await rateInfoExecute({
      params: {
        ...curRoom?.searchParams,
        roomRateCode: curRoom?.roomRate?.roomRateCode,
      }
    })
    const { success, errorMsg  } = data.value
    if(!success){
      ElMessageBox.confirm(errorMsg)
        .then(() => {
          step.value = 0
        }).then(() => {})
    }
    if(callback){
      step1Form.rooms[curRoomIndex].reRateInfo = true
    }
    const oldRateInfo = step1Form.rooms[curRoomIndex]?.rateInfo
    const newRateInfo = data.value?.data
    // const isChange = true
    const isChange = callback && oldRateInfo && oldRateInfo?.rateCode === newRateInfo?.rateCode && oldRateInfo?.cnyEstimatedTotal100 !== newRateInfo?.cnyEstimatedTotal100
    if(isChange){
      console.log('价格发生了变化')
      ElMessageBox.confirm(
        t('order_info.price_change_tip',
        {
          name: newRateInfo?.roomDescription,
          old_price: oldRateInfo?.cnyEstimatedTotal100,
          new_price: newRateInfo?.cnyEstimatedTotal100,
        }),
        t('order_info.price_change'),
        {
          confirmButtonText: t('order_info.price_change_ok'),
          callback: (res) => {
            if(res === 'confirm'){
              step1Form.rooms[curRoomIndex].rateInfo = newRateInfo
              getRateInfo(callback)
            } else {
              rateInfoLoading.value = false
            }
          },
        }
      )
    } else {
      step1Form.rooms[curRoomIndex].rateInfo = newRateInfo
      getRateInfo(callback)
    }
  } else {
    console.log('所有方型实时价格查询完毕')
    rateInfoLoading.value = false
    callback && callback()
  }
}

// 预订
const hadleBooking = (roomInfo, roomRate) => {
  let rooms = bookingInfo.value?.rooms || []
  const newRoom = {
    searchParams: JSON.parse(JSON.stringify(queryParams)),
    roomRate,
    roomInfo,
  }
  window?.SlhGlobal?.gtagEvent('rate_list_step1', {
    roomRate,
  })
  console.log('newRoom', newRoom)
  // console.log('isEdit.value', isEdit.value)
  // console.log('editIndex.value', editIndex.value)
  if(isEdit.value){
    rooms = [newRoom]
  } else {
    if(editIndex.value !== null && editIndex.value <= rooms.length -1){
      rooms.splice(editIndex.value, 1, newRoom)
    } else {
      rooms.push(newRoom)
    }
  }
  bookingInfo.value = {
    ...bookingInfo.value,
    rooms,
  }
  step1Form.rooms = bookingInfo.value?.rooms?.map((room, index) => {
    const formItem  = step1Form.rooms[index]
    const orderUserInfo = getUserInfo()
    const item = {
      ...room,
      open: true,
      userInfo: formItem ? formItem?.userInfo : orderUserInfo || defaultUserInfo,
    }
    console.log('formItem', formItem, 'item', item)
    return item
  }) || [],
  console.log('预订信息 === bookingInfo', bookingInfo.value)
  editIndex.value = null
  step.value = 1
  getRateInfo()
}

const step1Rules = {
  lastNameZh: [{required: true, message: t('common.last_name.required'), trigger: ['change', 'blur'],}],
  firstNameZh: [{required: true, message: t('common.first_name.required'), trigger: ['change', 'blur'],}],
  email: [
    {required: true, message: t('common.email.required'), trigger: ['change', 'blur'],},
    {pattern: emailRegExp, message: t('common.email.error'), trigger: ['change', 'blur']},
  ],
  address: [{required: true, message: t('common.address.required'), trigger: ['change', 'blur'],}],
  phone: [
    {required: true, message: t('common.phone.required'), trigger: ['change', 'blur'],},
    {pattern: loosePhoneRegExp, message: t('common.phone.error'), trigger: ['change', 'blur']},
  ],
  phone_zh: [
    {required: true, message: t('common.phone.required'), trigger: ['change', 'blur'],},
    {pattern: phoneRegExp, message: t('common.phone.error'), trigger: ['change', 'blur']},
  ],
}

// 监听名字变化
const nameChange = (key, index, value) => {
  const pinyinName = pinyin(value, {style: pinyin.STYLE_NORMAL}).join('');
  const englishName = pinyinName.charAt(0).toUpperCase() + pinyinName.slice(1);
  switch(key){
    case 'lastNameZh':
      step1Form.rooms[index].userInfo.lastName = englishName
      break;
    case 'firstNameZh':
      step1Form.rooms[index].userInfo.firstName = englishName
      break;
  }
}

const step2Rules = {
  creditCardCode: [{ required: true, message: t('rate_list.creditCardCode.required'), trigger: ['change', 'blur'],}],
  cardNumber: [
    { required: true, message: t('rate_list.cardNumber.required'), trigger: ['change', 'blur'],},
    { type: 'number', message: t('rate_list.cardNumber.error'), trigger: ['change', 'blur']},
  ],
  cardExpireDate: [{ required: true, message: t('rate_list.cardExpireDate.required'), trigger: ['change', 'blur'],},],
  cardCVV: [
    { required: true, message: t('rate_list.cardCVV.required'), trigger: ['change', 'blur'],},
    { pattern: cardCVVRegExp, message: t('rate_list.cardCVV.error'), trigger: ['change', 'blur']},
  ],
  cardHolderName: [
    {required: true, message: t('rate_list.cardHolderName.required'), trigger: ['change', 'blur'],},
    {pattern: userEnNameRegExp, message: t('rate_list.cardHolderName.error'), trigger: ['change', 'blur']},
  ],
}

// 房间入住信息是否展示
const toggleOpen = (index, state) => {
  step1Form.rooms[index].open = state
}

// 删除房间
const deleteRoom = (index) => {
  ElMessageBox.confirm(t('rate_list.delete_room_tip', {index: index + 1}))
    .then(() => {
      const rooms = bookingInfo.value?.rooms
      rooms.splice(index, 1)
      step1Form.rooms.splice(index, 1)
      bookingInfo.value.rooms = rooms
    }).then(() => {})
}

// 增加房间
const addRoom = () => {
  step.value = 0
}

// 编辑房间
const editRoom = (index) => {
  step.value = 0
  editIndex.value = index
}

// 复制文字
const copyText = (text) => {
  copy(text)
  ElMessage.success(t('order.copy_success'))
}

// 到下一步
const gotoNextStep = (nextStep) => {
  console.log('nextStep', nextStep)
  switch(nextStep){
    case 2:
      step1Form.rooms.forEach((item, index) => {
        const userInfo = step1Form.rooms[index].userInfo
        const countryCode = COUNTRIES.find(item => item.name === userInfo.country)?.countryCode || ''
        step1Form.rooms[index].userInfo = {
          ...item.userInfo,
          countryCode
        }
      })
      if(!step2Form.creditCardCode && hotelDetail.value?.acceptedCreditCard.length){
        // 设置默认信用卡类型为第一个
        step2Form.creditCardCode = hotelDetail.value?.acceptedCreditCard[0].ccCode
      }
      step1FormRef.value.validate(valid => {
        if (valid) {
          window?.SlhGlobal?.gtagEvent('rate_list_step2', {
            roomRates: step1Form.rooms.map(item => item.roomRate),
          })
          step.value = nextStep
        }
      });
      break;
  }
}

// 提交/编辑单个订单
const { isLoading:reservationIsLoading, execute: reservationExecute } = useRequest({
  url: `/cms-web/v1/${isEdit.value ? 'modifyReservation' : 'createHotelReservation'}`,
})

// 多个订单
const { isLoading:reservationRoomsIsLoading, execute: reservationRoomsExecute } = useRequest({
  method: "POST",
  url: '/cms-web/v1/createHotelReservationByMultiRooms',
})

// 预订成功后需要的处理
const reservationSuccess = (data) => {
  if(!data?.success){
    ElMessage.error(data?.errorMsg)
    return false
  } else {
    console.log('订单提交成功')
    reservationoOrderInfo.value = data?.data
    bookingInfo.value.rooms = []
    step.value = 3
    searchRole.value = ''
    disabledKeyword.value = false
  }
}

const summitReservation = async () => {
  console.log('开始提交订单====')
  if(step1Form.rooms.length === 1){
    // 只有一个房型
    const curRoom = step1Form.rooms[0]
    // submitLoading.value = true
    const indate = dayjs(curRoom?.searchParams?.indate).format(DATE_FORMAT5)
    const outdate = dayjs(curRoom?.searchParams?.outdate).format(DATE_FORMAT5)
    let params = {
      ...curRoom?.searchParams,
      ...curRoom?.userInfo,
      ...step2Form,
      salutation: 1, // 性别默认1
      indate,
      start: indate,
      outdate,
      end: outdate,
      email: curRoom?.userInfo?.email ? curRoom?.userInfo?.email.trim() : '',
      phone: `${curRoom?.userInfo?.phoneRefix} ${curRoom?.userInfo?.phone}`,
      roomTypeCode: curRoom?.roomRate?.roomCode,
      ratePlanCode: curRoom?.roomRate?.rateCode,
      rmaCodes: ''
    }
    if(isEdit.value && reservationId && reservationMainId){
      params.reservationId = reservationId
      params.itineraryId = reservationMainId
    }
    // console.log('params', params)
    const { data } = await reservationExecute({
      params,
    })
    console.log('reservationExecute === data', data.value)
    reservationSuccess(data.value)
  } else if(step1Form.rooms.length > 1){
    // 多个房型房型
    let params = []
    step1Form.rooms.map(room => {
      const indate = dayjs(room?.searchParams?.indate).format(DATE_FORMAT5)
      const outdate = dayjs(room?.searchParams?.outdate).format(DATE_FORMAT5)
      params.push({
        ...room?.searchParams,
        ...room?.userInfo,
        ...step2Form,
        salutation: 1, // 性别默认1
        indate,
        start: indate,
        outdate,
        end: outdate,
        email: room?.userInfo?.email ? room?.userInfo?.email.trim() : '',
        phone: `${room?.userInfo?.phoneRefix} ${room?.userInfo?.phone}`,
        roomTypeCode: room?.roomRate?.roomCode,
        ratePlanCode: room?.roomRate?.rateCode,
        rmaCodes: ''
      })
    })
    // console.log('params', params)
    const { data } = await reservationRoomsExecute({
      data: params,
    })
    console.log('reservationRoomsExecute === data', data.value)
    reservationSuccess(data.value)
  }
}

const submitOrder = () => {
  step2FormRef.value.validate(valid => {
    if (valid) {
      console.log('提交订单')
      window?.SlhGlobal?.gtagEvent('rate_list_step3', {
        roomRates: step1Form.rooms.map(item => item.roomRate),
      })
      step1Form.rooms = step1Form.rooms.map(item => {
        item.reRateInfo = false
        return item
      })
      console.log('step1Form.rooms', step1Form.rooms)
      getRateInfo(() => {
        summitReservation()
      }) // 重新检查检查下价格再提交数据
    }
  });
}

// 获取信用卡到期日禁用日期
const getCardExpireDateDisabledDate = (time) => {
  const roomsOutdates = step1Form.rooms.map(item => (item?.searchParams?.outdate))
  const maxDate = new Date(Math.max.apply(null, [dayjs().format(DATE_FORMAT), ...roomsOutdates].map(function(dateString) {
    return new Date(dateString);
  })))
  // console.log('maxDate', dayjs(maxDate).format(DATE_FORMAT))
  const disabled = dayjs(time).isSameOrBefore(dayjs(maxDate), 'month')
  return disabled
}

</script>
<style lang="scss">
.slh_hotel_rooms{
  .sort_select{
    .el-input__suffix-inner{
      border-left:1px  solid #d4d4d4;
    }
  }
}
</style>
<style lang="scss" scoped>
.credit_card{
  &.__AX{
    background-image: url('@/assets/imgs/banks/AX.png');
  }
  &.__CA{
    background-image: url('@/assets/imgs/banks/CA.png');
  }
  &.__DC{
    background-image: url('@/assets/imgs/banks/DC.png');
  }
  &.__DS{
    background-image: url('@/assets/imgs/banks/DS.png');
  }
  &.__JC{
    background-image: url('@/assets/imgs/banks/JC.png');
  }
  &.__UP{
    background-image: url('@/assets/imgs/banks/UP.png');
  }
  &.__VI{
    background-image: url('@/assets/imgs/banks/VI.png');
  }
}
</style>

