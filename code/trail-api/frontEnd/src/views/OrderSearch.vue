<template>
  <div class="slh_search_order w-full flex-1 flex flex-col items-center">
    <div class="w-full bg-cover bg-center-bottom bg-no-repeat bg-[url('@/assets/imgs/order_bg.jpg')]">
      <div class="pt-28 pb-16 text-center text-white text-shadow">
        <h3 class="text-2xl !leading-none mb-3">{{ $t('order_search.title') }}</h3>
        <a href="#" class="cursor-pointer underline underline-offset-4 text-sm inline-flex items-center">
          {{ $t('order_info.return_home') }}
          <i class="iconfont icon-arrow-right2 !text-sm"></i>
        </a>
      </div>
    </div>
    <div class="w-full p-4 pb-10 space-y-3">
      <div class="p-2 md:p-3 lg:p-4 xl:p-5 bg-bg_gary-light">
        <div class="p-3 md:p-5 lg:p-6 xl:p-7 pb-5 md:pb-6 lg:pb-8 xl:pb-9 bg-white">
          <img class="w-full mx-auto h-auto" src="@/assets/imgs/order_imgae.png" alt="">
          <form class="p-5 space-y-4" action="/trail-api/booking/order_info">
            <div class="slh-form-item" required>
              <label class="slh-label">{{ $t('common.last_name.label') }}</label>
              <input
                name="last_name"
                :placeholder="$t('common.last_name.placeholder')"
                required
                class="slh-input"
              >
            </div>
            <div class="slh-form-item" required>
              <label class="slh-label">{{ $t('common.reservationId.label') }}</label>
              <input
                name="reservationId"
                :placeholder="$t('common.reservationId.placeholder')"
                required
                class="slh-input"
              >
            </div>
            <div class="slh-form-item">
              <button type="submit" class="submit__btn slh-btn-brand">{{ $t('order_search.search_order') }}</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>