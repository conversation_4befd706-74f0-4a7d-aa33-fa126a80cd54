<template>
  <PageHeader
    :hotelInfo="hotelDetail"
    :loading="hotelInfoLoading"
  >
  </PageHeader>
  <el-result
    icon="warning"
    :title="$t('tip')"
    :sub-title="$t('order_info.no_order')"
    v-if="isNoOrder"
  >
    <template #extra>
      <el-button type="primary" @click="backHome">{{$t('i_see')}}</el-button>
    </template>
  </el-result>
  <div v-else>
    <div class="flex-1 pb-10 md:pb-20">
      <div
        class="slh_order_info container 2xl:max-w-7xl mx-auto mt-7 px-2.5 md:px-0 space-y-3 lg:space-y-4"
        v-loading="isLoading || !orderList"
      >
        <el-tabs
          v-model="activeTab"
          @tab-change="tabChange"
          v-if="orderList && orderList?.length > 1"
        >
          <el-tab-pane
            v-for="(orderInfo, index) in orderList"
            :key="orderInfo?.confirmation"
            :name="orderInfo?.confirmation"
            :label="`${$t('order.room')}${index + 1}`"
          >
          </el-tab-pane>
        </el-tabs>
        <div
          v-for="(orderInfo, index) in orderList"
          :key="orderInfo?.confirmation"
        >
          <template v-if="activeTab === orderInfo?.confirmation">
            <a
              class="slh-detail-sidebar"
              v-if="hotelDetail"
              :href="`${rootPath}/hotel/${hotelDetail?.hotelLink}`"
            >
              {{ $t('common.view_hotel_detail') }}
              <i class="iconfont icon-arrow-right2 leading-none"></i>
            </a>
            <SlhCard :title="$t('order_info.title')">
              <ul
                class="text-sm lg:text-base grid grid-cols-1 gap-1 md:gap-2"
                :class="[`${orderList?.length > 1 ? 'md:grid-cols-2' : 'md:grid-cols-3'}`]"
              >
                <li class="space-x-1">
                  <span class="inline-block text-text-light_2">{{$t('order_info.status')}}</span>
                  <span class="text-text-dark">{{ $t(`order_info.order_status.${orderInfo?.status}`)}}</span>
                </li>
                <li class="space-x-1" v-if="orderList?.length > 1">
                  <span class="inline-block text-text-light_2">{{$t('order.order_no')}}</span>
                  <span class="text-text-dark">{{ orderInfo?.itinerary }}</span>
                </li>
                <li class="space-x-1">
                  <span class="inline-block text-text-light_2">{{$t('confirm_num')}}</span>
                  <span class="text-text-dark">{{ orderInfo?.confirmation }}</span>
                </li>
                <li class="space-x-1">
                  <span class="inline-block text-text-light_2">{{$t('order.order_date')}}</span>
                  <span class="text-text-dark">{{ orderInfo?.bookedAt }}</span>
                </li>
              </ul>
            </SlhCard>
            <SlhCard :title="$t('order_info.stay_info')">
              <div class="space-y-3 lg:space-y-4 divide-y divide-line-light_2">
                <div class="pt-3 lg:pt-4" v-loading="hotelInfoLoading">
                  <h3 class="text-lg text-text-dark">{{hotelDetail?.hotelName}}</h3>
                  <h4 class="text-sm text-text-dark">{{hotelDetail?.hotelNameEn}}</h4>
                  <div class="pt-3 md:pt-4 lg:pt-5  2xl:pt-6">
                    <div class="text-text-light_2 flex items-start text-xs lg:text-sm">
                      <i class="iconfont icon-location text-sm lg:text-base"></i>
                      <p class="flex-1 pl-2">{{ hotelDetail?.address || `${hotelDetail?.countryName}·${hotelDetail?.cityName}` }}</p>
                    </div>
                    <a
                      class="text-text-light_2 flex items-center text-xs lg:text-sm"
                      :href="`tel:${hotelDetail?.phone}`">
                      <i class="iconfont icon-phone text-sm lg:text-base"></i>
                      <p class="flex-1 pl-2">{{ hotelDetail?.phone }}</p>
                    </a>
                  </div>
                </div>
                <div class="pt-3 lg:pt-4">
                  <div class="flex md:hidden">
                    <div class="flex-1">
                      <p class="text-xs text-text-light_2 mb-2">{{$t('searchbar.checkin')}}</p>
                      <p class="text-black">{{orderInfo?.inDate2}}</p>
                    </div>
                    <div class="flex-1">
                      <p class="text-xs text-text-light_2 mb-2">{{$t('searchbar.checkout')}}</p>
                      <p class="text-black">{{orderInfo?.outDate2}}</p>
                    </div>
                  </div>
                  <div class="flex items-end pt-3 md:pt-0">
                    <div class="space-y-2 flex-1">
                      <h3 class="text-sm md:text-base">
                        {{ orderInfo?.roomName || orderInfo?.roomNameEn }}
                        <span class="text-xs text-text-light_2">{{orderInfo.rateName}}</span>
                      </h3>
                      <p class="text-xs text-text-light_2" v-if="isSLHClubCodes(orderInfo?.rateCode)">{{$t('order.member_price')}}</p>
                      <p class="text-xs text-text-dark">
                        {{$t('common.aldult')}}{{ orderInfo?.adultsPerRoom }}，
                        <span v-if="orderInfo?.childrenPerRoom">{{$t('common.child')}}{{ orderInfo?.childrenPerRoom }}，</span>
                        {{ orderInfo?.nights }}{{$t('common.night')}}
                      </p>
                      <p class="text-xs text-text-dark hidden md:block">
                        {{ orderInfo?.inDate }} {{$t('common.to')}} {{ orderInfo?.outDate }}
                        <span>{{ orderInfo?.nights }}{{$t('common.night')}}</span>
                      </p>
                    </div>
                    <div class="space-y-1 text-right">
                      <p class="text-lg md:text-xl leading-none text-text-dark">
                        <SlhPrice :price="orderInfo?.cnyEstimatedTotal100" />
                      </p>
                      <p class="text-xs text-text-light_2 space-x-2">
                        <span>{{$t('order.tax_included')}}</span>
                      </p>
                    </div>
                  </div>
                  <p class="text-primary text-xs md:text-sm pt-3 lg:pt-4">
                    {{$t('order_info.checkin_tip', {checkin_after: orderInfo?.checkInAfter, checkout_before: orderInfo?.checkOutBefore})}}
                  </p>
                </div>
              </div>
            </SlhCard>
            <SlhCard :title="$t('order_info.cost_info')">
              <div class="flex flex-col md:flex-row items-center space-y-3 lg:space-y-0 divide-y md:divide-y-0 divide-line-light_2">
                <div class="w-full md:w-1/2 text-xs lg:text-sm space-y-2">
                  <p
                    class="space-x-1"
                    v-for="(roomRate, index) in orderInfo?.roomRateDetails"
                  >
                    <span class="inline-block w-[6em] text-right text-text-light_2">
                      {{$t('order_info.night_index', {index: index+1})}}
                    </span>
                    <span class="text-text-dark">
                      <SlhPrice :price="roomRate?.cnyAmount100" />
                    </span>
                  </p>
                  <p class="space-x-1">
                    <span class="inline-block text-text-light_2 text-right">{{$t('order_info.taxes_and_service')}}</span>
                    <span class="text-text-dark">
                      <SlhPrice :price="orderInfo?.cnyEstimatedTotal100 - orderInfo?.cnyTotalRate100" />
                    </span>
                  </p>
                </div>
                <div class="w-full md:w-1/2 pt-2 lg:pt-0">
                  <p class="flex justify-between items-end">
                    <span class="text-sm lg:text-base text-text-dark">{{$t('order.total_price')}}</span>
                    <span class="text-lg lg:text-2.1xl text-primary">
                      <SlhPrice :price="orderInfo?.cnyEstimatedTotal100" />
                    </span>
                  </p>
                  <p class="flex justify-between text-xs lg:text-sm text-text-light_2 mt-1">
                    <span>
                      <span v-if="isSLHClubCodes(orderInfo?.rateCode)">{{$t('order.member_price')}}</span>
                    </span>
                    <span>{{$t('order.tax_included')}}</span>
                  </p>
                </div>
              </div>
            </SlhCard>
            <SlhCard :title="$t('order_info.user_info')">
              <ul class="text-xs lg:text-sm flex flex-wrap">
                <li class="w-1/2 space-x-1">
                  <span class="inline-block w-[4em] text-right text-text-light_2">{{$t('common.last_name.label')}}:</span>
                  <span class="text-text-dark">{{ orderInfo?.guestLastName }}</span>
                </li>
                <li class="w-1/2 space-x-1">
                  <span class="inline-block w-[4em] text-right  text-text-light_2">{{$t('common.first_name.label')}}:</span>
                  <span class="text-text-dark">{{ orderInfo?.guestFirstName }}</span>
                </li>
                <li class="w-full lg:w-1/2 pt-3 space-x-1">
                  <span class="inline-block w-[4em] text-right text-text-light_2">{{$t('common.email.label')}}:</span>
                  <span class="text-text-dark">{{ orderInfo?.guestEmail }}</span>
                </li>
                <li class="w-full lg:w-1/2 pt-3 space-x-1">
                  <span class="inline-block w-[4em] text-right text-text-light_2">{{$t('common.phone.label')}}:</span>
                  <span class="text-text-dark">{{ orderInfo?.guestPhone }}</span>
                </li>
                <!-- <li class="w-full lg:w-1/2 pt-3">
                  <span class="inline-block w-[4em] text-right text-text-light_2">地址:</span>
                  <span class="text-text-dark">中国，陕西，西安，创业广场</span>
                </li>
                <li class="w-full lg:w-1/2 pt-3">
                  <span class="inline-block w-[4em] text-right text-text-light_2">邮编:</span>
                  <span class="text-text-dark">710000</span>
                </li>
                <li class="w-full lg:w-1/2 pt-3">
                  <span class="inline-block w-[4em] text-right text-text-light_2">居住国:</span>
                  <span class="text-text-dark">中国</span>
                </li> -->
              </ul>
            </SlhCard>
            <SlhCard :title="$t('order_info.refund_info')">
              <div class="flex">
                <i class="iconfont icon-notice text-base leading-5 text-primary"></i>
                <div class="flex-1 pl-1.5">
                  <h3 class="text-sm text-primary mb-1">{{$t('order.cancel_tip')}}</h3>
                  <p class="text-xs lg:text-sm text-text-light">{{orderInfo?.cancelRuleText}}</p>
                </div>
              </div>
            </SlhCard>
            <SlhCard :no-bg="true" :has-inner="false">
              <div class="space-y-2.5 md:space-y-0 md:space-x-6 md:flex">
                <template v-if="orderInfo?.status !== 'confirmedCancellation'">
                  <SlhButton
                    class="w-full"
                    type="brand"
                    :href="getEditOrderUrl(orderInfo)"
                  >
                    {{$t('order_info.change_order')}}
                  </SlhButton>
                  <SlhButton
                    class="w-full"
                    @click="resendConfirmation"
                    :loading="resendLoading"
                    :disabled="resendLoading"
                  >
                    {{$t('resend_confirmation')}}
                  </SlhButton>
                  <SlhButton
                    class="w-full"
                    @click="cancelDialog = true"
                  >
                    {{$t('order_info.cancel_order')}}
                  </SlhButton>
                </template>
                <SlhButton
                  class="w-full"
                  :href="rootPath || '/'"
                >
                  {{$t('order_info.return_home')}}
                </SlhButton>
              </div>
            </SlhCard>
          </template>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="cancelDialog"
      :title="$t('order_info.cancel_dialog.title')"
      :width="isMobile ? '90%' : '500'"
    >
      <div class="flex flex-col md:flex-row flex-wrap">
        <div
          class="w-full py-3 md:px-4 md:py-2 space-y-3 lg:space-y-4"
          v-for="(orderInfo, index) in orderList"
          :key="orderInfo?.confirmation"
        >
          <template v-if="activeTab === orderInfo?.confirmation">
            <!-- <div class="space-x-1">
              <el-tag effect="dark">{{`${$t('order.room')}${index + 1}`}}</el-tag>
            </div> -->
            <div class="space-x-1">
              <span class="text-text-light_2">{{$t('order.order_no')}}</span>
              <span class="text-primary">{{ orderInfo?.confirmation }}</span>
            </div>
            <div class="space-x-1">
              <span class="text-text-light_2">{{$t('order.order_date')}}</span>
              <span class="text-primary">{{ orderInfo?.bookedAt }}</span>
            </div>
            <div class="flex space-x-1">
              <i class="iconfont icon-notice text-base leading-5 text-primary"></i>
              <span class="flex-1 pl-1.5 text-xs lg:text-sm text-text-light_2">
                {{orderInfo?.cancelRuleText}}
              </span>
            </div>
          </template>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialog = false">
            {{ $t('order_info.cancel_dialog.cancel') }}
          </el-button>
          <el-button
            type="primary"
            :loading="cancelIsLoading"
            :disabled="cancelIsLoading"
            @click="cancelOrder"
          >
            {{ $t('order_info.cancel_dialog.ok') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { useI18n } from 'vue-i18n'
import PageHeader from '@/components/PageHeader.vue'
import SlhButton from '@/components/SlhButton.vue'
import SlhCard from '@/components/SlhCard.vue'
import SlhPrice from '@/components/SlhPrice.vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh'
import 'dayjs/locale/zh-tw'
import ppo from 'ppo'
import { ref, watch, computed, } from 'vue'
import { ElMessageBox} from 'element-plus'
import { DATE_FORMAT, DATE_FORMAT2, CHAIN_CODE,} from '@/config/index'
import { useRequest } from '@/utils/request'
import useAppStore from '@/stores/app'
import {isSLHClubCodes} from '@/utils/util'

dayjs.locale('zh')

const { isMobile, rootPath } = useAppStore()
const { t } = useI18n()
const chainCode = CHAIN_CODE

const reservationId = ppo.getUrlParam('reservationId')
const lastName = ppo.getUrlParam('lastName')
const cancelDialog = ref(false)
const activeTab = ref('')
const isNoOrder = ref(false)

const { data, isLoading, execute } = useRequest({
  url: '/cms-web/v1/getReservationDetailByIdAndLastName',
})
const { data: hotelInfo, isLoading: hotelInfoLoading, execute: hotelInfoExecute } = useRequest({
  url: '/cms-web/v1/getHotelDetails',
})
if(reservationId && lastName){
  execute({
    params: {
      chainCode,
      reservationId,
      lastName,
    }
  })
} else {
  isNoOrder.value = true
  // ElMessageBox.alert(t('order_info.error_tip'), t('tip'), {
  //   confirmButtonText: t('ok'),
  //   callback: (action) => {
  //     window.location.href = rootPath
  //   },
  // })
}
watch(
  () => data.value,
  (value) => {
    // console.log('orderInfo', value)
    if(value){
      if(value.success && value?.data?.length > 0){
        const firstOrder = value?.data[0]
        if(firstOrder){
          const { hotelCode, confirmation } = value?.data[0]
          activeTab.value = confirmation
          hotelCode && hotelInfoExecute({
            params: {
              chainCode,
              hotelCodes:hotelCode,
            }
          })
        }
      } else {
        isNoOrder.value = true
        // ElMessageBox.alert(t('order_info.no_order'), t('tip'), {
        //   confirmButtonText: t('i_see'),
        //   callback: (action) => {
        //     window.location.href = rootPath
        //   },
        // })
      }
    }
  },
  {
    immediate: true
  }
)

const orderList = computed(() => {
  const bookInfo = data?.value?.data
  console.log('bookInfo', bookInfo)
  return bookInfo ? bookInfo?.map(item => {
    const inDate = dayjs(item?.inDate)
    const outDate = dayjs(inDate).add(item?.nights, 'day')
    return {
      ...item,
      bookedAt: dayjs(item?.bookedAt).format(DATE_FORMAT),
      inDate: inDate.format(DATE_FORMAT),
      inDate2: inDate.format(DATE_FORMAT2),
      outDate: outDate.format(DATE_FORMAT),
      outDate2: outDate.format(DATE_FORMAT2),
    }
  }) : null;
});

const hotelDetail = computed(() => {
  const _hotelInfo = hotelInfo.value?.data?.length && hotelInfo.value?.data[0] || {};
  return {
    ..._hotelInfo,
    hotelBanner: _hotelInfo?.banners && _hotelInfo?.banners[0]?.sourceUrl ? `${window.IMG_PROXY_URL}?url=${encodeURIComponent(_hotelInfo?.banners[0]?.sourceUrl)}` : (_hotelInfo?.coverUrl || ''),
  };
});

const getEditOrderUrl = (ordeInfo) => {
  const {id, hotelCode, nights, rooms, adultsPerRoom, childrenPerRoom, childrenAges, reservationNumber, confirmation} = ordeInfo
  const inDate = dayjs(ordeInfo?.inDate)
  const outDate = dayjs(inDate).add(nights, 'day')
  let url = `${rootPath}/booking/rate_list?`
  let params = {
    hotelCode: hotelCode,
    reservationMainId: confirmation,
    reservationId: confirmation,
    lastName,
    indate:inDate.format(DATE_FORMAT),
    outdate:outDate.format(DATE_FORMAT),
    rooms,
    adultsPerRoom,
    childrenPerRoom,
  }
  if(childrenPerRoom && childrenAges){
    params.childrenAges = childrenAges
  }
  Object.keys(params).forEach((key) => {
    if(params[key]){
      url += `${key}=${params[key]}&`
    }
  })
  // console.log('editOrderUrl===params', params)
  // console.log('editOrderUrl===url', url)
  return url;
};

const { isLoading: cancelIsLoading, execute:cancelOrderExecute } = useRequest({
  url: '/cms-web/v1/cancelReservation',
})

const cancelOrder = async () => {
  const { hotelCode } = data.value?.data[0]
  const curOrder = orderList?.value.find(order => order.confirmation === activeTab.value)
  const { data:cancelOrderData } = await cancelOrderExecute({
    params: {
      chainCode,
      reservationId: curOrder?.confirmation,
      hotelCode,
    }
  })
  // console.log('cancelOrder==data', data)
  if(!cancelOrderData.value?.success){
    ElMessage.error(cancelOrderData.value?.errorMsg)
    return false
  } else {
    ElMessageBox.alert(t('order_info.cancel_success'), t('tip'), {
      callback: (action) => {
        // window.location.href = rootPath || '/'
        window.location.reload();
      },
    })
  }
}

const { isLoading: resendLoading, execute:resendExecute } = useRequest({
  url: '/cms-web/v1/sendReservationMailAgain',
})

const resendConfirmation = async () => {
  const curOrder = orderList?.value.find(order => order.confirmation === activeTab.value)
  const { data:resendData } = await resendExecute({
    params: {
      chainCode,
      reservationId: curOrder?.confirmation,
    }
  })
  if(!resendData.value?.success){
    ElMessage.error(resendData?.value?.errorMsg || t('resend_fail'))
    return false
  } else {
    ElMessageBox.alert(t('order.send_to_email'), t('tip'), {
      confirmButtonText: t('common.ok'),
      callback: (action) => {
      },
    })
  }
}

const tabChange = (tab) => {
  activeTab.value = tab
}

const backHome = () => {
  window.location.href = rootPath || '/'
}
</script>