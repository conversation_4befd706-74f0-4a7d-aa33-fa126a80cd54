<template>
  <div class="slh_leave w-full h-full pt-[100px] md:pt-[150px] lg:pt-[200px] pb-6 md:pb-8 lg:pb-11 bg-cover bg-center-top bg-no-repeat bg-[url('@/assets/imgs/login_bg_m.png')] md:bg-[url('@/assets/imgs/login_bg_pc.png')]">
    <div class="w-full px-6.5 md:px-0 md:w-187.5 mx-auto">
      <div class="w-full px-4 py-12.5 md:py-16 lg:py-20 xl:py-22.5 bg-white">
        <div class="flex flex-col justify-center text-center">
          <i class="iconfont icon-tip text-6xl lg:text-7xl leading-none text-text-light_2"></i>
          <p class="text-xm md:text-sm lg:text-base text-text-light_2 mt-4">{{$t('before_leave.leave_tip')}}</p>
        </div>
        <div class="space-y-1 text-center text-base md:text-xl mt-6">
          <h3 class="text-primary flex flex-col md:flex-row md:justify-center">
            <span>{{$t('common.title')}}</span>
            <span>（www.slh.com）</span>
          </h3>
          <p class="text-text-dark">{{$t('before_leave.over_tip')}}</p>
        </div>
        <div class="pt-11 md:pt-14 xl:pt-16 lg:pt-20 text-xs md:text-sm text-center">
          <p class="mb-4"><span class="text-primary">{{ jumpCountdown }}s</span> {{$t('before_leave.auto_jump')}}</p>
          <a :href="redirectUrl" class="text-text-light cursor-pointer underline underline-offset-4">{{$t('before_leave.no_jump_click')}}</a>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import ppo from 'ppo'
import { ref } from 'vue'
import { useIntervalFn } from '@vueuse/core'
import { JUMP_COUNTDOWN } from '@/config/index'
const redirect = ppo.getUrlParam('redirect')
console.log('redirect', redirect)

const jumpCountdown = ref(JUMP_COUNTDOWN);
const redirectUrl = ref(redirect ? decodeURIComponent(redirect) : '/index')

const { pause } = useIntervalFn(() => {
  jumpCountdown.value = jumpCountdown.value - 1
  if(jumpCountdown.value <= 0){
    pause()
    window.location.href = redirectUrl.value
    console.log('倒计时结束')
  }
}, 1000)

</script>
