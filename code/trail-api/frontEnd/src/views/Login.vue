<template>
  <div class="slh_login w-full pt-[100px] md:pt-[150px] lg:pt-[200px] pb-6 md:pb-8 lg:pb-11 bg-cover bg-center-top bg-no-repeat bg-[url('@/assets/imgs/login_bg_m.png')] md:bg-[url('@/assets/imgs/login_bg_pc.png')]">
    <div class="w-full md:w-187.5 md:bg-white md:py-12.5 md:px-17.5 mx-auto">
      <h1 class="text-xl  md:2xl lg:2.2xl mb-6 md:mb-10 text-white md:text-black text-center">{{$t('login.title')}}</h1>
      <div class="w-full p-6 md:px-0">
        <div class="px-5 py-4 md:px-0 md:py-0 bg-white">
          <div class="w-full" v-if="!submitSuccess">
            <div class="w-full mb-7.5">
              <el-radio-group
                class="w-full"
                v-model="verifyWay"
                :size="elementPlusSize"
              >
                <el-radio-button
                  class="w-1/2"
                  v-for="item in verifyWays"
                  :key="item.value"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio-button>
              </el-radio-group>
            </div>
            <el-form
              ref="phoneFormRef"
              :model="phoneForm"
              :rules="phoneFormRules"
              label-width="auto"
              label-position="top"
              require-asterisk-position="right"
              :size="elementPlusSize"
              v-show="verifyWay === 'phone'"
            >
              <el-form-item
                :label="$t('common.phone.label')"
                prop="phone"
              >
                <div class="w-full flex space-x-3 md:space-x-5">
                  <el-select v-model="phoneForm.phoneRefix" class="w-25 md:w-45">
                    <el-option
                      v-for="item in COUNTRIES"
                      :key="item.name"
                      :label="item.code"
                      :value="item.code"
                    />
                  </el-select>
                  <div class="flex-1">
                    <el-input
                      v-model="phoneForm.phone"
                      :placeholder="$t('common.phone.placeholder')"
                    />
                  </div>
                </div>
              </el-form-item>
              <el-form-item
                :label="$t('common.verify_code.label')"
                prop="verifyCode"
              >
                <div class="flex w-full space-x-3 md:space-x-5">
                  <div class="flex-1">
                    <el-input
                      v-model.number="phoneForm.verifyCode"
                      :placeholder="$t('common.verify_code.placeholder')"
                    />
                  </div>
                  <el-button
                    type="primary"
                    :loading="sendCodeing"
                    :disabled="sendCodeing || isActive"
                    @click="sendCode"
                  >
                    {{
                      isActive ?
                      `${coolingTime}${$t('common.verify_code.retry')}` : 
                        sendCodeed ?
                        $t('common.verify_code.resend_code') :
                        $t('common.verify_code.send_code')
                    }}
                  </el-button>
                </div>
              </el-form-item>
              <el-form-item>
                <p class="text-black text-xs md:text-sm text-center inline-flex items-center space-x-1">
                  <el-checkbox v-model="rememberMe">{{ $t('login.remember_me') }}</el-checkbox>
                </p>
              </el-form-item>
              <el-form-item>
                <SlhButton
                  class="w-full"
                  @click="submitPhoneForm"
                  type="brand"
                  :loading="phoneFormLoading"
                  :disabled="phoneFormLoading"
                >
                {{ $t('login.authentication') }}
                </SlhButton>
              </el-form-item>
            </el-form>
            <el-form
              ref="emailFormRef"
              :model="emailForm"
              :rules="emailFormRules"
              label-width="auto"
              label-position="top"
              require-asterisk-position="right"
              :size="elementPlusSize"
              v-show="verifyWay === 'email'"
            >
              <el-form-item
                :label="$t('common.email.label')"
                prop="email"
              >
                <el-input
                  v-model="emailForm.email"
                  :placeholder="$t('common.email.placeholder')"
                />
              </el-form-item>
              <el-form-item
                :label="$t('common.password.label')"
                prop="password"
              >
                <el-input
                  type="password" 
                  v-model="emailForm.password"
                  :label="$t('common.password.placeholder')"
                />
              </el-form-item>
              <el-form-item>
                <div class="w-full text-xs md:text-sm">
                  <p class="text-right">
                    <span class="text-primary cursor-pointer underline underline-offset-4">{{$t('login.forget_password')}}</span>
                  </p>
                  <p class="text-left">
                    <el-checkbox v-model="rememberMe">{{ $t('login.remember_me') }}</el-checkbox>
                  </p>
                </div>
              </el-form-item>
              <el-form-item>
                <SlhButton
                  class="w-full"
                  @click="submitEmailForm"
                  type="brand"
                  :loading="emailFormLoading"
                  :disabled="emailFormLoading"
                >
                  {{ $t('login.authentication') }}
                </SlhButton>
              </el-form-item>
            </el-form>
            <p class="text-black pt-2 text-xs md:text-sm text-center">
              {{ $t('login.unregistered') }}, <a :href="`${rootPath}/auth/register`" class="text-primary cursor-pointer underline underline-offset-4">{{ $t('login.quick_registration') }}</a>
            </p>
          </div>
          <div class="space-y-4" v-else>
            <div class="py-4 flex flex-col justify-center text-center">
              <i class="iconfont icon-copy text-6xl lg:text-7xl leading-none text-success"></i>
              <h3 class="text-xl lg:text-2xl text-black my-4">{{ $t('login.authentication_success') }}</h3>
            </div>
            <p class="text-black pt-5 md:pt-8 xl:pt-10 lg:pt-12.5 text-xs md:text-sm text-center">
              <span class="text-primary">{{ jumpCountdown }}</span> {{ $t('common.after_second') }},
              <a href="#" class="text-black cursor-pointer underline underline-offset-4">{{ $t('common.back_prev') }}</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { useI18n } from 'vue-i18n'
import { useTimeoutFn, useIntervalFn } from '@vueuse/core'
import { ref, reactive, computed, } from 'vue'
import SlhButton from '@/components/SlhButton.vue'
import { COUNTRIES, COOLING_TIME, JUMP_COUNTDOWN } from '@/config/index'
import useAppStore from '@/stores/app'
import { ElMessage } from 'element-plus'

const { elementPlusSize, rootPath } = useAppStore()
const { t } = useI18n()
const verifyWays = [
  {
    value: 'phone',
    label: t('login.verify_phone')
  },
  {
    value: 'email',
    label: t('login.verify_email')
  }
]
const  verifyWay = ref(verifyWays[0].value)

const phoneFormRef = ref(null)
const coolingTime = ref(COOLING_TIME)
const sendCodeing = ref(false)
const sendCodeed = ref(false)
const phoneFormLoading = ref(false)

const rememberMe = ref(false)
const submitSuccess = ref(false)

const phoneForm = reactive({
  phoneRefix:COUNTRIES[0].code,
  phone:'',
  verifyCode:'',
})

const phoneFormRules = {
  phone: [
    {required: true, message: t('common.phone.required'), trigger: ['blur', 'change'], },
    {pattern: /^(?:(?:\+|00)86)?1\d{10}$/, message: t('common.phone.error'), trigger: ['blur', 'change']},
  ],
  verifyCode: [
    {required: true, message: t('common.verify_code.required'), trigger: ['blur', 'change'],},
    {type: 'number', message: t('common.verify_code.error'), trigger: ['blur', 'change']},
  ],
}

// 发动验证码计时器
const { pause, resume, isActive } = useIntervalFn(() => {
  coolingTime.value = coolingTime.value - 1
  if(coolingTime.value <= 0){
    pause()
    coolingTime.value = COOLING_TIME
    console.log('倒计时结束')
  }
}, 1000)
pause()

// 发送验证码
const sendCode = () => {
  phoneFormRef.value.validateField('phone', (valid) => {
    if(valid){
      sendCodeing.value = true
      ElMessage.warning('正在开发中，敬请期待...')
      return false
      useTimeoutFn(() => {
        sendCodeing.value = false
        sendCodeed.value = true
        resume()
      }, 2000)
    }
  });
}

// 手机号动态验证
const submitPhoneForm = () => {
  phoneFormRef.value.validate(valid => {
    if(valid){
      console.log('手机号动态验证')
      ElMessage.warning('正在开发中，敬请期待...')
      return false
      phoneFormLoading.value = true
      submitSuccess.value = true
      jumpResume()
    }
  });
}

const emailFormRef = ref(null);
const emailFormLoading = ref(false);

const emailForm = reactive({
  email:'',
  password:'',
})

const emailFormRules = {
  email: [
    {required: true, message: t('common.email.required'), trigger: ['blur', 'change'],},
    {pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: t('common.email.error'), trigger: ['blur', 'change']},
  ],
  password: [
    {required: true, message: t('common.password.required'), trigger: ['blur', 'change'],},
    { min: 8, max: 16, message: t('common.password.error_length'), trigger: ['blur', 'change'] },
    { pattern: /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/, message: t('common.password.error'), trigger: ['blur', 'change'] }
  ],
}

const jumpCountdown = ref(JUMP_COUNTDOWN) 
const { pause:jumpPause, resume:jumpResume } = useIntervalFn(() => {
  jumpCountdown.value = jumpCountdown.value - 1
  if(jumpCountdown.value <= 0){
    jumpPause()
    window.location.href = '/user_center'
    console.log('倒计时结束')
  }
}, 1000)
jumpPause()

// 邮箱密码验证
const submitEmailForm = () => {
  emailFormRef.value.validate(valid => {
    if(valid){
      console.log('提交表单')
      ElMessage.warning('正在开发中，敬请期待...')
      return false
      emailFormLoading.value = true
      useTimeoutFn(() => {
        emailFormLoading.value = false
        submitSuccess.value = true
        jumpResume()
      }, 2000)
    }
  });
}

</script>