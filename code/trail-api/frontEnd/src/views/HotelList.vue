<template>
  <div class="com-hotel-list" ref="hotelListRef" v-if="list.length">
    <div class="hotel__item" v-for="item in list">
      <a class="hotel__img" :href="`${rootPath}/hotel/${item.hotelLink}${urlSearch ? `?${urlSearch}` : ''}`">
        <img
          class="hotel__img__ele aspect-ele"
          :src="`${rootPath}/static/assets/imgs/hotel_img_loading.jpg`" 
          v-if="!item.coverUrl"
          :alt="item.hotelName"
        >
        <img
          v-else
          class="hotel__img__ele aspect-ele"
          :src="`${rootPath}/static/assets/imgs/hotel_img_loading.jpg`"
          :data-src="item.coverUrl"
          lazy="true"
          :alt="item.hotelName"
        >
      </a>
      <div class="hotel__info">
        <h3 class="hotel__name slh-font-sunti"><a :href="`${rootPath}/hotel/${item.hotelLink}?${urlSearch}`">{{ item.hotelName }}</a></h3>
        <h4 class="hotel__en__name">{{ item.hotelNameEn }}</h4>
        <p class="hotel__address">
          <i class="iconfont icon-location address__icon"></i>
          <span class="pl-2">{{ item.countryName }} {{ item.cityName }}</span>
        </p>
        <div class="hotel__actions">
          <a
            class="slh-btn-brand w-full"
            @click.prevent="handleBooking(item)"
          >
            {{ $t('hotel_list.view_price') }}
          </a>
          <a class="slh-btn-default w-full" :href="`${rootPath}/hotel/${item.hotelLink}${urlSearch ? `?${urlSearch}` : ''}`">{{ $t('hotel_list.view_detail') }}</a>
        </div>
      </div>
    </div>
  </div>
  <el-empty :image-size="100" v-else/>
</template>
<script setup>
import useAppStore from '@/stores/app'
import {defineProps, watch, ref, onMounted, nextTick, handleError} from "vue"
import { useWindowScroll, useThrottleFn} from '@vueuse/core'
import { getUrlSearch } from '@/utils/util'

const { rootPath } = useAppStore()
const hotelListRef = ref(null)
const urlSearch = ref(getUrlSearch()) // 链接参数

const {list} = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})

const { y } = useWindowScroll()
const screenHeight = window.screen.height
const setImgSrc = (value) => {
  const hotelItems = document.querySelectorAll('.hotel__item')
  hotelItems.forEach((item, index) => {
    const top = item.offsetTop
    // console.log('value===',index, value, top, (top - value), screenHeight)
    if((top - value) <= screenHeight){
      const imgEle = item.querySelector('.hotel__img__ele')
      const isLazy = imgEle.getAttribute('lazy')
      const imgSrc = imgEle.getAttribute('data-src')
      if(isLazy){
        // console.log(`滚动了==${index}==${isLazy}==${imgSrc}`)
        imgEle.setAttribute('src', imgSrc)
        imgEle.setAttribute('lazy', false)
      }
    }
  })
}

onMounted(() => {
  setTimeout(() => {
    setImgSrc(y.value)
  })
})

watch(
  y,
  useThrottleFn((value) => {
    setImgSrc(value)
  }),
  {
    immediate: true
  }
)

// 预订按钮的点击事件
const handleBooking = (item) => {
  window?.SlhGlobal?.gtagEvent('hotel_list', {
    hotelCode: item.hotelCode || '',
    hotelName: item.hotelNameEn || '',
  })
  window.location.href = `${rootPath}/booking/rate_list?hotelCode=${item.hotelCode}&${urlSearch.value}`
}

</script>
<style lang="scss" scoped>
.com-hotel-list{
  @apply grid grid-cols-1 md:grid-cols-3 gap-5 md:gap-6 xl:gap-7;
  .hotel__item{
    @apply bg-white text-xs md:text-sm text-text-light flex flex-col;
    .hotel__img{
      @apply w-full aspect-w-380 aspect-h-255 object-cover;
      .hotel__img__ele{
        background:url('@/assets/imgs/loading.gif') no-repeat center center;
        background-size:30px 30px;
        @apply bg-bg_gary-light;
      }
    }
    .hotel__name{
      @apply text-lg md:text-xl lg:text-2xl text-black;
    }
    .hotel__info{
      @apply p-4 md:p-4 lg:p-5 border border-t-0 border-line-light_3 flex-1 flex flex-col;
    }
    .hotel__en__name{
      @apply mt-1;
    }
    .hotel__address{
      @apply flex items-center leading-none mt-4 md:mt-5;
      .address__icon{
        @apply text-base lg:text-xl leading-none;
      }
    }
    .hotel__actions{
      @apply md:flex-1 flex items-end space-x-2 mt-4 md:mt-5;
    }
  }
}
</style>

