<template>
  <el-config-provider :locale="eleLocale">
    <Searchbar
      :role="role"
      :queryParams="queryParams"
      :destination="destination"
      :disabledKeyword="disabledKeyword"
      @search="hadleSearch"
      @update:queryParams="updateQueryParams"
      @update:destination="updateCurDestination"
      class="hidden md:block"
    />
    <SearchbarMobile
      :role="role"
      :destination="destination"
      :queryParams="queryParams"
      :disabledKeyword="disabledKeyword"
      @search="hadleSearch"
      @update:destination="updateCurDestination"
      class="md:hidden"
    />
  </el-config-provider>
</template>

<script setup>
import {ref, watch, reactive} from 'vue'
import { useI18n } from 'vue-i18n'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import zhTw from 'element-plus/dist/locale/zh-tw.mjs'
import { useWindowSize, useThrottleFn } from '@vueuse/core'
import Searchbar from '@/components/Searchbar.vue'
import SearchbarMobile from '@/components/SearchbarMobile.vue'
import { ScreenEnum } from '@/enums/appEnums'
import { DEFAULT_QUERY_PARAMS, DESTINATION_LIST } from '@/config/index'
import useAppStore from '@/stores/app'

const { rootPath } = useAppStore()

const { locale, t } = useI18n()

// 查询参数
const defaultQueryParams = {
  ...DEFAULT_QUERY_PARAMS,
}

const curDestination = ref(null)
const queryParams = reactive(defaultQueryParams)

const props = defineProps({
  role: String,
  hotelCode: String,
  hotelName: {
    type: String,
    default: '',
  },
  keyword: {
    type: String,
    default: '',
  },
  disabledKeyword: {
    type: Boolean,
    default: false,
  },
})
const eleLocale = ref(locale.value === 'zh-tw' ? zhTw : zhCn)
const role = ref(props.role || '')
let destination = reactive([])
destination = DESTINATION_LIST.map(item => {
  let desItem = {
    label: t(`searchbar.destination_list.${item.key}`),
    key: item.key,
  }
  switch (item.key) {
    case 'hot':
      desItem.cities = window?.hotSiteCities || []
      desItem.countries = window?.hotSiteCountries || []
      break
    case 'China':
      desItem.cities = window?.domestic ? window?.domestic?.cities : []
      break
    case 'other':
      desItem.continents = (window?.continents || []).filter(continent => {
        const isInList = DESTINATION_LIST.find(cell => cell.key === continent.nameEn)
        return !isInList
      }).map(continent => ({
        title: continent.name,
        cities: continent.hotCities,
        countries: continent.countries,
      }))
      break
    default:
      const curItem = (window?.continents || []).find(continent => continent.nameEn === item.key)
      if(curItem){
        desItem.cities = curItem.hotCities
        desItem.countries = curItem.countries
      }
      break
  }
  return desItem
})

const { isMobile, setMobile } = useAppStore()
const { width } = useWindowSize()
const emits = defineEmits(['search'])

watch(
  props,
  (value) => {
    if(value?.keyword){
      queryParams.keyword = value?.keyword
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

watch(
  width,
  useThrottleFn((value) => {
    setMobile(value <= ScreenEnum.MD)
  }),
  {
    immediate: true
  }
)

const updateQueryParams = (data) => {
  Object.keys(data).map(key => {
    queryParams[key] = data[key]
  })
}

const updateCurDestination = (data) => {
  curDestination.value = data
}

const hadleSearch = (data) => {
  console.log('hadleSearch === data', props.role, data)
  data && updateQueryParams(data)
  if(props.role === 'booking'){
    // console.log('hadleSearch === data === booking', props.role, data)
    emits('search', queryParams)
  } else {
    let url = rootPath
    if(props.role === 'hotel'){
      window?.SlhGlobal?.gtagEvent('hotel_detail_booking', {
        hotelCode: props.hotelCode || '',
        hotelName: props.hotelName || '',
      })
      url = `${url}/booking/rate_list?hotelCode=${props.hotelCode}`
      // console.log('hadleSearch === queryParams', queryParams)
      // console.log('hadleSearch === url', url)
    } else {
      if(!queryParams.keyword || !curDestination.value){
        ElMessage({
          message: t('searchbar.search_tip'),
          type: 'warning',
        })
        return false
      }
      // console.log('updateCurDestination === data', curDestination.value)
      switch(curDestination.value.type){
        case 'countries':
          url = `${url}/country/${curDestination.value.data?.countryLink || curDestination.value.data?.link}?`
          break;
        case 'cities':
          url = `${url}/city/${curDestination.value.data.cityLink}/hotel?`
          break;
        case 'hotels':
          url =  `${url}/hotel/${curDestination.value.data.hotelLink}?`
          break;
      }
    }
    Object.keys(queryParams).map(key => {
      url += `&${key}=${key === 'keyword' ? encodeURIComponent(queryParams[key]) : queryParams[key]}`
    })
    console.log('hadleSearch === url', url)
    window.location.href = url
  }
}

</script>
<style scoped>
</style>