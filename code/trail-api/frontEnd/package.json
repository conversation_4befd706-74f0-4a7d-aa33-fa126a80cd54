{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "rimraf ../build && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@tailwindcss/forms": "^0.5.6", "@vitejs/plugin-legacy": "^5.0.0", "@vueuse/core": "^10.3.0", "@vueuse/integrations": "^10.3.0", "axios": "^1.4.0", "dayjs": "^1.11.9", "element-plus": "^2.3.9", "jquery": "^3.7.0", "node-gyp": "^10.2.0", "nprogress": "^0.2.0", "photoswipe": "^5.4.2", "pinia": "^2.1.6", "pinyin": "^3.0.0-alpha.5", "ppo": "^1.3.7", "rimraf": "^6.0.1", "swiper": "^10.2.0", "vue": "^3.3.4", "vue-i18n": "^9.2.2", "vue3-preview": "^1.0.5"}, "devDependencies": {"@rollup/plugin-inject": "^5.0.3", "@tailwindcss/aspect-ratio": "^0.4.2", "@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "^10.4.15", "fs-extra": "^11.1.1", "laravel-vite-plugin": "^0.8.1", "postcss": "^8.4.28", "sass": "^1.66.0", "tailwindcss": "^3.3.3", "terser": "^5.21.0", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}