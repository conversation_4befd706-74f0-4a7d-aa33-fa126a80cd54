import { fileURLToPath, URL } from 'url'
import { resolve } from 'path'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import inject from '@rollup/plugin-inject'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import laravel from 'laravel-vite-plugin'
import legacy from '@vitejs/plugin-legacy'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const isProd = env.NODE_ENV === 'production'
  console.log('isProd', isProd)
  return {
    server: {
      host: '0.0.0.0', // 监听所有网络接口
      port: env.PORT // 你的端口号
    },
    plugins: [
      vue(),
      legacy({
        targets: ['defaults', 'not IE 11'],
      }),
      inject({
        $: "jquery",  // 这里会自动载入 node_modules 中的 jquery   jquery全局变量
        jQuery: "jquery",
        "windows.jQuery": "jquery",
        ppo: "ppo",
        "windows.ppo": "ppo",
      }),
      laravel({
        publicDirectory: "../",
        hotFile: "./hot",
        input: [
          'src/css/global.scss',
          'src/css/tailwindcss.scss',
          'src/css/page-article.scss',
          'src/css/index.scss',
          'src/css/club.scss',
          'src/css/hotel-detail.scss',
          'src/css/page-our-club.scss',
          'src/global.js',
          'src/hotel-detail.js',
          'src/pages/before_leave/main.js',
          'src/pages/index/main.js',
          'src/pages/login/main.js',
          'src/pages/order_info/main.js',
          'src/pages/order_search/main.js',
          'src/pages/rate_list/main.js',
          'src/pages/register/main.js',
          'src/pages/user_center/main.js',
          'src/pages/media_relations/main.js',
          'src/pages/hotel_list/main.js',
        ],
        refresh: true,
      }),
      AutoImport({
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    build: {
      manifest: true,
      cssCodeSplit: false, // 启用/禁用 CSS 代码拆分
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['vue', 'jquery', 'axios', 'dayjs', 'ppo']
          }
        }
      },
    },
  }
})
