// tailwind.config.js
module.exports = {
  content: [
    '../WEB-INF/view/*.jsp',
    '../WEB-INF/view/**/*.jsp',
    '../WEB-INF/view/**/**/*.jsp',
    './src/**/*.{vue,js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      screens: {
        
      },
      borderWidth: {
        '3': '3px',
        '5': '5px',
        '6': '6px',
        '7': '7px',
        '9': '9px',
        '10': '10px',
      },
      fontSize: {
        '2xs': '0.625rem',//10px
        '1.1xl': '1.375rem',//22px
        '2.1xl': '1.625rem', //26px
        '2.2xl': '1.75rem', //28px
        '4.2xl': '2.625rem', //42px
      },
      colors: {
        primary: {
          DEFAULT: '#9e693d',
          dark: '#7e5431',
          light: '#bb9677',
          light_2: 'rgba(252,247,244, 0.8)',
          light_3: 'rgba(249,240,233, 0.5)',
          light_4: '#f8f8f8',
          light_5: '#fcf7f4',
          light_6: '#f7f0ea',
        },
        brand: {
          DEFAULT: '#f4cc2c',
          dark: '#e1b70c',
        },
        // 文字颜色
        text: {
          DEFAULT: '#333333',
          dark: '#282828',
          light: '#666666',
          light_2: '#999999',
          light_3: '#454545',
          tip: 'rgba(69, 138, 148, 0.7)',
        },
        bg_clube: {
          DEFAULT: '#f8f0e9',
          dark: '#f7f0ea',
        },
        bg_gary: {
          DEFAULT: '#d4d4d4',
          light: '#f4f4f4',
          dark: '#bbbbbb',
          light_2: '#f5f5f5',
          dark_2: '#dcdcdc',
          light_3: '#e1e1e1',
          dark_3: '#c8c8c8',
        },
        bg_blank: {
          DEFAULT: '#282828',
        },
        // 线条颜色
        line: {
          DEFAULT: '#d4d4d4',
          light: 'rgba(234, 234, 234, 0.3)',
          light_2: '#e9e9e9',
          light_3: '#f7f0ea',
          light_4: '#ebebeb',
          light_5: '#e5e5e5',
          light_6: 'rgba(255,255,255,0.2)',
          dark: '#444444',
        },
        // 提示颜色
        success: 'var(--el-color-success)',
        warning: 'var(--el-color-warning)',
        danger: 'var(--el-color-danger)',
        error: 'var(--el-color-error)',
        info: 'var(--el-color-info)',
        // 渐变色
        gradient:{
          'black-start': 'rgba(0, 0, 0, 0.52)',
          'black-end': 'rgba(0, 0, 0, 0)',
          'primary-start': 'rgba(158, 105, 61, 1)',
          'primary-end': 'rgba(158, 105, 61, 0)',
        },
        // 社交媒体
        xiaohongshu: "#ff0000",
        weibo: "#d5261e",
        weixin: "#07c160",
      },
      spacing:{
        1.25: '0.3125rem', // 5px
        4.5: '1.12rem', // 18px
        5: '1.25rem', // 20px
        6.5: '1.625rem', // 26px
        7.5: '1.875rem', // 30px
        8.5: '2.125rem', // 34px
        12.5: '3.125rem', // 50px
        15: '3.75rem', // 60px
        15.75: '3.9375rem', // 63px
        16.5: '4.125rem', // 66px
        17.5: '4.375rem', // 70px
        22.5: '5.625rem', // 90px
        25: '6.25rem', // 100px
        26.75: '6.6875rem', // 107px
        29: '7.25rem', // 116px
        32.5: '8.125rem', //130px
        35: '8.75rem', // 140px
        38: '9.5rem', // 152px
        39: '9.75rem', // 156px
        45: '11.25rem', // 180px
        50: '12.5rem', // 200px
        62.5: '15.625rem', // 250px
        65: '16.25rem', // 260px
        75: '18.75rem', // 300px
        100: '25rem', // 400px
        125: '31.25rem', // 500px
        130: '32.5rem', // 520px
        147.5: '36.875rem', // 590px
        172.5: '43.125rem', // 690px
        187.5: '46.875rem', // 750px
      },
      height: {
        'screen-80': '80vh',
      },
      textUnderlineOffset: {
        1: '1px',
        2: '2px',
        3: '3px',
        4: '4px',
        8: '8px',
      },
      // 媒体比例
      aspectRatio: {
        '4/3': '4 / 3',
        '2/1': '2 / 1',
        '1680/668': '1680 / 668', // 首页_页面头部banner宽高比
        '750/836': '750 / 836', // 首页_页面头部banner手机宽高比
        '830/206': '830 / 206', // 首页_会员权益banner宽高比
        '680/206': '680 / 206', // 首页_会员权益banner手机宽高比
        '351/323': '351 / 323', // 首页_探索深思精选酒店banner宽高比
        '381/200': '381 / 200', // 首页_探索深思精选酒店banner手机宽高比
        '233/550': '233 / 550', // 首页_热门目的地图片pc宽高比
        '750/550': '750 / 550', // 首页_热门目的地图片pc展开宽高比
        '750/200': '750 / 200', // 首页_热门目的地图片手机宽高比
        '380/256': '380 / 256', // 首页_优惠活动推荐pc宽高比
        '650/550': '650 / 550', // 首页_主题酒店推荐1_pc宽高比
        '650/270': '650 / 270', // 首页_主题酒店推荐2_pc宽高比
        '320/270': '320 / 270', // 首页_主题酒店推荐3_pc宽高比
        '1680/420': '1680 / 420', // 酒店列表banner宽高比
        '380/255': '380 / 255', // 酒店列表图宽高比
        '281/187': '281 / 187', // 酒店列表幻灯片图宽高比
        '1680/600': '1680 / 600', // 会员俱乐部_头部banner宽高比
        '1680/380': '1680 / 380', // 会员俱乐部_底部banner宽高比
        '340/235': '340 / 235', // 会员俱乐部会员类型图片宽高比
        '1680/480': '1680 / 480', // 酒店详情_banner宽高比
        '750/500': '750 / 500', // 酒店详情_banner手机宽高比
        '570/366': '570 / 366', // 酒店详情_房型图片宽高比
        '1200/480': '1200 / 480', // 酒店详情_画廊, 优惠活动列表图片宽高比
        '130/100': '130 / 100', // 酒店详情_画廊小图图片宽高比
        '525/210': '525 / 210', // 新闻列表_图片宽高比
        '340/227': '340 / 227', // 业主故事_图片宽高比
        '480/275': '480 / 275', // 酒店详情房型封面图片宽高比
        1: "1",
        2: "2",
        3: "3",
        4: "4",
        5: "5",
        1680: '1680',
        668: '668',
        750: '750',
        836: '836',
        830: '830',
        206: '206',
        680: '680',
        680: '680',
        351: '351',
        323: '323',
        381: '381',
        381: '381',
        200: '200',
        233: '233',
        550: '550',
        380: '380',
        256: '256',
        650: '650',
        270: '270',
        320: '320',
        420: '420',
        380: '380',
        255: '255',
        281: '281',
        187: '187',
        600: '600',
        380: '380',
        340: '340',
        235: '235',
        480: '480',
        500: '500',
        570: '570',
        366: '366',
        1200: '1200',
        480: '480',
        130: '130',
        100: '100',
        525: '525',
        210: '210',
        227: '227',
        330: '330',
        280: '280',
        122: '122',
        75: '75',
        275: '275',
      },
      maxWidth: {
        50: '12.5em',  // 200px
        56: '14em',  // 224px
        187.5: '46.875em',  // 750px
        300: '75em',  // 1200px
      },
      minHeight: {
        11: '2.75em',  // 44px
        50: '12.5em',  // 200px
        70: '17.5em',  // 280px
        90: '22.5em',  // 360px
        105: '26.25em',  // 420px
        120: '30em',  // 480px
      },
      lineClamp: {
        7: '7',
        8: '8',
        9: '9',
        10: '10',
        11: '11',
        12: '12',
      },
      opacity: {
        '85': '.85',
      },
      listStyleType: {
        circle: 'circle',
        square: 'square',
        roman: 'upper-roman',
      },
    }
  },
  corePlugins: {
    aspectRatio: false,
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/aspect-ratio'),
  ]
}