# 第三方API集成说明

## 概述

本文档说明如何在HomeController的toHotelDetail方法中集成第三方API调用，获取酒店服务信息。
**重要更新**: 现在API返回的JSON数据在后端直接解析为Java对象，模板可以直接使用，无需前端JavaScript处理。

## 配置

### 1. 配置文件设置

在 `config.properties` 文件中已配置：
```properties
api.base_url=http://slh-service.test/api/
api.mock_mode=true
```

- `api.base_url`: 第三方API的基础URL
- `api.mock_mode`: 是否启用模拟模式（开发测试时可设为true）

### 2. 数据模型

创建了 `HotelServiceData` 数据模型类：
```
code/trail-api/src/com/trail/model/HotelServiceData.java
```

### 3. 服务类

创建了 `ThirdPartyApiService` 服务类，位于：
```
code/trail-api/src/com/trail/service/ThirdPartyApiService.java
```

### 4. 工具类

- `MockDataGenerator`: 模拟数据生成器，用于开发测试
- `ApiConfigValidator`: API配置验证工具

## 功能特性

### 1. 酒店服务接口调用

- **接口地址**: `{api.base_url}hotel-services/{hotelCode}`
- **示例**: `http://slh-service.test/api/hotel-services/HUCMBTF`
- **返回格式**: JSON

### 2. 方法说明

#### getHotelServices(String hotelCode)
- 调用酒店服务接口并解析为数据对象
- 参数：酒店代码
- 返回：HotelServiceData 对象或 null
- 支持模拟模式和降级处理

#### callApi(String endpoint)
- 通用API调用方法（返回原始JSON）
- 参数：API端点
- 返回：JsonObject 或 null

#### 数据模型结构
```java
HotelServiceData {
    String hotelCode;
    String hotelName;
    String status;
    String message;
    List<ServiceItem> services;
}

ServiceItem {
    String id;
    String name;
    String description;
    String category;
    String price;
    boolean available;
}
```

## 使用方式

### 1. 在Controller中使用

```java
@Autowired
private ThirdPartyApiService thirdPartyApiService;

// 在toHotelDetail方法中调用
HotelServiceData hotelServicesData = thirdPartyApiService.getHotelServices(hotel.getHotelCode());
if (hotelServicesData != null) {
    model.addAttribute("hotelServicesData", hotelServicesData);
}
```

### 2. 在JSP页面中使用

**现在可以直接在模板中使用解析后的数据对象：**

```jsp
<c:if test="${not empty hotelServicesData}">
  <div class="hotel-services">
    <h3>酒店服务信息</h3>
    <c:choose>
      <c:when test="${not empty hotelServicesData.services}">
        <div class="services-grid">
          <c:forEach var="service" items="${hotelServicesData.services}">
            <div class="service-item">
              <h4>${service.name}</h4>
              <p>${service.description}</p>
              <span class="category">${service.category}</span>
              <span class="price">${service.price}</span>
              <c:choose>
                <c:when test="${service.available}">
                  <span class="status available">✓ 可用</span>
                </c:when>
                <c:otherwise>
                  <span class="status unavailable">✗ 不可用</span>
                </c:otherwise>
              </c:choose>
            </div>
          </c:forEach>
        </div>
      </c:when>
      <c:otherwise>
        <p>暂无酒店服务信息</p>
        <c:if test="${not empty hotelServicesData.message}">
          <p class="message">${hotelServicesData.message}</p>
        </c:if>
      </c:otherwise>
    </c:choose>
  </div>
</c:if>
```

**无需JavaScript处理** - 所有数据都在后端解析完成，模板可以直接使用。

## 测试

### 1. 单元测试

运行测试类：
```
code/trail-api/src/com/trail/test/ThirdPartyApiServiceTest.java
```

### 2. 手动测试

1. 启动应用
2. 访问酒店详情页面：`/hotel/{hotelLink}`
3. 检查页面源码中是否包含 `hotelServicesData`
4. 查看日志输出

## 错误处理

### 1. 日志记录

- 成功调用：INFO级别日志
- 失败调用：ERROR级别日志
- 警告信息：WARN级别日志

### 2. 异常处理

- 网络异常：返回null，记录错误日志
- JSON解析异常：返回null，记录错误日志
- 参数验证：空参数返回null，记录警告日志

## 扩展功能

### 1. 添加新的API端点

```java
public JsonObject getHotelRooms(String hotelCode) {
    return callApi("hotel-rooms/" + hotelCode);
}
```

### 2. 添加请求头

在 `httpGet` 方法中可以添加更多请求头：
```java
httpGet.setHeader("Authorization", "Bearer " + token);
httpGet.setHeader("X-API-Key", apiKey);
```

### 3. 添加POST请求支持

可以扩展服务类支持POST请求：
```java
private String httpPost(String url, String jsonBody) {
    // POST请求实现
}
```

## 注意事项

1. **网络超时**: 默认使用系统超时设置
2. **错误重试**: 当前版本不支持自动重试
3. **缓存**: 当前版本不支持响应缓存
4. **安全性**: 建议在生产环境中添加API认证
5. **性能**: 建议添加连接池管理

## 故障排除

### 1. API调用失败

检查项目：
- 网络连接是否正常
- API服务是否可用
- 配置文件中的URL是否正确
- 酒店代码是否有效

### 2. 日志查看

查看应用日志中的相关信息：
```
INFO - Calling third party API: http://slh-service.test/api/hotel-services/HUCMBTF
ERROR - Error calling third party API: [URL], [Exception]
```

### 3. 调试模式

启用DEBUG日志级别查看详细的API响应：
```
DEBUG - API response: [JSON Response]
```
