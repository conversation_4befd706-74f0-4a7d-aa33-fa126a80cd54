# 第三方API集成说明

## 概述

本文档说明如何在HomeController的toHotelDetail方法中集成第三方API调用，获取酒店服务信息。

## 配置

### 1. 配置文件设置

在 `config.properties` 文件中已配置：
```properties
api.base_url=http://slh-service.test/api/
```

### 2. 服务类

创建了 `ThirdPartyApiService` 服务类，位于：
```
code/trail-api/src/com/trail/service/ThirdPartyApiService.java
```

## 功能特性

### 1. 酒店服务接口调用

- **接口地址**: `{api.base_url}hotel-services/{hotelCode}`
- **示例**: `http://slh-service.test/api/hotel-services/HUCMBTF`
- **返回格式**: JSON

### 2. 方法说明

#### getHotelServices(String hotelCode)
- 调用酒店服务接口
- 参数：酒店代码
- 返回：JsonObject 或 null

#### callApi(String endpoint)
- 通用API调用方法
- 参数：API端点
- 返回：JsonObject 或 null

## 使用方式

### 1. 在Controller中使用

```java
@Autowired
private ThirdPartyApiService thirdPartyApiService;

// 在toHotelDetail方法中调用
JsonObject hotelServicesData = thirdPartyApiService.getHotelServices(hotel.getHotelCode());
if (hotelServicesData != null) {
    model.addAttribute("hotelServicesData", hotelServicesData);
}
```

### 2. 在JSP页面中使用

```jsp
<c:choose>
    <c:when test="${not empty hotelServicesData}">
        <!-- 显示API数据 -->
        <pre>${hotelServicesData}</pre>
    </c:when>
    <c:otherwise>
        <!-- 显示错误信息 -->
        <p>未能获取第三方接口数据</p>
    </c:otherwise>
</c:choose>
```

### 3. JavaScript中解析JSON

```javascript
var hotelData = ${hotelServicesData};
if (hotelData.services) {
    hotelData.services.forEach(function(service) {
        console.log(service.name, service.description);
    });
}
```

## 测试

### 1. 单元测试

运行测试类：
```
code/trail-api/src/com/trail/test/ThirdPartyApiServiceTest.java
```

### 2. 手动测试

1. 启动应用
2. 访问酒店详情页面：`/hotel/{hotelLink}`
3. 检查页面源码中是否包含 `hotelServicesData`
4. 查看日志输出

## 错误处理

### 1. 日志记录

- 成功调用：INFO级别日志
- 失败调用：ERROR级别日志
- 警告信息：WARN级别日志

### 2. 异常处理

- 网络异常：返回null，记录错误日志
- JSON解析异常：返回null，记录错误日志
- 参数验证：空参数返回null，记录警告日志

## 扩展功能

### 1. 添加新的API端点

```java
public JsonObject getHotelRooms(String hotelCode) {
    return callApi("hotel-rooms/" + hotelCode);
}
```

### 2. 添加请求头

在 `httpGet` 方法中可以添加更多请求头：
```java
httpGet.setHeader("Authorization", "Bearer " + token);
httpGet.setHeader("X-API-Key", apiKey);
```

### 3. 添加POST请求支持

可以扩展服务类支持POST请求：
```java
private String httpPost(String url, String jsonBody) {
    // POST请求实现
}
```

## 注意事项

1. **网络超时**: 默认使用系统超时设置
2. **错误重试**: 当前版本不支持自动重试
3. **缓存**: 当前版本不支持响应缓存
4. **安全性**: 建议在生产环境中添加API认证
5. **性能**: 建议添加连接池管理

## 故障排除

### 1. API调用失败

检查项目：
- 网络连接是否正常
- API服务是否可用
- 配置文件中的URL是否正确
- 酒店代码是否有效

### 2. 日志查看

查看应用日志中的相关信息：
```
INFO - Calling third party API: http://slh-service.test/api/hotel-services/HUCMBTF
ERROR - Error calling third party API: [URL], [Exception]
```

### 3. 调试模式

启用DEBUG日志级别查看详细的API响应：
```
DEBUG - API response: [JSON Response]
```
