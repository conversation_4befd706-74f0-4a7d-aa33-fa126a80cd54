package com.trail.service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.trail.model.HotelServiceData;
import com.trail.util.MockDataGenerator;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 第三方API服务类
 * 用于调用外部接口获取数据
 */
@Service
public class ThirdPartyApiService {
    
    private static final Logger log = Logger.getLogger(ThirdPartyApiService.class);
    private Gson gson = new Gson();
    
    @Value("${api.base_url}")
    private String apiBaseUrl;

    @Value("${api.mock_mode:false}")
    private boolean mockMode;
    
    /**
     * 调用酒店服务接口并解析为数据对象
     * @param hotelCode 酒店代码
     * @return 解析后的酒店服务数据对象
     */
    public HotelServiceData getHotelServices(String hotelCode) {
        if (StringUtils.isEmpty(hotelCode)) {
            log.warn("Hotel code is empty");
            return null;
        }

        // 如果启用了模拟模式，返回模拟数据
        if (mockMode) {
            log.info("Mock mode enabled, returning mock data for hotel: " + hotelCode);
            return MockDataGenerator.generateMockHotelServiceData(hotelCode);
        }

        String url = apiBaseUrl + "hotel-services/" + hotelCode;
        log.info("Calling third party API: " + url);

        try {
            String response = httpGet(url);
            if (StringUtils.isNotEmpty(response)) {
                return parseHotelServiceResponse(response, hotelCode);
            }
        } catch (Exception e) {
            log.error("Error calling third party API: " + url, e);
            // 如果API调用失败，可以选择返回模拟数据作为降级方案
            log.info("API call failed, returning mock data as fallback for hotel: " + hotelCode);
            return MockDataGenerator.generateMockHotelServiceData(hotelCode);
        }

        return null;
    }

    /**
     * 解析API响应为酒店服务数据对象
     * API返回格式：[{"id":64,"service_code":106,"service_name":"礼宾部/行李员服务","service_name_en":"Bell staff/porter","service_type":"HAC"}]
     * @param jsonResponse JSON响应字符串
     * @param hotelCode 酒店代码
     * @return 解析后的数据对象
     */
    private HotelServiceData parseHotelServiceResponse(String jsonResponse, String hotelCode) {
        try {
            JsonParser parser = new JsonParser();
            JsonElement jsonElement = parser.parse(jsonResponse);

            HotelServiceData serviceData = new HotelServiceData();
            serviceData.setHotelCode(hotelCode);
            serviceData.setStatus("success");

            List<HotelServiceData.ServiceItem> services = new ArrayList<>();

            // API直接返回数组格式
            if (jsonElement.isJsonArray()) {
                JsonArray servicesArray = jsonElement.getAsJsonArray();
                for (JsonElement element : servicesArray) {
                    if (element.isJsonObject()) {
                        JsonObject serviceObj = element.getAsJsonObject();
                        HotelServiceData.ServiceItem serviceItem = parseServiceItem(serviceObj);
                        if (serviceItem != null) {
                            services.add(serviceItem);
                        }
                    }
                }
            }

            serviceData.setServices(services);

            if (services.isEmpty()) {
                serviceData.setMessage("该酒店暂无服务信息");
            } else {
                serviceData.setMessage("成功获取 " + services.size() + " 项服务信息");
            }

            log.info("Successfully parsed hotel services data for hotel: " + hotelCode +
                    ", found " + services.size() + " services");

            return serviceData;

        } catch (Exception e) {
            log.error("Error parsing hotel service response for hotel: " + hotelCode, e);
            HotelServiceData errorData = new HotelServiceData();
            errorData.setHotelCode(hotelCode);
            errorData.setStatus("error");
            errorData.setMessage("解析服务数据时出现错误: " + e.getMessage());
            errorData.setServices(new ArrayList<>());
            return errorData;
        }
    }

    /**
     * 解析单个服务项目
     * 实际API字段：{"id":64,"service_code":106,"service_name":"礼宾部/行李员服务","service_name_en":"Bell staff/porter","service_type":"HAC"}
     */
    private HotelServiceData.ServiceItem parseServiceItem(JsonObject serviceObj) {
        try {
            HotelServiceData.ServiceItem serviceItem = new HotelServiceData.ServiceItem();

            if (serviceObj.has("id")) {
                serviceItem.setId(serviceObj.get("id").getAsInt());
            }

            if (serviceObj.has("service_code")) {
                serviceItem.setServiceCode(serviceObj.get("service_code").getAsInt());
            }

            if (serviceObj.has("service_name")) {
                serviceItem.setServiceName(serviceObj.get("service_name").getAsString());
            }

            if (serviceObj.has("service_name_en")) {
                serviceItem.setServiceNameEn(serviceObj.get("service_name_en").getAsString());
            }

            if (serviceObj.has("service_type")) {
                serviceItem.setServiceType(serviceObj.get("service_type").getAsString());
            }

            return serviceItem;

        } catch (Exception e) {
            log.warn("Error parsing service item: " + e.getMessage() + ", JSON: " + serviceObj.toString());
            return null;
        }
    }
    
    /**
     * 通用GET请求方法
     * @param url 请求URL
     * @return 响应字符串
     */
    private String httpGet(String url) {
        HttpClient httpClient = new DefaultHttpClient();
        HttpGet httpGet = new HttpGet(url);
        
        try {
            // 设置请求头
            httpGet.setHeader("Accept", "application/json");
            httpGet.setHeader("Content-Type", "application/json");
            httpGet.setHeader("User-Agent", "SLH-Trail-API/1.0");
            
            HttpResponse response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            
            if (statusCode == 200) {
                String result = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.debug("API response: " + result);
                return result;
            } else {
                log.warn("API returned status code: " + statusCode);
            }
        } catch (Exception e) {
            log.error("HTTP GET request failed: " + url, e);
        } finally {
            httpGet.releaseConnection();
        }
        
        return null;
    }
    
    /**
     * 调用通用API接口
     * @param endpoint 接口端点
     * @return JSON响应数据
     */
    public JsonObject callApi(String endpoint) {
        if (StringUtils.isEmpty(endpoint)) {
            log.warn("API endpoint is empty");
            return null;
        }
        
        String url = apiBaseUrl + endpoint;
        log.info("Calling API: " + url);
        
        try {
            String response = httpGet(url);
            if (StringUtils.isNotEmpty(response)) {
                JsonParser parser = new JsonParser();
                return parser.parse(response).getAsJsonObject();
            }
        } catch (Exception e) {
            log.error("Error calling API: " + url, e);
        }
        
        return null;
    }
}
