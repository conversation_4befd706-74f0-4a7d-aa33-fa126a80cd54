package com.trail.service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 第三方API服务类
 * 用于调用外部接口获取数据
 */
@Service
public class ThirdPartyApiService {
    
    private static final Logger log = Logger.getLogger(ThirdPartyApiService.class);
    private Gson gson = new Gson();
    
    @Value("${api.base_url}")
    private String apiBaseUrl;
    
    /**
     * 调用酒店服务接口
     * @param hotelCode 酒店代码
     * @return JSON响应数据
     */
    public JsonObject getHotelServices(String hotelCode) {
        if (StringUtils.isEmpty(hotelCode)) {
            log.warn("Hotel code is empty");
            return null;
        }
        
        String url = apiBaseUrl + "hotel-services/" + hotelCode;
        log.info("Calling third party API: " + url);
        
        try {
            String response = httpGet(url);
            if (StringUtils.isNotEmpty(response)) {
                JsonParser parser = new JsonParser();
                return parser.parse(response).getAsJsonObject();
            }
        } catch (Exception e) {
            log.error("Error calling third party API: " + url, e);
        }
        
        return null;
    }
    
    /**
     * 通用GET请求方法
     * @param url 请求URL
     * @return 响应字符串
     */
    private String httpGet(String url) {
        HttpClient httpClient = new DefaultHttpClient();
        HttpGet httpGet = new HttpGet(url);
        
        try {
            // 设置请求头
            httpGet.setHeader("Accept", "application/json");
            httpGet.setHeader("Content-Type", "application/json");
            httpGet.setHeader("User-Agent", "SLH-Trail-API/1.0");
            
            HttpResponse response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            
            if (statusCode == 200) {
                String result = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.debug("API response: " + result);
                return result;
            } else {
                log.warn("API returned status code: " + statusCode);
            }
        } catch (Exception e) {
            log.error("HTTP GET request failed: " + url, e);
        } finally {
            httpGet.releaseConnection();
        }
        
        return null;
    }
    
    /**
     * 调用通用API接口
     * @param endpoint 接口端点
     * @return JSON响应数据
     */
    public JsonObject callApi(String endpoint) {
        if (StringUtils.isEmpty(endpoint)) {
            log.warn("API endpoint is empty");
            return null;
        }
        
        String url = apiBaseUrl + endpoint;
        log.info("Calling API: " + url);
        
        try {
            String response = httpGet(url);
            if (StringUtils.isNotEmpty(response)) {
                JsonParser parser = new JsonParser();
                return parser.parse(response).getAsJsonObject();
            }
        } catch (Exception e) {
            log.error("Error calling API: " + url, e);
        }
        
        return null;
    }
}
