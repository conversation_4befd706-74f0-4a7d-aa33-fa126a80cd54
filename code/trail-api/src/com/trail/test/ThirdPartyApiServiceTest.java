package com.trail.test;

import com.trail.model.HotelServiceData;
import com.trail.service.ThirdPartyApiService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 第三方API服务测试类
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/applicationContext.xml"})
public class ThirdPartyApiServiceTest {

    @Autowired
    private ThirdPartyApiService thirdPartyApiService;

    @Test
    public void testGetHotelServices() {
        // 测试调用酒店服务接口
        String testHotelCode = "HUCMBTF";

        System.out.println("Testing hotel services API for hotel code: " + testHotelCode);

        HotelServiceData result = thirdPartyApiService.getHotelServices(testHotelCode);

        if (result != null) {
            System.out.println("API call successful!");
            System.out.println("Hotel Code: " + result.getHotelCode());
            System.out.println("Hotel Name: " + result.getHotelName());
            System.out.println("Status: " + result.getStatus());
            System.out.println("Message: " + result.getMessage());

            if (result.getServices() != null && !result.getServices().isEmpty()) {
                System.out.println("Services found: " + result.getServices().size());
                for (HotelServiceData.ServiceItem service : result.getServices()) {
                    System.out.println("  - ID: " + service.getId());
                    System.out.println("    Service Code: " + service.getServiceCode());
                    System.out.println("    Service Name: " + service.getServiceName());
                    System.out.println("    Service Name EN: " + service.getServiceNameEn());
                    System.out.println("    Service Type: " + service.getServiceType());
                    System.out.println("    ---");
                }
            } else {
                System.out.println("No services found");
            }
        } else {
            System.out.println("API call returned null - check configuration and network connectivity");
        }
    }

    @Test
    public void testCallGenericApi() {
        // 测试通用API调用（返回原始JSON）
        String endpoint = "hotel-services/HUCMBTF";

        System.out.println("Testing generic API call with endpoint: " + endpoint);

        // 注意：callApi方法仍然返回JsonObject，用于需要原始JSON的场景
        // JsonObject result = thirdPartyApiService.callApi(endpoint);

        // 现在我们主要使用解析后的数据对象
        HotelServiceData result = thirdPartyApiService.getHotelServices("HUCMBTF");

        if (result != null) {
            System.out.println("Parsed data object test successful!");
            System.out.println("Hotel: " + result.getHotelCode());
            System.out.println("Services count: " + (result.getServices() != null ? result.getServices().size() : 0));
        } else {
            System.out.println("Parsed data object test returned null");
        }
    }
}
