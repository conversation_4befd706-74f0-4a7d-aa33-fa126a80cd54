package com.trail.test;

import com.google.gson.JsonObject;
import com.trail.service.ThirdPartyApiService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 第三方API服务测试类
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/applicationContext.xml"})
public class ThirdPartyApiServiceTest {

    @Autowired
    private ThirdPartyApiService thirdPartyApiService;

    @Test
    public void testGetHotelServices() {
        // 测试调用酒店服务接口
        String testHotelCode = "HUCMBTF";
        
        System.out.println("Testing hotel services API for hotel code: " + testHotelCode);
        
        JsonObject result = thirdPartyApiService.getHotelServices(testHotelCode);
        
        if (result != null) {
            System.out.println("API call successful!");
            System.out.println("Response: " + result.toString());
        } else {
            System.out.println("API call returned null - check configuration and network connectivity");
        }
    }

    @Test
    public void testCallGenericApi() {
        // 测试通用API调用
        String endpoint = "hotel-services/HUCMBTF";
        
        System.out.println("Testing generic API call with endpoint: " + endpoint);
        
        JsonObject result = thirdPartyApiService.callApi(endpoint);
        
        if (result != null) {
            System.out.println("Generic API call successful!");
            System.out.println("Response: " + result.toString());
        } else {
            System.out.println("Generic API call returned null");
        }
    }
}
