package com.trail.test;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.trail.model.HotelServiceData;

import java.util.ArrayList;
import java.util.List;

/**
 * JSON解析测试工具
 * 用于验证实际API响应的解析逻辑
 */
public class JsonParsingTest {
    
    public static void main(String[] args) {
        // 实际的API响应数据
        String actualApiResponse = "[{\"id\":64,\"service_code\":106,\"service_name\":\"礼宾部/行李员服务\",\"service_name_en\":\"Bell staff/porter\",\"service_type\":\"HAC\"}]";
        
        System.out.println("=== JSON解析测试 ===");
        System.out.println("原始JSON: " + actualApiResponse);
        System.out.println();
        
        try {
            HotelServiceData result = parseHotelServiceResponse(actualApiResponse, "HUCMBTF");
            
            if (result != null) {
                System.out.println("解析成功！");
                System.out.println("酒店代码: " + result.getHotelCode());
                System.out.println("状态: " + result.getStatus());
                System.out.println("消息: " + result.getMessage());
                System.out.println("服务数量: " + (result.getServices() != null ? result.getServices().size() : 0));
                System.out.println();
                
                if (result.getServices() != null && !result.getServices().isEmpty()) {
                    System.out.println("=== 服务详情 ===");
                    for (HotelServiceData.ServiceItem service : result.getServices()) {
                        System.out.println("ID: " + service.getId());
                        System.out.println("服务代码: " + service.getServiceCode());
                        System.out.println("服务名称: " + service.getServiceName());
                        System.out.println("英文名称: " + service.getServiceNameEn());
                        System.out.println("服务类型: " + service.getServiceType());
                        System.out.println("---");
                    }
                }
            } else {
                System.out.println("解析失败！");
            }
            
        } catch (Exception e) {
            System.out.println("解析异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析API响应为酒店服务数据对象（复制自ThirdPartyApiService的逻辑）
     */
    private static HotelServiceData parseHotelServiceResponse(String jsonResponse, String hotelCode) {
        try {
            JsonParser parser = new JsonParser();
            JsonElement jsonElement = parser.parse(jsonResponse);
            
            HotelServiceData serviceData = new HotelServiceData();
            serviceData.setHotelCode(hotelCode);
            serviceData.setStatus("success");
            
            List<HotelServiceData.ServiceItem> services = new ArrayList<>();
            
            // API直接返回数组格式
            if (jsonElement.isJsonArray()) {
                JsonArray servicesArray = jsonElement.getAsJsonArray();
                for (JsonElement element : servicesArray) {
                    if (element.isJsonObject()) {
                        JsonObject serviceObj = element.getAsJsonObject();
                        HotelServiceData.ServiceItem serviceItem = parseServiceItem(serviceObj);
                        if (serviceItem != null) {
                            services.add(serviceItem);
                        }
                    }
                }
            }
            
            serviceData.setServices(services);
            
            if (services.isEmpty()) {
                serviceData.setMessage("该酒店暂无服务信息");
            } else {
                serviceData.setMessage("成功获取 " + services.size() + " 项服务信息");
            }
            
            return serviceData;
            
        } catch (Exception e) {
            System.out.println("解析错误: " + e.getMessage());
            HotelServiceData errorData = new HotelServiceData();
            errorData.setHotelCode(hotelCode);
            errorData.setStatus("error");
            errorData.setMessage("解析服务数据时出现错误: " + e.getMessage());
            errorData.setServices(new ArrayList<>());
            return errorData;
        }
    }
    
    /**
     * 解析单个服务项目
     */
    private static HotelServiceData.ServiceItem parseServiceItem(JsonObject serviceObj) {
        try {
            HotelServiceData.ServiceItem serviceItem = new HotelServiceData.ServiceItem();
            
            if (serviceObj.has("id")) {
                serviceItem.setId(serviceObj.get("id").getAsInt());
            }
            
            if (serviceObj.has("service_code")) {
                serviceItem.setServiceCode(serviceObj.get("service_code").getAsInt());
            }
            
            if (serviceObj.has("service_name")) {
                serviceItem.setServiceName(serviceObj.get("service_name").getAsString());
            }
            
            if (serviceObj.has("service_name_en")) {
                serviceItem.setServiceNameEn(serviceObj.get("service_name_en").getAsString());
            }
            
            if (serviceObj.has("service_type")) {
                serviceItem.setServiceType(serviceObj.get("service_type").getAsString());
            }
            
            return serviceItem;
            
        } catch (Exception e) {
            System.out.println("解析服务项目错误: " + e.getMessage() + ", JSON: " + serviceObj.toString());
            return null;
        }
    }
}
