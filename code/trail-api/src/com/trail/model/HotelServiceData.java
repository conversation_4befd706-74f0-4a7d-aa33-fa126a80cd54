package com.trail.model;

import java.util.List;

/**
 * 酒店服务数据模型
 * 根据实际API响应格式设计：
 * [{"id":64,"service_code":106,"service_name":"礼宾部/行李员服务","service_name_en":"Bell staff/porter","service_type":"HAC"}]
 */
public class HotelServiceData {

    private String hotelCode;
    private List<ServiceItem> services;
    private String status;
    private String message;

    public HotelServiceData() {
    }

    public String getHotelCode() {
        return hotelCode;
    }

    public void setHotelCode(String hotelCode) {
        this.hotelCode = hotelCode;
    }

    public List<ServiceItem> getServices() {
        return services;
    }

    public void setServices(List<ServiceItem> services) {
        this.services = services;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 服务项目内部类 - 匹配实际API响应字段
     */
    public static class ServiceItem {
        private Integer id;
        private Integer serviceCode;
        private String serviceName;
        private String serviceNameEn;
        private String serviceType;

        public ServiceItem() {
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public Integer getServiceCode() {
            return serviceCode;
        }

        public void setServiceCode(Integer serviceCode) {
            this.serviceCode = serviceCode;
        }

        public String getServiceName() {
            return serviceName;
        }

        public void setServiceName(String serviceName) {
            this.serviceName = serviceName;
        }

        public String getServiceNameEn() {
            return serviceNameEn;
        }

        public void setServiceNameEn(String serviceNameEn) {
            this.serviceNameEn = serviceNameEn;
        }

        public String getServiceType() {
            return serviceType;
        }

        public void setServiceType(String serviceType) {
            this.serviceType = serviceType;
        }
    }
}
