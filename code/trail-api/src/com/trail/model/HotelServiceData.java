package com.trail.model;

import java.util.List;

/**
 * 酒店服务数据模型
 */
public class HotelServiceData {
    
    private String hotelCode;
    private String hotelName;
    private List<ServiceItem> services;
    private String status;
    private String message;
    
    public HotelServiceData() {
    }
    
    public String getHotelCode() {
        return hotelCode;
    }
    
    public void setHotelCode(String hotelCode) {
        this.hotelCode = hotelCode;
    }
    
    public String getHotelName() {
        return hotelName;
    }
    
    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }
    
    public List<ServiceItem> getServices() {
        return services;
    }
    
    public void setServices(List<ServiceItem> services) {
        this.services = services;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    /**
     * 服务项目内部类
     */
    public static class ServiceItem {
        private String id;
        private String name;
        private String description;
        private String category;
        private String price;
        private boolean available;
        
        public ServiceItem() {
        }
        
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public String getCategory() {
            return category;
        }
        
        public void setCategory(String category) {
            this.category = category;
        }
        
        public String getPrice() {
            return price;
        }
        
        public void setPrice(String price) {
            this.price = price;
        }
        
        public boolean isAvailable() {
            return available;
        }
        
        public void setAvailable(boolean available) {
            this.available = available;
        }
    }
}
