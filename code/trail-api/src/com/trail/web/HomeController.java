package com.trail.web;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import cn.lhw.common.entity.*;
import cn.lhw.common.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import com.dragontrail.crs.service.RateSearchService;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.itcrowd.framework.util.DateUtil;
import com.spreada.utils.chinese.ZHConverter;

import cn.lhw.common.constants.ChainCodeEnum;
import cn.lhw.common.log.HttpLog;
import cn.lhw.common.utils.SlhUtils;

@Controller
public class HomeController {
    private static final Logger log = Logger.getLogger(HomeController.class);
    private Gson gson = new Gson();
    @Value("${image.upload.url}")
    private String imageUploadUrl;
    @Value("${chainCode}")
    private String chainCode;

    @Value("${locale}")
    private String localeConfig;

    //前端酒店总数
    @Value("${hotel.count:600}")
    private int FrontHotelCount;

    //前端国家数量
    @Value("${hotel.country.count:90}")
    private int FrontCountryCount;

    //前端 Api 地址
    @Value("${front.api_host}")
    private String frontApiUrl;

    @Autowired
    private HotelService hotelService;
    @Autowired
    private CountryService countryService;
    @Autowired
    private CityService cityService;
    @Autowired
    private ContinentService continentService;
    @Autowired
    private SLHFeatureService slhFeatureService;
    @Autowired
    private HotelImageService hotelImageService;
    @Autowired
    private LandmarkService landmarkService;
    @Autowired
    private TransportService transportService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    @Qualifier("rateSearchService")
    private RateSearchService rateSearchService;
    @Autowired
    private HotSiteService hotSiteService;
    @Autowired
    private SLHHotelCodeService slhHotelCodeService;
    @Autowired
    private SeekSimplicityService seekSimplicityService;
    @Autowired
    private RoomService roomService;

    @Autowired
    private HotelIconFeatureService hotelIconFeatureService;


    @RequestMapping(value = {"/maintaim"})
    public String maintaim(HttpSession session, HttpServletRequest request, Model model) {
        return "maintaim";
    }

    @RequestMapping(value = "/travelcomfortzones")
    public String travelcomfortzones(HttpSession session, HttpServletRequest request, Model model) {
        String locale = request.getParameter("locale");
        if (locale == null) {
            locale = localeConfig;
        }
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }
        String title = "超越自我的旅行 | SLH全球奢华精品酒店优惠活动";
        String keywords = "全球精品奢华酒店特别优惠,SLH全球奢华酒店优惠促销,全球奢华酒店打折";
        String description = "SLH全球奢华精品酒店特别优惠套餐，打折信息，会员专享特惠及节假日特别优惠套餐等，专享活动、惊喜不断。";
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            keywords = ZHConverter.convert(keywords, ZHConverter.TRADITIONAL);
            description = ZHConverter.convert(description, ZHConverter.TRADITIONAL);
        }
        model.addAttribute("pageTitle", "超越自我的旅行");
        model.addAttribute("crumbNav", "超越自我的旅行");
        model.addAttribute("continents", continents);
        model.addAttribute("locale", locale);
        model.addAttribute("title", title);
        model.addAttribute("keywords", keywords);
        model.addAttribute("description", description);
        if (locale.equals("zh_TW")) {
            continents.parallelStream().forEach(continent -> {
                continent.setName(ZHConverter.convert(continent.getName(), ZHConverter.TRADITIONAL));
                continent.getCountries().parallelStream().forEach(country ->
                        country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL))
                );
            });
            model.addAttribute("continents", continents);
            return "travelcomfortzones_tw";
        }

        return "travelcomfortzones";
    }

    @RequestMapping(value = {"/", "/index"})
    public String index(HttpSession session, HttpServletRequest request, Model model,
                        @RequestParam(value = "locale", required = false) String locale) {
        locale = localeConfig;
        //搜索框缓存数据1
        FuzzyQueryResult queryCache = hotelService.fuzzyQuery(chainCode);

        //设置搜索繁体数据
        queryCache.getCountries().parallelStream().forEach(country ->
                country.setCountryNameTra(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL)));
        queryCache.getCities().parallelStream().forEach(city -> {
            city.setCityNameTra(ZHConverter.convert(city.getCityName(), ZHConverter.TRADITIONAL));
            if (city.getCountryName() != null) {
                city.setCountryNameTra(ZHConverter.convert(city.getCountryName(), ZHConverter.TRADITIONAL));
            } else {
                System.out.println(city.getCityName() + "city.getCountryName() is null");
                city.setCountryNameTra("");
            }
        });
        queryCache.getHotels().parallelStream().forEach(hotel -> {
            hotel.setHotelNameTra(ZHConverter.convert(hotel.getHotelName(), ZHConverter.TRADITIONAL));
            hotel.setCityNameTra(ZHConverter.convert(hotel.getCityName(), ZHConverter.TRADITIONAL));
            hotel.setCountryNameTra(ZHConverter.convert(hotel.getCountryName(), ZHConverter.TRADITIONAL));
        });
        List<Continent> continents = continentService.getContinentsAllInfo(chainCode);

        String checkinDate = SlhUtils.getDefaultCheckInDate();
        String checkoutDate = SlhUtils.getDefaultCheckoutDate();
        int nights = DateUtil.daysBetween(DateUtil.stringFormatToDate(checkinDate, "yyyy-MM-dd"), DateUtil.stringFormatToDate(checkoutDate, "yyyy-MM-dd"));
        model.addAttribute("checkinDate", checkinDate);
        model.addAttribute("checkoutDate", checkoutDate);
        model.addAttribute("nights", nights);
        model.addAttribute("testInt", 2);

        // 查询PC端轮播图
        List<Activity> swiperPC = activityService.getActivitiesByType(chainCode, "index-swiper-pc");
        // 数据预处理，把图片路径替换为绝对路径
        for (int j = 0; j < swiperPC.size(); j++) {
            swiperPC.get(j).setBanner(configService.getConfig(chainCode, "imagePath" + (j % 4 + 1)).getValue() + "/" + swiperPC.get(j).getBanner());
            if (StringUtils.isNotBlank(swiperPC.get(j).getBannerTra())) {
                swiperPC.get(j).setBannerTra(configService.getConfig(chainCode, "imagePath" + (j % 4 + 1)).getValue() + "/" + swiperPC.get(j).getBannerTra());
            }
        }
        model.addAttribute("swiperPC", swiperPC);

        // 查询移动端轮播图
        List<Activity> swiperMobile = activityService.getActivitiesByType(chainCode, "index-swiper-m");
        // 数据预处理，把图片路径替换为绝对路径
        for (int j = 0; j < swiperMobile.size(); j++) {
            swiperMobile.get(j).setBanner(configService.getConfig(chainCode, "imagePath" + (j % 4 + 1)).getValue() + "/" + swiperMobile.get(j).getBanner());
            if (StringUtils.isNotBlank(swiperMobile.get(j).getBannerTra())) {
                swiperMobile.get(j).setBannerTra(configService.getConfig(chainCode, "imagePath" + (j % 4 + 1)).getValue() + "/" + swiperMobile.get(j).getBannerTra());
            }
        }
        model.addAttribute("swiperMobile", swiperMobile);

        // 查询主题信息
        List<Activity> experiences = activityService.getActivitiesByType(chainCode, "experience");
        // 数据预处理，把图片路径替换为绝对路径
        for (int i = 0; i < experiences.size(); i++) {
            try {
                String imagepPath = configService.getConfig(chainCode, "imagePath" + (i % 4 + 1)).getValue();
                experiences.get(i).setBanner(imagepPath + "/" + experiences.get(i).getBanner());
                String content = experiences.get(i).getContent();
                if (content.indexOf("/slhImg") > 0 && content.indexOf("title=") > 0) {
                    String backgroundImageUrl = imagepPath + content.substring(content.indexOf("/slhImg") + 7, content.indexOf("title=") - 2);
                    experiences.get(i).setBackgroundImageUrl(backgroundImageUrl);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 查询优惠活动
        List<Activity> promotions = activityService.getActivitiesByType(chainCode, "promotion");
        // 数据预处理，把图片路径替换为绝对路径
        for (int j = 0; j < promotions.size(); j++) {
            promotions.get(j).setBanner(configService.getConfig(chainCode, "imagePath" + (j % 4 + 1)).getValue() + "/" + promotions.get(j).getBanner());
            if (StringUtils.isNotBlank(promotions.get(j).getBannerTra())) {
                promotions.get(j).setBannerTra(configService.getConfig(chainCode, "imagePath" + (j % 4 + 1)).getValue() + "/" + promotions.get(j).getBannerTra());
            }
        }

        // 微信精选
        List<Activity> weChats = activityService.getActivitiesByType(chainCode, "weChat");
        // 数据预处理，把图片路径替换为绝对路径
        for (int k = 0; k < weChats.size(); k++) {
            weChats.get(k).setBanner(configService.getConfig(chainCode, "imagePath" + (k % 4 + 1)).getValue() + "/" + weChats.get(k).getBanner());
        }

        //推荐位一,只取一个
        List<Activity> adsOnes = activityService.getActivitiesByType(chainCode, "ads-one");
        for (int i = 0; i < adsOnes.size(); i++){
            adsOnes.get(i).setBanner(configService.getConfig(chainCode, "imagePath" + (i % 4 + 1)).getValue() + "/" + adsOnes.get(i).getBanner());
        }

        //大中华区热门旅行
        List<Activity> chinaDestinations = activityService.getActivitiesByType(chainCode, "china-destination");
        for (int i = 0; i < chinaDestinations.size(); i++){
            chinaDestinations.get(i).setBanner(configService.getConfig(chainCode, "imagePath" + (i % 4 + 1)).getValue() + "/" + chinaDestinations.get(i).getBanner());
        }

        //国际热门旅行
        List<Activity> internationalDestinations = activityService.getActivitiesByType(chainCode, "international-destination");
        for (int i = 0; i < internationalDestinations.size(); i++){
            internationalDestinations.get(i).setBanner(configService.getConfig(chainCode, "imagePath" + (i % 4 + 1)).getValue() + "/" + internationalDestinations.get(i).getBanner());
        }

        List<HotSite> hotSiteCountries = hotSiteService.getHotSiteHasOnlineHotels(chainCode, "country");
        List<HotSite> hotSiteCities = hotSiteService.getHotSiteHasOnlineHotels(chainCode, "city");
        List<HotSite> hotSiteHotels = hotSiteService.getHotSiteHasOnlineHotels(chainCode, "hotel");
        for (HotSite hotSite : hotSiteHotels) {
            hotSite.getHotel().setHotelName(hotelService.getHotelNameForSLH(hotSite.getHotel()));
        }


        Country domestic = countryService.getCountry(chainCode, "CN", "online", "online");
        Iterator<City> itDomesticHotCities = domestic.getCities().iterator();
        while (itDomesticHotCities.hasNext()) {
            City city = itDomesticHotCities.next();
            List<Hotel> hotels = hotelService.getHotelsByCityCode(chainCode, city.getCityCode(), null, 0, 99999);
            if (hotels.size() == 0) {
                itDomesticHotCities.remove();
            }
        }
        Iterator<Hotel> itDomesticHotHotels = domestic.getHotHotels().iterator();
        while (itDomesticHotHotels.hasNext()) {
            Hotel hotel = itDomesticHotHotels.next();
            if ("offline".equals(hotel.getOnlineStatus())) {
                itDomesticHotHotels.remove();
            } else {
                hotel.setCityName(hotelService.getHotelCityNameForSLH(hotel));
            }
        }
        String title = String.format("全球奢华精品酒店 | 全球%d多个国家%d多家独立酒店独一无二的入住体验", FrontCountryCount, FrontHotelCount);
        String keywords = "SLH,全球奢华精品酒店,small luxury hotels,全球小型豪华酒店,世界小型豪华酒店,世界小型奢华酒店,全球小型奢华酒店";
        String description = String.format("欢迎您下榻全球奢华精品酒店！我们位于全球各地的最佳奢华精品酒店可为您提供独一无二的入住体验。目前，我们在全球的%d多个国家共有%d多家独立奢华精品酒店，在这些精品酒店里，客人们可以享受到至臻完美的服务!注册会员，可享更多优惠福利。免费服务电话：************", FrontCountryCount, FrontHotelCount);
        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            keywords = ZHConverter.convert(keywords, ZHConverter.TRADITIONAL);
            description = ZHConverter.convert(description, ZHConverter.TRADITIONAL);
            continents.parallelStream().forEach(continent -> {
                continent.setName(ZHConverter.convert(continent.getName(), ZHConverter.TRADITIONAL));
                continent.getCountries().parallelStream().forEach(country ->
                        country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL))
                );
                continent.getHotCities().parallelStream().forEach(city ->
                        city.setCityName(ZHConverter.convert(city.getCityName(), ZHConverter.TRADITIONAL))
                );
                continent.getHotHotels().parallelStream().forEach(hotHotel -> {
                    hotHotel.setCityName(ZHConverter.convert(hotHotel.getCityName(), ZHConverter.TRADITIONAL));
                    hotHotel.setHotelName(ZHConverter.convert(hotHotel.getHotelName(), ZHConverter.TRADITIONAL));
                });
            });
            hotSiteCountries.parallelStream().forEach(country ->
                    country.setName(ZHConverter.convert(country.getName(), ZHConverter.TRADITIONAL)));
            hotSiteCities.parallelStream().forEach(city ->
                    city.setName(ZHConverter.convert(city.getName(), ZHConverter.TRADITIONAL))
            );
            domestic.getCities().parallelStream().forEach(city ->
                    city.setCityName(ZHConverter.convert(city.getCityName(), ZHConverter.TRADITIONAL))
            );
            domestic.getHotHotels().parallelStream().forEach(hotHotel -> {
                hotHotel.setCityName(ZHConverter.convert(hotHotel.getCityName(), ZHConverter.TRADITIONAL));
                hotHotel.setHotelName(ZHConverter.convert(hotHotel.getHotelName(), ZHConverter.TRADITIONAL));
            });
            experiences.parallelStream().forEach(activity ->
                    activity.setTitle(ZHConverter.convert(activity.getTitle(), ZHConverter.TRADITIONAL))
            );
            promotions.parallelStream().forEach(activity ->
                    activity.setTitle(ZHConverter.convert(activity.getTitle(), ZHConverter.TRADITIONAL))
            );
            weChats.parallelStream().forEach(activity ->
                    activity.setTitle(ZHConverter.convert(activity.getTitle(), ZHConverter.TRADITIONAL))
            );
            hotSiteHotels.parallelStream().forEach(hotSite -> {
                Hotel hotel = hotSite.getHotel();
                hotel.setCountryName(ZHConverter.convert(hotel.getCountryName(), ZHConverter.TRADITIONAL));
                hotel.setCityName(ZHConverter.convert(hotel.getCityName(), ZHConverter.TRADITIONAL));
                hotel.setHotelName(ZHConverter.convert(hotel.getHotelName(), ZHConverter.TRADITIONAL));
            });
        } else {
            return "redirect:/error";
        }
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
            for (Hotel hotel : continent.getHotHotels()) {
                hotel.setCityName(hotelService.getHotelCityNameForSLH(hotel));
                hotel.setHotelName(hotelService.getHotelNameForSLH(hotel));
            }
        }
        model.addAttribute("locale", localeAttribute);
        model.addAttribute("queryCache", gson.toJson(queryCache));
        model.addAttribute("continents", continents);
        model.addAttribute("hotSiteCountries", hotSiteCountries);
        model.addAttribute("hotSiteCities", hotSiteCities);
        model.addAttribute("hotSiteHotels", hotSiteHotels);
        model.addAttribute("domestic", domestic);
        model.addAttribute("experiences", experiences);
        model.addAttribute("promotions", promotions);
        model.addAttribute("weChats", weChats);
        model.addAttribute("title", title);
        model.addAttribute("keywords", keywords);
        model.addAttribute("description", description);
        model.addAttribute("adsOnes", adsOnes);
        model.addAttribute("chinaDestinations", chinaDestinations);
        model.addAttribute("internationalDestinations", internationalDestinations);

        model.addAttribute("currentPage", "index");

        HttpLog.addAccessLog("slh", "trail-api-access", "info", "首页访问", request);
        return "index";
    }

    @RequestMapping(value = {"/hotel/{hotelLink}"})
    public String toHotelDetail(HttpSession session, HttpServletRequest request, HttpServletResponse response, @PathVariable(value = "hotelLink", required = false) String hotelLink,
                                @RequestParam(value = "currency", required = false) String currency,
                                @RequestParam(value = "checkinDate", required = false, defaultValue = "") String checkinDate,
                                @RequestParam(value = "checkoutDate", required = false, defaultValue = "") String checkoutDate,
                                @RequestParam(value = "adultNumber", required = false, defaultValue = "2") Integer adultNumber,
                                @RequestParam(value = "roomNumber", required = false, defaultValue = "1") Integer roomNumber,
                                @RequestParam(value = "locale", required = false) String locale, Model model) throws IOException {

        locale = localeConfig;
        if ("".equals(checkinDate)) {
            checkinDate = SlhUtils.getDefaultCheckInDate();
        }
        if ("".equals(checkoutDate)) {
            checkoutDate = SlhUtils.getDefaultCheckoutDate();
        }
        int nights = DateUtil.daysBetween(DateUtil.stringFormatToDate(checkinDate, "yyyy-MM-dd"), DateUtil.stringFormatToDate(checkoutDate, "yyyy-MM-dd"));
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);

        Hotel hotel = hotelService.getHotelByLink2(this.chainCode, hotelLink);
        // 对旧的酒店链接设置301重定向
// 确保 hotel 不为空且 hotelLink 不等于酒店链接
        if (hotel != null && !hotelLink.equals(hotel.getHotelLink())) {
            // 设置目标 URL 中的关键字部分
            String keyWord = "/hotel/";
            String param = "";

            // 确定 locale 和 currency 参数
            if (StringUtils.isNotEmpty(locale) && "zh_TW".equals(locale)) {
                param = "?locale=zh_TW";
                if (StringUtils.isNotEmpty(currency)) {
                    param += "&currency=" + currency;
                } else {
                    param += "&currency=twd";  // 默认货币为 TWD
                }
            } else {
                param = "?locale=zh_CN";  // 默认地区为简体中文
            }

            // 设置 HTTP 状态码为 301（永久重定向）
            response.setStatus(301);

            // 构建重定向 URL，确保包含前缀和酒店链接
            String redirectUrl = frontApiUrl
                    + keyWord
                    + hotel.getHotelLink()
                    + param;

            // 设置 Location 头部，执行 301 重定向
            response.setHeader("Location", redirectUrl);

            // 关闭连接，避免保持连接
            response.setHeader("Connection", "close");
        }

        model.addAttribute("hotelLink", hotelLink);
        String title = "";
        List<Transport> transports;
        List<Landmark> landmarks;
        if (hotel != null) {
            String imagePath = configService.getConfig(chainCode, "imagePath").getValue();
            HotelImage hotelImage = hotelImageService.getHotelImageCover(hotel.getChainCode(), hotel.getHotelCode());
            if (hotelImage != null) {
                String coverUrl = imagePath + StringUtils.defaultIfEmpty(hotelImage.getUrl(), "");
                hotel.setCoverUrl(coverUrl);
            }

            //设置酒店banner图片，多张
            List<HotelImage> banners = hotelImageService.getHotelImagesByType(chainCode, hotel.getHotelCode(), "1").stream()
                    .filter(banner -> {
                        // 获取 sourceUrl，如果 sourceUrl 为空，则使用 url
                        String effectiveUrl = banner.getSourceUrl() != null && !banner.getSourceUrl().isEmpty()
                                ? banner.getSourceUrl()
                                : imagePath + banner.getUrl();
                        return effectiveUrl != null && !effectiveUrl.isEmpty();
                    })
                    .collect(Collectors.toList());
            banners.forEach(banner -> {
                // 获取原始 URL
                String originalUrl = banner.getSourceUrl() != null && !banner.getSourceUrl().isEmpty()
                        ? banner.getSourceUrl()
                        : (banner.getUrl() != null ? imagePath + banner.getUrl() : null);
                // 找到文件名最后一个下划线和扩展名的位置
                int lastUnderscoreIndex = originalUrl.lastIndexOf('_');
                int extensionIndex = originalUrl.lastIndexOf('.');

                // 确保下划线和扩展名的位置合法
                if (lastUnderscoreIndex > -1 && extensionIndex > lastUnderscoreIndex) {
                    // 提取下划线后的部分
                    String suffix = originalUrl.substring(lastUnderscoreIndex, extensionIndex);

                    // 如果是 _O，替换为 _S
                    if ("_O".equals(suffix)) {
                        originalUrl = originalUrl.substring(0, lastUnderscoreIndex) + "_S" + originalUrl.substring(extensionIndex);
                    }
                }

                // 设置更新后的 URL
                banner.setSourceUrl(originalUrl);
            });
            model.addAttribute("banners", banners);

            //修改封面滚动图，改为使用gallery
            List<HotelImage> banner = new ArrayList<HotelImage>();
            if (CollectionUtils.isNotEmpty(banners)) {
                banner.add(banners.get(0));
            }
            hotel.setBanners(banner);
            model.addAttribute("hotelCode", hotel.getHotelCode());
            model.addAttribute("sabreCode", slhHotelCodeService.getSabreCode(hotel.getHotelCode()));
            //保存trust_code用于gallery
            String trustCode = slhHotelCodeService.getTrustCode(hotel.getHotelCode());
            model.addAttribute("trustCode", trustCode);
            if (StringUtils.isNotEmpty(hotel.getOriginChainCode())) {
                model.addAttribute("originChainCode", hotel.getOriginChainCode());
            } else {
                model.addAttribute("originChainCode", ChainCodeEnum.getChainId(chainCode));
            }
            if ("CN".equals(hotel.getCountryCode())) {
                model.addAttribute("currentPage", "domestic");
            } else {
                model.addAttribute("currentPage", "international");
            }

            // 查询feature信息
            if (hotel.getFeature() != null && hotel.getFeature().length() > 0) {
                List<String> featureName = gson.fromJson(hotel.getFeature(), new TypeToken<List<String>>() {
                }.getType());
                if (featureName != null && featureName.size() > 0) {
                    // sql in有最大长度限制，截取成多段查询
                    List<List<String>> featuresList = SlhUtils.splitList(featureName, 500);
                    Map<String, List<SLHFeature>> featuresMap = new HashMap<>();
                    List<String> types = slhFeatureService.getFeatureType(chainCode);
                    for (String type : types) {
                        List<SLHFeature> features = new ArrayList<>();
                        for (List<String> list : featuresList) {
                            features.addAll(slhFeatureService.getFeatureCodesByChainCode(chainCode, hotel.getHotelCode(), type, list));
                        }
                        if (features.size() > 0) {
                            if (type.equals("儿童政策") || type.equals("宠物政策")) {
                                model.addAttribute("isPolicies", true);// 是否有酒店政策
                            } else {
                                model.addAttribute("isFeatures", true);// 是否有酒店设施
                            }
                        }
                        featuresMap.put(type, features);
                    }
                    model.addAttribute("featuresMap", featuresMap);
                }
            }
            String offeredService = hotel.getOfferedService();
            if (!StringUtils.isEmpty(offeredService)) {
                JsonObject json = gson.fromJson(offeredService, JsonObject.class);
                if (json.get("isKidsStayFree") != null && !json.get("isKidsStayFree").isJsonNull()) {
                    model.addAttribute("isKidsStayFree", json.get("isKidsStayFree").getAsBoolean());
                }
                if (json.get("childrenMenu") != null && !json.get("childrenMenu").isJsonNull()) {
                    model.addAttribute("childrenMenu", json.get("childrenMenu").getAsBoolean());
                }
            }

            model.addAttribute("maxChildAge", hotel.getMaxAge());

            // 查询可以接受的信用卡信息
            if (hotel.getAcceptedCreditCardStr() != null && hotel.getAcceptedCreditCardStr().length() > 0) {
                String acceptedCreditCardStr = hotel.getAcceptedCreditCardStr();
                String[] acceptedCreditCards = acceptedCreditCardStr.substring(1, hotel.getAcceptedCreditCardStr().length() - 1).split(",");
                for (String creditCard : acceptedCreditCards) {
                    if (acceptedCreditCardStr.contains("CB") && acceptedCreditCardStr.contains("DN")) {
                        if (creditCard.contains("CB")) continue;
                    }
                    if (creditCard.contains("FB")) continue;
                    AcceptedCreditCard acceptedCreditCard = new AcceptedCreditCard();
                    acceptedCreditCard.setCcCode(creditCard.substring(1, creditCard.length() - 1));
                    hotel.getAcceptedCreditCard().add(acceptedCreditCard);
                }
            }

            landmarks = landmarkService.getLandmarks(chainCode, hotel.getHotelCode());

            transports = transportService.getTransports(chainCode, hotel.getHotelCode());


            // TODO: 切换sabre修改为查询所有房子
//			Map<String, Object> roomRateMap = rateSearchService.searchRoomsRate(session.getId(),chainCode, hotel.getHotelCode(),
//					"room", adultNumber, DateUtil.strToDate(checkinDate, "yyyy-MM-dd"), nights, roomNumber);
//            Map<String, Object> roomRateMap = rateSearchService.searchRoomsRateSLHSabre(session.getId(), chainCode, hotel.getHotelCode(),
//                    "room", adultNumber, DateUtil.strToDate(checkinDate, "yyyy-MM-dd"), nights, roomNumber);
//            model.addAttribute("roomRateMap", roomRateMap);
            //查询酒店所有房型
            List<Room> hotelRooms = roomService.getRoomsByHotel(chainCode, hotel.getHotelCode());
            //遍历房型图片，并修改房型图片的url
            for (Room room : hotelRooms) {
                for (HotelImage hi : room.getImages()) {
                    //设置图片为 sourceUrl,判断 sourceurl 是否为空
                    if (hi.getSourceUrl() != null) {
                        if (hi.getSourceUrl().contains("vfmii") && hi.getUrl() != null){
                            hi.setSourceUrl(hi.getUrl());
                        }else {
                            hi.setUrl(hi.getSourceUrl().replaceAll("(?i)(.*)(_O\\.|_S\\.)", "$1_J."));
                            hi.setSourceUrl(hi.getSourceUrl().replaceAll("(?i)(.*)(_O\\.|_S\\.)", "$1_R."));
                        }
                    }else {
                        hi.setSourceUrl(hi.getUrl());
                    }

                }
            }
            model.addAttribute("hotelRooms", hotelRooms);

            final HotelListRequest hotelListRequest = new HotelListRequest();
            hotelListRequest.setChainCode(chainCode);
            hotelListRequest.setHotelCodes(new ArrayList<String>(Arrays.asList(hotel.getHotelCode().split(","))));
            hotelListRequest.setAdultsPerRoom(adultNumber);
            hotelListRequest.setIndate(DateUtil.strToDate(checkinDate, "yyyy-MM-dd"));
            hotelListRequest.setNights(nights);
            hotelListRequest.setRooms(roomNumber);
            //TODO 待优化，前面已经查了一次房型价
//			List<Hotel> hotels = rateSearchService.searchHotelsWithCache(session.getId(), chainCode, new ArrayList<String>(Arrays.asList(hotel.getHotelCode().split(","))), hotelListRequest.getIndate(), nights, hotelListRequest.getAdultsPerRoom(), hotelListRequest.getChildrenPerRoom(), hotelListRequest.getChildAges(), hotelListRequest.getRooms(), "room", null, null).getData();
//            List<Hotel> hotels = rateSearchService.searchHotelsMinRateWithCache(session.getId(), chainCode, new ArrayList<String>(Arrays.asList(hotel.getHotelCode().split(","))), hotelListRequest.getIndate(), nights, hotelListRequest.getAdultsPerRoom(), hotelListRequest.getChildrenPerRoom(), hotelListRequest.getChildAges(), hotelListRequest.getRooms(), "room", null, null).getData();
//            model.addAttribute("hotelMinRate", hotels);
            model.addAttribute("currency", currency);

//            hotel.setNearbyHotels(rateSearchService.getNearByHotels(session.getId(), chainCode, hotel.getHotelCode(), "room",
//                    adultNumber, DateUtil.strToDate(checkinDate, "yyyy-MM-dd"), nights, roomNumber));
            if (!"".equals(hotel.getHotelName())) {
                title = hotel.getHotelName().replace("<br>", "") + " | slh全球奢华精品酒店";
            } else if (!"".equals(hotel.getHotelAliasEn())) {
                title = hotel.getHotelAliasEn() + " | slh全球奢华精品酒店";
            } else {
                title = hotel.getHotelNameEn() + " | slh全球奢华精品酒店";
            }
            model.addAttribute("keywords", hotel.getCountryName() + " " + hotel.getCityName() + " " + hotel.getHotelName() + "，" + hotel.getHotelNameEn() + ", SLH全球奢华酒店");
            if (hotel.getUniqueQuelities() != null){
                model.addAttribute("description", hotel.getUniqueQuelities().replace("<ul>", "").replace("</ul>", "").replace("<li>", "").replace("</li>", "").replace("\n", " "));
            }else{
                model.addAttribute("description", hotel.getHotelNameEn().replace("<br>", ""));
            }

            Country country = countryService.getCountry(this.chainCode, hotel.getCountryCode(), null);
            City city = cityService.getCity(this.chainCode, hotel.getCityCode(), null);
            //获取城市酒店
            if (city != null) {
                model.addAttribute("cityHotels", hotelService.getHotelsByCityCode(chainCode, city.getCityCode(), "ORDER BY RAND() ", 0, 11).stream()
                        .filter(cityHotel -> !cityHotel.getHotelCode().equals(hotel.getHotelCode())).collect(Collectors.toList()));
            }
            model.addAttribute("country", country);
            model.addAttribute("city", city);
        } else {
            model.addAttribute("hotelCode", "");
            model.addAttribute("currentPage", "");
            this.setErrorSession(session, chainCode, "404", "酒店不存在");
            return "redirect:/error";
        }
        model.addAttribute("checkinDate", checkinDate);
        model.addAttribute("checkoutDate", checkoutDate);
        model.addAttribute("nights", nights);
        model.addAttribute("adultNumber", adultNumber);
        model.addAttribute("roomNumber", roomNumber);
        model.addAttribute("activityLink", "");

        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            if (landmarks != null && !landmarks.isEmpty() && !"HUTXGUH".equals(hotel.getHotelCode())) {
                landmarks.parallelStream().forEach(landmark ->
                        landmark.setName(ZHConverter.convert(landmark.getName(), ZHConverter.TRADITIONAL)));
            }
            //繁体站：不针对港澳台酒店进行二次翻译：如鹿港永樂酒店HUTXGUH
            if (transports != null && !transports.isEmpty() && !"HUTXGUH".equals(hotel.getHotelCode())) {
                transports.parallelStream().forEach(transport ->
                        transport.setName(ZHConverter.convert(transport.getName(), ZHConverter.TRADITIONAL)));
            }
            continents.parallelStream().forEach(continent ->
                    continent.getCountries().parallelStream().forEach(country ->
                            country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL))
                    )
            );
            hotel.setHotelName(ZHConverter.convert(hotel.getHotelName(), ZHConverter.TRADITIONAL));
            hotel.setCountryName(ZHConverter.convert(hotel.getCountryName(), ZHConverter.TRADITIONAL));
            hotel.setAddress(ZHConverter.convert(hotel.getAddress(), ZHConverter.TRADITIONAL));
            hotel.setBrief(ZHConverter.convert(hotel.getBrief(), ZHConverter.TRADITIONAL));
            hotel.setUniqueQuelities(ZHConverter.convert(hotel.getUniqueQuelities(), ZHConverter.TRADITIONAL));
            hotel.setDetail(ZHConverter.convert(hotel.getDetail(), ZHConverter.TRADITIONAL));
            hotel.setCityName(ZHConverter.convert(hotel.getCityName(), ZHConverter.TRADITIONAL));
            hotel.getNearbyHotels().parallelStream().forEachOrdered(nearByHotel -> {
                nearByHotel.setHotelName(ZHConverter.convert(nearByHotel.getHotelName(), ZHConverter.TRADITIONAL));
                nearByHotel.setCityName(ZHConverter.convert(nearByHotel.getCityName(), ZHConverter.TRADITIONAL));
                nearByHotel.setCountryName(ZHConverter.convert(nearByHotel.getCountryName(), ZHConverter.TRADITIONAL));
            });
            if (model.asMap().get("featuresMap") != null) {
                Map<String, List<SLHFeature>> feature = (Map<String, List<SLHFeature>>) model.asMap().get("featuresMap");
                Map<String, List<SLHFeature>> ZHfeature = new LinkedHashMap<>();
                for (Map.Entry<String, List<SLHFeature>> entry : feature.entrySet()) {
                    String key = ZHConverter.convert(entry.getKey(), ZHConverter.TRADITIONAL);
                    List<SLHFeature> value = entry.getValue();
                    value.parallelStream().forEachOrdered(slhFeature ->
                            slhFeature.setName(ZHConverter.convert(slhFeature.getName(), ZHConverter.TRADITIONAL))
                    );
                    ZHfeature.put(key, value);
                }
                model.addAttribute("featuresMap", ZHfeature);
            }
            if (model.asMap().get("roomRateMap") != null) {
                Map<String, Object> roomRateMap = (Map<String, Object>) model.asMap().get("roomRateMap");
                List<Room> roomList = (List<Room>) roomRateMap.get("rooms");
                roomList.parallelStream().forEachOrdered(room -> {
                    room.setRoomDescription(ZHConverter.convert(room.getRoomDescription(), ZHConverter.TRADITIONAL));
                    room.setRoomText(ZHConverter.convert(room.getRoomText(), ZHConverter.TRADITIONAL));
                });
                roomRateMap.put("rooms", roomList);
                model.addAttribute("roomRateMap", roomRateMap);
            }
        } else {
            return "redirect:/error";
        }
        for (Continent continent : continents) {
            model.addAttribute("continent_" + continent.getNameEn().replace(" ", ""), continent);
        }

        //查询 iconfeature
        List<HotelIconFeatures> iconFeatures = hotelIconFeatureService.getHotelIconFeatures(chainCode, hotel.getHotelCode());
        //打印ext
        HotelExt hotelExt = hotelService.parseHotelExt(hotel.getExt());
        model.addAttribute("iconFeatures", iconFeatures);
        model.addAttribute("locale", localeAttribute);
        model.addAttribute("hotel", hotel);
        model.addAttribute("hotelExt", hotelExt);
        model.addAttribute("title", title);
        model.addAttribute("landmarks", landmarks);
        model.addAttribute("transports", transports);
        //获取酒店 service fueature
        HttpLog.addAccessLog("slh", "trail-api-access", "info", "酒店详情页访问" + hotelLink, (HttpServletRequest) request);
        return "hotel-detail";
    }

    @RequestMapping(value = {"/hotel/iata/{hotelLink}"})
    public String toHotelDetailIfIata(HttpSession session, HttpServletRequest request, @PathVariable(value = "hotelLink", required = false) String hotelLink,
                                      @RequestParam(value = "checkinDate", required = false, defaultValue = "") String checkinDate,
                                      @RequestParam(value = "checkoutDate", required = false, defaultValue = "") String checkoutDate,
                                      @RequestParam(value = "adultNumber", required = false, defaultValue = "2") Integer adultNumber,
                                      @RequestParam(value = "roomNumber", required = false, defaultValue = "1") Integer roomNumber,
                                      @RequestParam(value = "iataNumber", required = true) String iataNumber,
                                      @RequestParam(value = "rateAccessCode", required = true) String rateAccessCode,
                                      @RequestParam(value = "locale", required = false) String locale,
                                      Model model) {
        locale = localeConfig;
        if ("".equals(checkinDate)) {
            checkinDate = SlhUtils.getDefaultCheckInDate();
        }
        if ("".equals(checkoutDate)) {
            checkoutDate = SlhUtils.getDefaultCheckoutDate();
        }
        int nights = DateUtil.daysBetween(DateUtil.stringFormatToDate(checkinDate, "yyyy-MM-dd"), DateUtil.stringFormatToDate(checkoutDate, "yyyy-MM-dd"));
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);

        Hotel hotel = hotelService.getHotelByLink2(this.chainCode, hotelLink);
        model.addAttribute("hotelLink", hotelLink);
        String title = "";
        List<Transport> transports;
        List<Landmark> landmarks;
        if (hotel != null) {
            String imagePath = configService.getConfig(chainCode, "imagePath").getValue();
            HotelImage hotelImage = hotelImageService.getHotelImageCover(hotel.getChainCode(), hotel.getHotelCode());
            if (hotelImage != null) {
                String coverUrl = imagePath + StringUtils.defaultIfEmpty(hotelImage.getUrl(), "");
                hotel.setCoverUrl(coverUrl);
            }

            //设置酒店banner图片，多张
            List<HotelImage> banners = hotelImageService.getHotelImagesByType(chainCode, hotel.getHotelCode(), "1");
            banners.forEach(x -> x.setUrl(imagePath + x.getUrl()));

            //修改封面滚动图，改为使用gallery
            List<HotelImage> banner = new ArrayList<HotelImage>();
            banner.add(banners.get(0));

            hotel.setBanners(banner);
            model.addAttribute("hotelCode", hotel.getHotelCode());
            model.addAttribute("sabreCode", slhHotelCodeService.getSabreCode(hotel.getHotelCode()));
            //保存trust_code用于gallery
            String trustCode = slhHotelCodeService.getTrustCode(hotel.getHotelCode());
            model.addAttribute("trustCode", trustCode);
            if (StringUtils.isNotEmpty(hotel.getOriginChainCode())) {
                model.addAttribute("originChainCode", hotel.getOriginChainCode());
            } else {
                model.addAttribute("originChainCode", ChainCodeEnum.getChainId(chainCode));
            }
            if ("CN".equals(hotel.getCountryCode())) {
                model.addAttribute("currentPage", "domestic");
            } else {
                model.addAttribute("currentPage", "international");
            }

            // 查询feature信息
            if (hotel.getFeature() != null && hotel.getFeature().length() > 0) {
                List<String> featureName = gson.fromJson(hotel.getFeature(), new TypeToken<List<String>>() {
                }.getType());
                if (featureName != null && featureName.size() > 0) {
                    // sql in有最大长度限制，截取成多段查询
                    List<List<String>> featuresList = SlhUtils.splitList(featureName, 500);
                    Map<String, List<SLHFeature>> featuresMap = new HashMap<>();
                    List<String> types = slhFeatureService.getFeatureType(chainCode);
                    for (String type : types) {
                        List<SLHFeature> features = new ArrayList<>();
                        for (List<String> list : featuresList) {
                            features.addAll(slhFeatureService.getFeatureCodesByChainCode(chainCode, hotel.getHotelCode(), type, list));
                        }
                        if (features.size() > 0) {
                            if (type.equals("儿童政策") || type.equals("宠物政策")) {
                                model.addAttribute("isPolicies", true);// 是否有酒店政策
                            } else {
                                model.addAttribute("isFeatures", true);// 是否有酒店设施
                            }
                        }
                        featuresMap.put(type, features);
                    }
                    model.addAttribute("featuresMap", featuresMap);
                }
            }

            String offeredService = hotel.getOfferedService();
            if (!StringUtils.isEmpty(offeredService)) {
                JsonObject json = gson.fromJson(offeredService, JsonObject.class);
                if (json.get("isKidsStayFree") != null && !json.get("isKidsStayFree").isJsonNull()) {
                    model.addAttribute("isKidsStayFree", json.get("isKidsStayFree").getAsBoolean());
                }
                if (json.get("childrenMenu") != null && !json.get("childrenMenu").isJsonNull()) {
                    model.addAttribute("childrenMenu", json.get("childrenMenu").getAsBoolean());
                }
            }

            model.addAttribute("maxChildAge", hotel.getMaxAge());

            // 查询可以接受的信用卡信息
            if (hotel.getAcceptedCreditCardStr() != null && hotel.getAcceptedCreditCardStr().length() > 0) {
                String[] acceptedCreditCards = hotel.getAcceptedCreditCardStr()
                        .substring(1, hotel.getAcceptedCreditCardStr().length() - 1).split(",");
                for (String creditCard : acceptedCreditCards) {
                    AcceptedCreditCard acceptedCreditCard = new AcceptedCreditCard();
                    acceptedCreditCard.setCcCode(creditCard.substring(1, creditCard.length() - 1));
                    hotel.getAcceptedCreditCard().add(acceptedCreditCard);
                }
            }

            landmarks = landmarkService.getLandmarks(chainCode, hotel.getHotelCode());
            transports = transportService.getTransports(chainCode, hotel.getHotelCode());
//			Map<String, Object> roomRateMap = rateSearchService.searchRoomsRate(session.getId(),chainCode, hotel.getHotelCode(),
//					"room", adultNumber, DateUtil.strToDate(checkinDate, "yyyy-MM-dd"), nights, roomNumber);
            Map<String, Object> roomRateMap = rateSearchService.searchRoomsRateSLHSabre(session.getId(), chainCode, hotel.getHotelCode(),
                    "room", adultNumber, DateUtil.strToDate(checkinDate, "yyyy-MM-dd"), nights, roomNumber);
            model.addAttribute("roomRateMap", roomRateMap);

            final HotelListRequest hotelListRequest = new HotelListRequest();
            hotelListRequest.setChainCode(chainCode);
            hotelListRequest.setHotelCodes(new ArrayList<String>(Arrays.asList(hotel.getHotelCode().split(","))));
            hotelListRequest.setAdultsPerRoom(adultNumber);
            hotelListRequest.setIndate(DateUtil.strToDate(checkinDate, "yyyy-MM-dd"));
            hotelListRequest.setNights(nights);
            hotelListRequest.setRooms(roomNumber);
            //TODO 待优化，前面已经查了一次房型价
//			List<Hotel> hotels = rateSearchService.searchHotelsWithCache(session.getId(), chainCode, new ArrayList<String>(Arrays.asList(hotel.getHotelCode().split(","))), hotelListRequest.getIndate(), nights, hotelListRequest.getAdultsPerRoom(), hotelListRequest.getChildrenPerRoom(), hotelListRequest.getChildAges(), hotelListRequest.getRooms(), "room", null, null).getData();
//			model.addAttribute("hotelMinRate", hotels);
            List<Hotel> hotels = rateSearchService.searchHotelsMinRateWithCache(session.getId(), chainCode, new ArrayList<String>(Arrays.asList(hotel.getHotelCode().split(","))), hotelListRequest.getIndate(), nights, hotelListRequest.getAdultsPerRoom(), hotelListRequest.getChildrenPerRoom(), hotelListRequest.getChildAges(), hotelListRequest.getRooms(), "room", null, null).getData();
            model.addAttribute("hotelMinRate", hotels);

            hotel.setNearbyHotels(rateSearchService.getNearByHotels(session.getId(), chainCode, hotel.getHotelCode(), "room",
                    adultNumber, DateUtil.strToDate(checkinDate, "yyyy-MM-dd"), nights, roomNumber));

            if (!"".equals(hotel.getHotelName())) {
                title = hotel.getHotelName().replace("<br>", "") + " | slh全球奢华精品酒店";
            } else if (!"".equals(hotel.getHotelAliasEn())) {
                title = hotel.getHotelAliasEn() + " | slh全球奢华精品酒店";
            } else {
                title = hotel.getHotelNameEn() + " | slh全球奢华精品酒店";
            }
            model.addAttribute("keywords", hotel.getCountryName() + " " + hotel.getCityName() + " " + hotel.getHotelName() + "，" + hotel.getHotelNameEn() + ", SLH全球奢华酒店");
            model.addAttribute("description", hotel.getUniqueQuelities().replace("<ul>", "").replace("</ul>", "").replace("<li>", "").replace("</li>", "").replace("\n", " "));

            Country country = countryService.getCountry(this.chainCode, hotel.getCountryCode(), null);
            City city = cityService.getCity(this.chainCode, hotel.getCityCode(), null);
            model.addAttribute("country", country);
            model.addAttribute("city", city);
        } else {
            model.addAttribute("hotelCode", "");
            model.addAttribute("currentPage", "");
            this.setErrorSession(session, chainCode, "404", "酒店不存在");
            return "redirect:/error";
        }
        model.addAttribute("checkinDate", checkinDate);
        model.addAttribute("checkoutDate", checkoutDate);
        model.addAttribute("nights", nights);
        model.addAttribute("adultNumber", adultNumber);
        model.addAttribute("roomNumber", roomNumber);
        model.addAttribute("activityLink", "");
        model.addAttribute("iataNumber", iataNumber);
        model.addAttribute("rateAccessCode", rateAccessCode);
        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            if (landmarks != null && !landmarks.isEmpty() && !"HUTXGUH".equals(hotel.getHotelCode())) {
                landmarks.parallelStream().forEach(landmark ->
                        landmark.setName(ZHConverter.convert(landmark.getName(), ZHConverter.TRADITIONAL)));
            }
            if (transports != null && !transports.isEmpty() && !"HUTXGUH".equals(hotel.getHotelCode())) {
                transports.parallelStream().forEach(transport ->
                        transport.setName(ZHConverter.convert(transport.getName(), ZHConverter.TRADITIONAL)));
            }
            continents.parallelStream().forEach(continent ->
                    continent.getCountries().parallelStream().forEach(country ->
                            country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL))
                    )
            );
            hotel.setHotelName(ZHConverter.convert(hotel.getHotelName(), ZHConverter.TRADITIONAL));
            hotel.setBrief(ZHConverter.convert(hotel.getBrief(), ZHConverter.TRADITIONAL));
            hotel.setUniqueQuelities(ZHConverter.convert(hotel.getUniqueQuelities(), ZHConverter.TRADITIONAL));
            hotel.setDetail(ZHConverter.convert(hotel.getDetail(), ZHConverter.TRADITIONAL));
            hotel.setCityName(ZHConverter.convert(hotel.getCityName(), ZHConverter.TRADITIONAL));
            hotel.getNearbyHotels().parallelStream().forEachOrdered(nearByHotel -> {
                nearByHotel.setHotelName(ZHConverter.convert(nearByHotel.getHotelName(), ZHConverter.TRADITIONAL));
                nearByHotel.setCityName(ZHConverter.convert(nearByHotel.getCityName(), ZHConverter.TRADITIONAL));
                nearByHotel.setCountryName(ZHConverter.convert(nearByHotel.getCountryName(), ZHConverter.TRADITIONAL));
            });
            if (model.asMap().get("featuresMap") != null) {
                Map<String, List<SLHFeature>> feature = (Map<String, List<SLHFeature>>) model.asMap().get("featuresMap");
                Map<String, List<SLHFeature>> ZHfeature = new LinkedHashMap<>();
                for (Map.Entry<String, List<SLHFeature>> entry : feature.entrySet()) {
                    String key = ZHConverter.convert(entry.getKey(), ZHConverter.TRADITIONAL);
                    List<SLHFeature> value = entry.getValue();
                    value.parallelStream().forEachOrdered(slhFeature ->
                            slhFeature.setName(ZHConverter.convert(slhFeature.getName(), ZHConverter.TRADITIONAL))
                    );
                    ZHfeature.put(key, value);
                }
                model.addAttribute("featuresMap", ZHfeature);
            }
            if (model.asMap().get("roomRateMap") != null) {
                Map<String, Object> roomRateMap = (Map<String, Object>) model.asMap().get("roomRateMap");
                List<Room> roomList = (List<Room>) roomRateMap.get("rooms");
                roomList.parallelStream().forEachOrdered(room -> {
                    room.setRoomDescription(ZHConverter.convert(room.getRoomDescription(), ZHConverter.TRADITIONAL));
                    room.setRoomText(ZHConverter.convert(room.getRoomText(), ZHConverter.TRADITIONAL));
                });
                roomRateMap.put("rooms", roomList);
                model.addAttribute("roomRateMap", roomRateMap);
            }
        } else {
            return "redirect:/error";
        }
        for (Continent continent : continents) {
            model.addAttribute("continent_" + continent.getNameEn().replace(" ", ""), continent);
        }
        model.addAttribute("locale", localeAttribute);
        model.addAttribute("hotel", hotel);
        model.addAttribute("title", title);
        model.addAttribute("landmarks", landmarks);
        model.addAttribute("transports", transports);
        HttpLog.addAccessLog("slh", "trail-api-access", "info", "酒店详情页访问" + hotelLink, (HttpServletRequest) request);
        return "hotel-detail";
    }

    /**
     * 转到国家详情页
     */
    @RequestMapping(value = {"/country/{countryLink}"})
    public String toCountryDetail(HttpSession session, HttpServletRequest request, @PathVariable(value = "countryLink", required = false) String countryLink,
                                  @RequestParam(value = "cityLink", required = false, defaultValue = "") String cityLink,
                                  @RequestParam(value = "activityLink", required = false, defaultValue = "") String activityLink,
                                  @RequestParam(value = "checkinDate", required = false, defaultValue = "") String checkinDate,
                                  @RequestParam(value = "checkoutDate", required = false, defaultValue = "") String checkoutDate,
                                  @RequestParam(value = "adultNumber", required = false, defaultValue = "2") Integer adultNumber,
                                  @RequestParam(value = "roomNumber", required = false, defaultValue = "1") Integer roomNumber,
                                  @RequestParam(value = "locale", required = false) String locale,
                                  Model model) {
        locale = localeConfig;
        System.out.println("*************locale******" + locale + "**************");
        if ("".equals(checkinDate)) {
            checkinDate = SlhUtils.getDefaultCheckInDate();
        }
        if ("".equals(checkoutDate)) {
            checkoutDate = SlhUtils.getDefaultCheckoutDate();
        }
        String pageType = "china";
        if (!Objects.equals(countryLink, "china")){
            pageType = "";
        }
        Country country = countryService.getCountryByLink(this.chainCode, countryLink);
        if (country == null || (StringUtils.isNotBlank(locale) && !locale.equals("zh_CN") && !locale.equals("zh_TW"))) {
            this.setErrorSession(session, chainCode, "404", "国家不存在");
            return "redirect:/error";
        } else {
            this.renderCountryDetail(session.getId(), request, countryLink, cityLink, activityLink, null, checkinDate,
                    checkoutDate, adultNumber, roomNumber, locale, model, pageType);
        }

        HttpLog.addAccessLog("slh", "trail-api-access", "info", "国家详情页访问", request);
        return "hotel-list";
    }

    //国际酒店
    @RequestMapping(value = {"/internationals/hotels"})
    public String toInternationalDetail(HttpSession session, HttpServletRequest request, @PathVariable(value = "countryLink", required = false) String countryLink,
                                        @RequestParam(value = "cityLink", required = false, defaultValue = "") String cityLink,
                                        @RequestParam(value = "activityLink", required = false, defaultValue = "") String activityLink,
                                        @RequestParam(value = "checkinDate", required = false, defaultValue = "") String checkinDate,
                                        @RequestParam(value = "checkoutDate", required = false, defaultValue = "") String checkoutDate,
                                        @RequestParam(value = "adultNumber", required = false, defaultValue = "2") Integer adultNumber,
                                        @RequestParam(value = "roomNumber", required = false, defaultValue = "1") Integer roomNumber,
                                        @RequestParam(value = "locale", required = false) String locale,
                                        Model model) {
        locale = localeConfig;
        countryLink = "china";
        System.out.println("*************locale******" + locale + "**************");
        if ("".equals(checkinDate)) {
            checkinDate = SlhUtils.getDefaultCheckInDate();
        }
        if ("".equals(checkoutDate)) {
            checkoutDate = SlhUtils.getDefaultCheckoutDate();
        }
        Country country = countryService.getCountryByLink(this.chainCode, countryLink);
        if (country == null || (StringUtils.isNotBlank(locale) && !locale.equals("zh_CN") && !locale.equals("zh_TW"))) {
            this.setErrorSession(session, chainCode, "404", "国家不存在");
            return "redirect:/error";
        } else {
            this.renderCountryDetail(session.getId(), request, countryLink, cityLink, activityLink, null, checkinDate,
                    checkoutDate, adultNumber, roomNumber, locale, model, "internationals");
        }

        HttpLog.addAccessLog("slh", "trail-api-access", "info", "国家详情页访问", request);
        return "hotel-list";
    }

    //主题页面
    @RequestMapping(value = {"/theme/hotels"})
    public String toThemeDetail(HttpSession session, HttpServletRequest request, @PathVariable(value = "countryLink", required = false) String countryLink,
                                @RequestParam(value = "cityLink", required = false, defaultValue = "") String cityLink,
                                @RequestParam(value = "activityLink", required = false, defaultValue = "") String activityLink,
                                @RequestParam(value = "checkinDate", required = false, defaultValue = "") String checkinDate,
                                @RequestParam(value = "checkoutDate", required = false, defaultValue = "") String checkoutDate,
                                @RequestParam(value = "adultNumber", required = false, defaultValue = "2") Integer adultNumber,
                                @RequestParam(value = "roomNumber", required = false, defaultValue = "1") Integer roomNumber,
                                @RequestParam(value = "locale", required = false) String locale,
                                Model model) {

        if (locale == null){
            locale = localeConfig;
        }
        // 查询主题信息
        List<Activity> experiences = activityService.getActivitiesByType(chainCode, "experience");
        // 数据预处理，把图片路径替换为绝对路径
        for (int i = 0; i < experiences.size(); i++) {
            try {
                String imagepPath = configService.getConfig(chainCode, "imagePath" + (i % 4 + 1)).getValue();
                experiences.get(i).setBanner(imagepPath + "/" + experiences.get(i).getBanner());
                String content = experiences.get(i).getContent();
                if (content.indexOf("/slhImg") > 0 && content.indexOf("title=") > 0) {
                    String backgroundImageUrl = imagepPath + content.substring(content.indexOf("/slhImg") + 7, content.indexOf("title=") - 2);
                    experiences.get(i).setBackgroundImageUrl(backgroundImageUrl);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        model.addAttribute("experiences", experiences);
        model.addAttribute("locale", locale);
        HttpLog.addAccessLog("slh", "trail-api-access", "info", "国家详情页访问", request);
        return "theme";
    }


    private void renderCountryDetail(String sessionId, HttpServletRequest request, String countryLink, String cityLink, String activityLink,
                                     Integer subjectId, String checkinDate, String checkoutDate, Integer adultNumber, Integer roomNumber, String locale,
                                     Model model, String pageType) {

        if ("".equals(checkinDate)) {
            checkinDate = SlhUtils.getDefaultCheckInDate();
        }
        if ("".equals(checkoutDate)) {
            checkoutDate = SlhUtils.getDefaultCheckoutDate();
        }
        int nights = DateUtil.daysBetween(DateUtil.stringFormatToDate(checkinDate, "yyyy-MM-dd"), DateUtil.stringFormatToDate(checkoutDate, "yyyy-MM-dd"));

        this.renderContinents(chainCode, locale, model);

        Country country = null;
        City city = null;
        Activity activity = null;
        if (!"".equals(activityLink)) {
            activity = activityService.getActivityByLink(this.chainCode, activityLink);
            if (activity != null) {
                model.addAttribute("activityLink", activity.getActivityLink());
            } else {
                model.addAttribute("activityLink", "");
            }
        } else {
            model.addAttribute("activityLink", "");
        }
        model.addAttribute("countryLink", countryLink);
        if (!"".equals(countryLink)) {
            country = countryService.getCountryByLink(this.chainCode, countryLink);
            model.addAttribute("countryCode", country.getCountryCode());
        } else {
            model.addAttribute("countryCode", "");
        }
        model.addAttribute("cityLink", cityLink);
        String currentTitle = "";
        if (!"".equals(cityLink)) {
            city = cityService.getCityByLink(this.chainCode, cityLink);
            String cityName = city.getCityName();
            if (StringUtils.isNotBlank(locale) && locale.equals("zh_TW")) {
                cityName = ZHConverter.convert(cityName, ZHConverter.TRADITIONAL);
            }
            model.addAttribute("cityName", cityName);
            currentTitle = cityName;
        } else {
            model.addAttribute("cityName", "");
        }
        model.addAttribute("type", "country");
        model.addAttribute("checkinDate", checkinDate);
        model.addAttribute("checkoutDate", checkoutDate);
        model.addAttribute("nights", nights);
        model.addAttribute("adultNumber", adultNumber);
        model.addAttribute("roomNumber", roomNumber);
        model.addAttribute("pageType", pageType);
        String title = "";
        String keywords = "";
        String description = "";
        if (country != null) {
            if (country.getImageUrl() != null && country.getImageUrl().lastIndexOf(".") > 0) {
                StringBuffer banner = new StringBuffer(country.getImageUrl());
                banner.insert(country.getImageUrl().lastIndexOf("."), "_1024_510");
                model.addAttribute("banner", banner.toString());
            }

            if ("CN".equals(country.getCountryCode())) {
                String cityName = "";
                if ("".equals(cityLink) || "-1".equals(cityLink)) {
                    cityName = "大中华区";
                    keywords = "大中华区全球精品奢华酒店,大中华区SLH全球奢华酒店,大中华区大中华区奢华酒店,大中华区精品酒店";
                } else {
                    if (city != null) {
                        cityName = city.getCityName();
                        keywords = cityName + "奢华精品酒店，" + cityName + "大中华区全球精品奢华酒店,大中华区SLH全球奢华酒店,大中华区大中华区奢华酒店,大中华区精品酒店";
                        model.addAttribute("cityName", cityName);
                    }
                }
                currentTitle = cityName;
                title = cityName + "酒店 | " + cityName + "奢华精品酒店，享各大旅游热门城市奢华精品酒店入住体验";
                description = "欢迎您下榻全球奢华精品酒店！在大中华区入住全球奢华精品酒店旗下的酒店，享受奢华的大中华区美餐、室外水疗，观赏已经流传了很多世纪的精美山水画。客人们可以享受到至臻完美的服务!注册会员，可享更多优惠福利。免费服务电话：************";
            } else {
                currentTitle = country.getCountryName();
                title = country.getCountryName() + "奢华精品酒店 | " + country.getContinentName() + "奢华精品酒店，全球奢华精品酒店";
                keywords = country.getCountryName() + "奢华精品酒店 | " + country.getContinentName() + "奢华精品酒店，全球奢华精品酒店";
                description = country.getDetail();
            }
        }

        if (pageType == "internationals"){
            title =  "国际奢华精品酒店 | 奢华精品酒店，全球奢华精品酒店";
            keywords =  "国际奢华精品酒店 | 奢华精品酒店，全球奢华精品酒店";
            description =  "欢迎您下榻全球奢华精品酒店！在大中华区入住全球奢华精品酒店旗下的酒店，享受奢华的大中华区美餐、室外水疗，观赏已经流传了很多世纪的精美山水画。客人们可以享受到至臻完美的服务!注册会员，可享更多优惠福利。免费服务电话：************";
        }

//		Map<String, Object> result = rateSearchService.searchHotelsRate(sessionId,this.chainCode,
//				country != null ? country.getCountryCode() : null, city != null ? city.getCityCode() : null,
//				activity != null ? activity.getId() : -1, subjectId, "nameAsc", adultNumber, DateUtil.strToDate(checkinDate, "yyyy-MM-dd"), nights,
//				roomNumber, 1, 8);
//		model.addAttribute("hotels", result.get("hotels"));
//		model.addAttribute("hotelsCount", result.get("hotelsCount"));

        // 查询主题列表
        List<Activity> activities = null;
        if (city != null) {// 如果指定了城市则返回该城市下的所有主题
            activities = activityService.getActivitiesByCity(this.chainCode, country.getCountryCode(),
                    city.getCityCode());
        } else if (country != null) {// 返回该国家下的所有主题
            activities = activityService.getActivitiesByCountry(this.chainCode, country.getCountryCode());
        } else if (!"".equals(activityLink) && null != activity) {// 返回活动下的所有主题
            activities = activityService.getActivitiesByActivity(this.chainCode, activity.getId());
        }

        // 查询城市列表
        List<City> cities = null;
        if (activity != null) {
            cities = cityService.getCitiesByCountryAndHotelType(chainCode,
                    country != null ? country.getCountryCode() : "", activity.getId());
            currentTitle = activity.getTitle();
        } else {
            if (country != null) {
                cities = cityService.getCitiesByCountry(chainCode, country.getCountryCode(), "online", true);
            }
        }

        List<Country> countries = countryService.extractCountriesFromCites(cities);

        // 查询手机端城市列表
        List<CityGroup> cityGroups = cityService.getCityGroups(chainCode,
                country != null ? country.getCountryCode() : "", activity != null ? activity.getId() : null);

        List<CountryGroup> countryGroups = countryService.getCountryGroups(chainCode, activity != null ? activity.getId() : null);
        model.addAttribute("countryGroups", countryGroups);

        if (country != null && "CN".equals(country.getCountryCode())) {
            model.addAttribute("currentPage", "domestic");
        } else {
            model.addAttribute("currentPage", "international");
        }
        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            if (country != null) {
                country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL));
                country.setDetail(ZHConverter.convert(country.getDetail(), ZHConverter.TRADITIONAL));
            }
            currentTitle = ZHConverter.convert(currentTitle, ZHConverter.TRADITIONAL);
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            keywords = ZHConverter.convert(keywords, ZHConverter.TRADITIONAL);
            description = ZHConverter.convert(description, ZHConverter.TRADITIONAL);
            activities.parallelStream().forEachOrdered(activityPS ->
                    activityPS.setTitle(ZHConverter.convert(activityPS.getTitle(), ZHConverter.TRADITIONAL))
            );
            cities.parallelStream().forEachOrdered(cityPS ->
                    cityPS.setCityName(ZHConverter.convert(cityPS.getCityName(), ZHConverter.TRADITIONAL))
            );
            countries.parallelStream().forEachOrdered(countryPS ->
                    countryPS.setCountryName(ZHConverter.convert(countryPS.getCountryName(), ZHConverter.TRADITIONAL))
            );
            cityGroups.parallelStream().forEach(cityGroup -> {
                cityGroup.getCities().parallelStream().forEachOrdered(cityPS ->
                        cityPS.setCityName(ZHConverter.convert(cityPS.getCityName(), ZHConverter.TRADITIONAL)));
            });
            countryGroups.parallelStream().forEach(countryGroup -> {
                countryGroup.getCountries().parallelStream().forEach(countryPS ->
                        countryPS.setCountryName(ZHConverter.convert(countryPS.getCountryName(), ZHConverter.TRADITIONAL)));
            });
        }
        model.addAttribute("locale", localeAttribute);
        if (country != null) {
            model.addAttribute("countryName", country.getCountryName());
            model.addAttribute("detail", country.getDetail());
            model.addAttribute("crumbNav", country.getCountryName());
        }

        //获取酒店总数
        int allHotelCount = hotelService.search(chainCode, null, null, null, null, null, "online", 0, 999).getTotalCount();
        //中国区酒店总数
        int chineseHotelCount = hotelService.search(chainCode, null, null, null, "CN", null, "online", 0, 999).getTotalCount();
        model.addAttribute("chineseHotelCount", chineseHotelCount);
        model.addAttribute("allHotelCount", allHotelCount);
        model.addAttribute("title", title);
        model.addAttribute("keywords", keywords);
        model.addAttribute("description", description.replaceAll("\\n", ""));
        model.addAttribute("activities", activities);
        model.addAttribute("cities", cities);
        model.addAttribute("countries", countries);
        model.addAttribute("cityGroups", cityGroups);
        model.addAttribute("currentCountry", country);
        model.addAttribute("currentTitle", currentTitle);
    }

    private void renderContinents(String chainCode, String locale, Model model) {
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
        //繁体
        if (StringUtils.isNotBlank(locale) && locale.equals("zh_TW")) {
            continents.parallelStream().forEach(continent -> {
                continent.setName(ZHConverter.convert(continent.getName(), ZHConverter.TRADITIONAL));
                continent.getCountries().parallelStream().forEach(country ->
                        country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL))
                );
            });
        }
        for (Continent continent : continents) {
            model.addAttribute("continent_" + continent.getNameEn().replace(" ", ""), continent);
        }
    }

    @RequestMapping(value = {"/city/{cityLink}/hotel"})
    public String toCityDetail(HttpSession session, HttpServletRequest request, @PathVariable(value = "cityLink", required = false) String cityLink,
                               @RequestParam(value = "checkinDate", required = false, defaultValue = "") String checkinDate,
                               @RequestParam(value = "checkoutDate", required = false, defaultValue = "") String checkoutDate,
                               @RequestParam(value = "nights", required = false, defaultValue = "1") Integer nights,
                               @RequestParam(value = "adultNumber", required = false, defaultValue = "2") Integer adultNumber,
                               @RequestParam(value = "roomNumber", required = false, defaultValue = "1") Integer roomNumber,
                               @RequestParam(value = "locale", required = false) String locale,
                               Model model) {
        locale = localeConfig;
        if ("".equals(checkinDate)) {
            checkinDate = SlhUtils.getDefaultCheckInDate();
        }
        if ("".equals(checkoutDate)) {
            checkoutDate = SlhUtils.getDefaultCheckoutDate();
        }
        City city = cityService.getCityByLink(this.chainCode, cityLink);
        if (city == null || (StringUtils.isNotBlank(locale) && !locale.equals("zh_CN") && !locale.equals("zh_TW"))) {
            return "redirect:/error";
        }
        Country country = null;
        country = countryService.getCountry(this.chainCode, city.getCountryCode(), "online");
        String countryLink = "";
        if (country != null) {
            countryLink = country.getCountryLink();
        }
        this.renderCountryDetail(session.getId(), request, countryLink, cityLink, "", null,
                checkinDate, checkoutDate, adultNumber, roomNumber, locale, model, "city");

        HttpLog.addAccessLog("slh", "trail-api-access", "info", "城市详情页访问", request);
        return "hotel-list";
    }

    /**
     * 转到活动详情页
     *
     * @param activityLink
     * @param model
     * @return
     */
    @RequestMapping(value = {"/offers/{activityLink}"})
    public String toOffersDetail(HttpSession session, HttpServletRequest request,
                                 @PathVariable(value = "activityLink", required = false) String activityLink,
                                 @RequestParam(value = "countryLink", required = false, defaultValue = "") String countryLink,
                                 @RequestParam(value = "cityLink", required = false, defaultValue = "") String cityLink,
                                 @RequestParam(value = "checkinDate", required = false, defaultValue = "") String checkinDate,
                                 @RequestParam(value = "checkoutDate", required = false, defaultValue = "") String checkoutDate,
                                 @RequestParam(value = "adultNumber", required = false, defaultValue = "2") Integer adultNumber,
                                 @RequestParam(value = "roomNumber", required = false, defaultValue = "1") Integer roomNumber,
                                 @RequestParam(value = "locale", required = false) String locale,
                                 Model model) {
        locale = localeConfig;
        this.renderCountryDetail(session.getId(), request, countryLink, cityLink, activityLink, null, checkinDate,
                checkoutDate, adultNumber, roomNumber, locale, model, "offers");

        model.addAttribute("countryCode", "");
        model.addAttribute("cityLink", "");

        Activity activity = activityService.getActivityByLink(this.chainCode, activityLink);
        String title = "";
        String keywords = "";
        String description = "";
        if (activity != null) {
            activity.setBanner(configService.getConfig(chainCode, "imagePath").getValue() + "/" + activity.getBanner());
            model.addAttribute("activityLink", activity.getActivityLink());
            model.addAttribute("type", activity.getType());
            title = activity.getTitle() + " | SLH全球奢华精品酒店优惠活动";
            List<Hotel> hotels = hotelService.getHotelsByActivity(this.chainCode, activity.getId(), null, null, null);
            model.addAttribute("activityHotelCount", hotels.size());

            if ("promotion".equals(activity.getType())) {
                model.asMap().remove("currentPage");
                model.addAttribute("currentPage", "activity");
            } else {
                model.asMap().remove("currentPage");
                model.addAttribute("currentPage", "brand-story");
            }
        } else {
            model.addAttribute("activityLink", activityLink);
            model.addAttribute("type", "");
            title = "优惠活动 | SLH全球奢华精品酒店优惠活动";
            model.addAttribute("activityHotelCount", 0);
            this.setErrorSession(session, chainCode, "404", "优惠活动不存在");
            return "redirect:/error";
        }
        keywords = "全球精品奢华酒店特别优惠,SLH全球奢华酒店优惠促销,全球奢华酒店打折";
        description = "SLH全球奢华精品酒店特别优惠套餐，打折信息，会员专享特惠及节假日特别优惠套餐等，专享活动、惊喜不断。";

        String pageTitle = "优惠活动";

        String crumbNar = null;
        if (activity.getTitle().length() < 20) {
            crumbNar = activity.getTitle();
        } else {
            crumbNar = activity.getTitle().substring(0, 20) + "...";
        }
        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            keywords = ZHConverter.convert(keywords, ZHConverter.TRADITIONAL);
            description = ZHConverter.convert(description, ZHConverter.TRADITIONAL);
            crumbNar = ZHConverter.convert(crumbNar, ZHConverter.TRADITIONAL);
            pageTitle = ZHConverter.convert(pageTitle, ZHConverter.TRADITIONAL);
            if (activity != null) {
                activity.setTitle(ZHConverter.convert(activity.getTitle(), ZHConverter.TRADITIONAL));
                activity.setContent(ZHConverter.convert(activity.getContent(), ZHConverter.TRADITIONAL));
            }
        } else {
            return "redirect:/error";
        }
        model.addAttribute("locale", localeAttribute);
        model.addAttribute("crumbNav", crumbNar);
        model.addAttribute("title", title);
        model.addAttribute("keywords", keywords);
        model.addAttribute("description", description);
        model.addAttribute("activity", activity);
        model.addAttribute("pageTitle", pageTitle);
        HttpLog.addAccessLog("slh", "trail-api-access", "info", "活动详情页访问", request);
        return "activity-detail";
    }

    /*
     * 转到优惠活动列表页面
     */
    @RequestMapping(value = {"/offers"})
    public String toOffersList(HttpSession session, HttpServletRequest request, @RequestParam(value = "locale", required = false) String locale, Model model) {
        locale = localeConfig;
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);

        List<Activity> activities = activityService.getActivitiesByType(this.chainCode, "promotion");
        // 数据预处理，把图片路径替换为绝对路径
        for (Activity activity : activities) {
            activity.setBanner(
                    configService.getConfig(this.chainCode, "imagePath").getValue() + "/" + activity.getBanner());
            if (StringUtils.isNotBlank(activity.getBannerTra())) {
                activity.setBannerTra(configService.getConfig(chainCode, "imagePath").getValue() + "/" + activity.getBannerTra());
            }
        }
        model.addAttribute("type", "promotion");
        model.addAttribute("activityLink", "");

        String title = "优惠活动 | SLH全球奢华精品酒店优惠活动";
        String keywords = "全球精品奢华酒店特别优惠,SLH全球奢华酒店优惠促销,全球奢华酒店打折";
        String description = "SLH全球奢华精品酒店特别优惠套餐，打折信息，会员专享特惠及节假日特别优惠套餐等，专享活动、惊喜不断。";
        model.addAttribute("pageTitle", "优惠活动");
        model.addAttribute("crumbNav", "优惠活动");

        model.addAttribute("currentPage", "activity");
        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            keywords = ZHConverter.convert(keywords, ZHConverter.TRADITIONAL);
            description = ZHConverter.convert(description, ZHConverter.TRADITIONAL);
            continents.parallelStream().forEach(continent -> {
                continent.setName(ZHConverter.convert(continent.getName(), ZHConverter.TRADITIONAL));
                continent.getCountries().parallelStream().forEach(country ->
                        country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL))
                );
            });
            activities.parallelStream().forEachOrdered(activity -> {
                activity.setTitle(ZHConverter.convert(activity.getTitle(), ZHConverter.TRADITIONAL));
                activity.setPageDescription(ZHConverter.convert(activity.getPageDescription(), ZHConverter.TRADITIONAL));
            });
        } else {
            return "redirect:/error";
        }
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }
        model.addAttribute("locale", localeAttribute);
        model.addAttribute("continents", continents);
        model.addAttribute("title", title);
        model.addAttribute("keywords", keywords);
        model.addAttribute("description", description);

        model.addAttribute("activities", activities);
        HttpLog.addAccessLog("slh", "trail-api-access", "info", "活动列表页访问", request);

        return "activity";
    }

    /**
     * 转到主题的首页
     *
     * @param activityLink
     * @param model
     * @return
     */
    @RequestMapping(value = {"/hotel-experience/{activityLink}"})
    public String toExperienceDetail(HttpSession session, HttpServletRequest request,
                                     @PathVariable(value = "activityLink", required = false) String activityLink,
                                     @RequestParam(value = "countryCode", required = false, defaultValue = "") String countryCode,
                                     @RequestParam(value = "cityLink", required = false, defaultValue = "") String cityLink,
                                     @RequestParam(value = "checkinDate", required = false, defaultValue = "") String checkinDate,
                                     @RequestParam(value = "checkoutDate", required = false, defaultValue = "") String checkoutDate,
                                     @RequestParam(value = "adultNumber", required = false, defaultValue = "2") Integer adultNumber,
                                     @RequestParam(value = "roomNumber", required = false, defaultValue = "1") Integer roomNumber,
                                     @RequestParam(value = "locale", required = false) String locale,
                                     @RequestParam(value = "isTest", required = false, defaultValue = "0") Integer isTest,
                                     Model model) {
        locale = localeConfig;
        if ("".equals(checkinDate)) {
            checkinDate = SlhUtils.getDefaultCheckInDate();
        }
        if ("".equals(checkoutDate)) {
            checkoutDate = SlhUtils.getDefaultCheckoutDate();
        }
        int nights = DateUtil.daysBetween(DateUtil.stringFormatToDate(checkinDate, "yyyy-MM-dd"), DateUtil.stringFormatToDate(checkoutDate, "yyyy-MM-dd"));
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);

        Country country = countryService.getCountry(this.chainCode, countryCode, "online");
        Activity activity = activityService.getActivityByLink(this.chainCode, activityLink);
        model.addAttribute("cityLink", cityLink);
        model.addAttribute("cityName", "");
        model.addAttribute("countryCode", countryCode);
        model.addAttribute("type", "experience");
        model.addAttribute("activityLink", activityLink);
        model.addAttribute("checkinDate", checkinDate);
        model.addAttribute("checkoutDate", checkoutDate);
        model.addAttribute("nights", nights);
        model.addAttribute("adultNumber", adultNumber);
        model.addAttribute("roomNumber", roomNumber);
        String title = "";
        String currentTitle = "";
        if (activity != null) {
            activity.setBanner(configService.getConfig(chainCode, "imagePath").getValue() + "/" + activity.getBanner());
            model.addAttribute("banner", activity.getBanner());
            model.addAttribute("title", activity.getPageTitle());
            model.addAttribute("keywords", activity.getKeyWords());
            model.addAttribute("description", activity.getDescription());
            currentTitle = activity.getTitle();

//			City city = cityService.getCityByLink(checkoutDate, cityLink);
//			String cityCode = null;
//			if(city!=null) {
//				cityCode = city.getCityCode();
//			}
//			Integer activityId = activity.getId();
//			Integer subjectId = null;
//			Map<String, Object> result = rateSearchService.searchHotelsRate(session.getId(),this.chainCode, "", cityCode,
//					activityId, subjectId, "nameAsc", adultNumber, DateUtil.strToDate(checkinDate, "yyyy-MM-dd"), nights, roomNumber, 1, 8);
//			model.addAttribute("hotels", result.get("hotels"));
//			model.addAttribute("hotelsCount", result.get("hotelsCount"));
        } else {
            return "redirect:/error";
        }

        List<Activity> activities = activityService.getActivitiesByType(this.chainCode, "experience");
        List<City> cities = cityService.getCitiesByActivity(this.chainCode, activity.getId(), "country_code");

        List<Country> countries = countryService.extractCountriesFromCites(cities);
        // 查询手机端城市列表
        List<CityGroup> cityGroups = cityService.getCityGroups(chainCode,
                country != null ? country.getCountryCode() : null, activity.getId());

        List<CountryGroup> countryGroups = countryService.getCountryGroups(chainCode, activity.getId());
        model.addAttribute("countryGroups", countryGroups);
        model.addAttribute("currentPage", "index");
        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            currentTitle = ZHConverter.convert(currentTitle, ZHConverter.TRADITIONAL);
            continents.parallelStream().forEach(continent -> {
                continent.setName(ZHConverter.convert(continent.getName(), ZHConverter.TRADITIONAL));
                continent.getCountries().parallelStream().forEach(countryPS ->
                        countryPS.setCountryName(ZHConverter.convert(countryPS.getCountryName(), ZHConverter.TRADITIONAL))
                );
            });
            if (activity != null) {
                activity.setPageDescription(ZHConverter.convert(activity.getPageDescription(), ZHConverter.TRADITIONAL));
            }
            cities.parallelStream().forEach(cityS ->
                    cityS.setCityName(ZHConverter.convert(cityS.getCityName(), ZHConverter.TRADITIONAL))
            );
            countries.parallelStream().forEach(countryS ->
                    countryS.setCountryName(ZHConverter.convert(countryS.getCountryName(), ZHConverter.TRADITIONAL))
            );
            cityGroups.parallelStream().forEach(cityGroup ->
                    cityGroup.getCities().parallelStream().forEach(city ->
                            city.setCityName(ZHConverter.convert(city.getCityName(), ZHConverter.TRADITIONAL))
                    )
            );
            countryGroups.parallelStream().forEach(countryGroup ->
                    countryGroup.getCountries().parallelStream().forEach(countryPS ->
                            countryPS.setCountryName(ZHConverter.convert(countryPS.getCountryName(), ZHConverter.TRADITIONAL))
                    )
            );
            activities.parallelStream().forEach(activityPS ->
                    activityPS.setTitle(ZHConverter.convert(activityPS.getTitle(), ZHConverter.TRADITIONAL))
            );
        } else {
            return "redirect:/error";
        }
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }

        if (activity != null) {
            model.addAttribute("crumbNav", activity.getTitle());
            model.addAttribute("activityTitle", activity.getTitle());
            model.addAttribute("countryName", activity.getTitle());
            model.addAttribute("detail", activity.getPageDescription());
        }
        model.addAttribute("locale", localeAttribute);
        model.addAttribute("continents", continents);
        model.addAttribute("cityGroups", cityGroups);
        model.addAttribute("cities", cities);
        model.addAttribute("countries", countries);
        model.addAttribute("activities", activities);
        model.addAttribute("currentTitle", currentTitle);
        HttpLog.addAccessLog("slh", "trail-api-access", "info", "主题详情页访问", request);
        return "hotel-list";
    }

    @RequestMapping(value = {"/brand-story/{type}"})
    public String toBrandSotry(HttpSession session, HttpServletRequest request, @PathVariable(value = "type", required = false) String type,
                               @RequestParam(value = "locale", required = false) String locale, Model model) {
        locale = localeConfig;
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
        String title = "";
        String keywords = "";
        String description = "";
        model.addAttribute("type", type);
        String view;
        switch (type) {
            case "about-us":
                List<Activity> actList = activityService.getActivitiesByType(this.chainCode, type);
                if (CollectionUtils.isNotEmpty(actList)) {
                    Activity aboutUs = activityService.getActivitiesByType(this.chainCode, type).get(0);
                    aboutUs.setBanner(configService.getConfig(chainCode, "imagePath").getValue() + "/" + aboutUs.getBanner());
                    if (StringUtils.isNotBlank(locale) && locale.equals("zh_TW")) {
                        aboutUs.setContent(ZHConverter.convert(aboutUs.getContent(), ZHConverter.TRADITIONAL));
                    }
                    model.addAttribute("activity", aboutUs);
                }
                title = "关于我们 | 关于SLH全球奢华精品酒店品牌信息";
                keywords = "关于SLH,关于全球精品奢华酒店,SLH新闻动态";
                description = "无论你是否有兴趣了解更多关于全球小型奢华酒店的信息、是否希望成为品牌的一部分，或是了解我们的合作伙伴以及我们对于旅游业的使命，所有的信息都可以在这里找到。";
                model.addAttribute("pageTitle", "关于我们");
                model.addAttribute("crumbNav", "关于我们");
                model.addAttribute("currentPage", "brand-story");
                view = "about-us";
                break;
            case "contact-us":
                Activity contcatUs = activityService.getActivitiesByType(this.chainCode, type).get(0);
                contcatUs.setBanner(
                        configService.getConfig(chainCode, "imagePath").getValue() + "/" + contcatUs.getBanner());
                title = "联系我们 | SLH全球奢华精品酒店联系我们";
                keywords = "关于SLH,联系全球精品奢华酒店,SLH联系方式,全球奢华精品联系方式";
                description = "如果你想进行电话预订或是在预订前后需要任何帮助，我们将为你提供7*24的服务。中国大陆地区用户请拨打4 001 203276，香港地区用户请拨打800 966 234，均为免费热线。";
                String pageTitleCU = "联系我们";
                String crumbNavCU = "联系我们";
                if (StringUtils.isNotBlank(locale) && locale.equals("zh_TW")) {
                    pageTitleCU = ZHConverter.convert(pageTitleCU, ZHConverter.TRADITIONAL);
                    crumbNavCU = ZHConverter.convert(crumbNavCU, ZHConverter.TRADITIONAL);
                    contcatUs.setContent(ZHConverter.convert(contcatUs.getContent(), ZHConverter.TRADITIONAL));
                }
                model.addAttribute("pageTitle", pageTitleCU);
                model.addAttribute("crumbNav", crumbNavCU);
                model.addAttribute("activity", contcatUs);
                model.addAttribute("currentPage", "brand-story");
                view = "contact-us";
                break;
            case "contact-us-global":
                Activity global = activityService.getActivitiesByType(this.chainCode, type).get(0);
                title = "联系我们 | SLH全球奢华精品酒店联系我们";
                keywords = "关于SLH,联系全球精品奢华酒店,SLH联系方式,全球奢华精品联系方式";
                description = "如果你想进行电话预订或是在预订前后需要任何帮助，我们将为你提供7*24的服务。中国大陆地区用户请拨打4 001 203276，香港地区用户请拨打800 966 234，均为免费热线。";
                model.addAttribute("activity", global);
                //model.addAttribute("currentPage", "brand-story");
                view = "contact-us-global";
                break;
            case "news":
                List<Activity> activities = activityService.getActivitiesByType(this.chainCode, type);
                // 数据预处理，把图片路径替换为绝对路径
                for (Activity activity : activities) {
                    activity.setBanner(
                            configService.getConfig(this.chainCode, "imagePath").getValue() + "/" + activity.getBanner());
                }
                title = "新闻动态 | SLH全球奢华精品酒店最新动态";
                keywords = "关于SLH,关于全球精品奢华酒店,SLH新闻动态";
                description = "欢迎来到全球奢华精品酒店的“世界新闻报”页面。在这里，你会发现我们的新闻稿，关于SLH的营销活动及企业消息。";
                String pageTitle = "新闻动态";
                String crumbNav = "新闻动态";
                if (StringUtils.isNotBlank(locale) && locale.equals("zh_TW")) {
                    pageTitle = ZHConverter.convert(pageTitle, ZHConverter.TRADITIONAL);
                    crumbNav = ZHConverter.convert(crumbNav, ZHConverter.TRADITIONAL);
                    activities.parallelStream().forEachOrdered(activity -> {
                        activity.setTitle(ZHConverter.convert(activity.getTitle(), ZHConverter.TRADITIONAL));
                        activity.setPageDescription(ZHConverter.convert(activity.getPageDescription(), ZHConverter.TRADITIONAL));
                    });
                }
                model.addAttribute("pageTitle", pageTitle);
                model.addAttribute("crumbNav", crumbNav);
                model.addAttribute("currentPage", "brand-story");
                model.addAttribute("activities", activities);
                view = "news";
                break;
            case "weChat":
                List<Activity> weChats = activityService.getActivitiesByType(this.chainCode, type);
                // 数据预处理，把图片路径替换为绝对路径
                for (Activity activity : weChats) {
                    activity.setBanner(
                            configService.getConfig(this.chainCode, "imagePath").getValue() + "/" + activity.getBanner());
                }
                title = "微信精选 | SLH全球奢华精品酒店最新动态";
                keywords = "关于SLH,关于全球精品奢华酒店,SLH新闻动态,SLH微信精选,全球奢华精品酒店微信";
                description = "最新最全的SLH全球奢华精品酒店微信内容就在这里。你会发现我们的新闻稿，关于SLH的营销活动及最新消息。";
                String pageTitleNews = "微信精选";
                String crumbNavNews = "微信精选";
                if (StringUtils.isNotBlank(locale) && locale.equals("zh_TW")) {
                    pageTitleNews = ZHConverter.convert(pageTitleNews, ZHConverter.TRADITIONAL);
                    crumbNavNews = ZHConverter.convert(crumbNavNews, ZHConverter.TRADITIONAL);
                    weChats.parallelStream().forEachOrdered(activity -> {
                        activity.setTitle(ZHConverter.convert(activity.getTitle(), ZHConverter.TRADITIONAL));
                        activity.setPageDescription(ZHConverter.convert(activity.getPageDescription(), ZHConverter.TRADITIONAL));
                    });
                }
                model.addAttribute("pageTitle", pageTitleNews);
                model.addAttribute("crumbNav", crumbNavNews);
                model.addAttribute("currentPage", "brand-story");
                model.addAttribute("activities", weChats);
                view = "weChat";
                break;
            case "media-relations":
                title = "媒体对接 | SLH全球奢华精品酒店媒体入住申请";
                keywords = "关于SLH,关于全球精品奢华酒店,媒体入住申请,SLH媒体对接,全球奢华精品媒体联系";
                description = "如果您想申请参观其中某个全球奢华精品酒店，请点击下面链接填写“媒体入住申请”表格。酒店人员将及时给您回复，告诉您他们是否有能力以及如何招待您。请为申请的每一家酒店提交各自的表格。";
                model.addAttribute("currentPage", "brand-story");
                view = "media-relations";
                break;
            case "story":
                type = "owner-story";
                List<Activity> stories = activityService.getActivitiesByType(this.chainCode, type);
                // 数据预处理，把图片路径替换为绝对路径
                for (Activity activity : stories) {
                    activity.setBanner(
                            configService.getConfig(this.chainCode, "imagePath").getValue() + "/" + activity.getBanner());
                }
                title = "业主故事 | SLH全球奢华精品酒店最新动态";
                keywords = "关于SLH,关于全球精品奢华酒店,SLH业主故事";
                description = "欢迎来到全球奢华精品酒店的“业主故事”页面。在这里，你会发现我们的业主故事。";
                String pageTitleStory = "业主故事";
                String crumbNavStory = "业主故事";
                if (StringUtils.isNotBlank(locale) && locale.equals("zh_TW")) {
                    pageTitleStory = ZHConverter.convert(pageTitleStory, ZHConverter.TRADITIONAL);
                    crumbNavStory = ZHConverter.convert(crumbNavStory, ZHConverter.TRADITIONAL);
                    stories.parallelStream().forEachOrdered(activity -> {
                        activity.setTitle(ZHConverter.convert(activity.getTitle(), ZHConverter.TRADITIONAL));
                        activity.setPageDescription(ZHConverter.convert(activity.getPageDescription(), ZHConverter.TRADITIONAL));
                    });
                }
                model.addAttribute("pageTitle", pageTitleStory);
                model.addAttribute("crumbNav", crumbNavStory);
                model.addAttribute("currentPage", "brand-story");
                model.addAttribute("activities", stories);
                model.addAttribute("currentPage", "brand-story");
                view = "story";
                break;
            default:
                this.setErrorSession(session, chainCode, "404", "品牌故事类型不存在");
                view = "404";
        }
        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            keywords = ZHConverter.convert(keywords, ZHConverter.TRADITIONAL);
            description = ZHConverter.convert(description, ZHConverter.TRADITIONAL);
            continents.parallelStream().forEach(continent -> {
                continent.setName(ZHConverter.convert(continent.getName(), ZHConverter.TRADITIONAL));
                continent.getCountries().parallelStream().forEach(country ->
                        country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL))
                );
            });
        } else {
            return "redirect:/error";
        }
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }
        model.addAttribute("locale", localeAttribute);
        model.addAttribute("continents", continents);
        model.addAttribute("title", title);
        model.addAttribute("keywords", keywords);
        model.addAttribute("description", description);
        HttpLog.addAccessLog("slh", "trail-api-access", "info", "品牌故事页访问", request);
        return view;
    }

    @RequestMapping(value = {"/travel-agents"})
    public String toTravelAgents(HttpSession session, HttpServletRequest request, @RequestParam(value = "locale", required = false) String locale,
                                 Model model) {
        locale = localeConfig;
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
        for (Continent continent : continents) {
            model.addAttribute("continent_" + continent.getNameEn().replace(" ", ""), continent);
        }

        model.addAttribute("type", "travel-agents");
        String view;
        String title = "";
        String keywords = "";
        String description = "";
        String crumbNav = "";
        Activity travelAgents = null;
        try {
            travelAgents = activityService.getActivitiesByType(this.chainCode, "travel-agents").get(0);
            travelAgents.setBanner(configService.getConfig(chainCode, "imagePath").getValue() + "/" + travelAgents.getBanner());
            title = "旅行社|SLH全球奢华精品酒店";
            keywords = "旅行社联系方式, 同业联系, SLH同业联系方式";
            description = "您可以通过SLH专属的同业网站进行预订，也欢迎随时联系SLH的销售人员。";
            model.addAttribute("pageTitle", "旅行社");
            crumbNav = "旅行社";
            model.addAttribute("currentPage", "index");
            view = "travel-agents";

        } catch (Exception e) {
            this.setErrorSession(session, chainCode, "404", "旅行社数据不存在");
            view = "404";
        }
        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            keywords = ZHConverter.convert(keywords, ZHConverter.TRADITIONAL);
            description = ZHConverter.convert(description, ZHConverter.TRADITIONAL);
            continents.parallelStream().forEach(continent -> {
                continent.setName(ZHConverter.convert(continent.getName(), ZHConverter.TRADITIONAL));
                continent.getCountries().parallelStream().forEach(country ->
                        country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL))
                );
            });
            crumbNav = ZHConverter.convert(crumbNav, ZHConverter.TRADITIONAL);
            if (travelAgents != null) {
                travelAgents.setTitle(ZHConverter.convert(travelAgents.getTitle(), ZHConverter.TRADITIONAL));
                travelAgents.setContent(ZHConverter.convert(travelAgents.getContent(), ZHConverter.TRADITIONAL));
            }
        } else {
            return "redirect:/error";
        }
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }
        model.addAttribute("crumbNav", crumbNav);
        model.addAttribute("locale", localeAttribute);
        model.addAttribute("continents", continents);
        model.addAttribute("title", title);
        model.addAttribute("keywords", keywords);
        model.addAttribute("description", description);
        model.addAttribute("activity", travelAgents);
        HttpLog.addAccessLog("slh", "trail-api-access", "info", "品牌故事页访问", request);
        return view;
    }

    /**
     * 转到新闻详情页
     *
     * @param activityLink
     * @param model
     * @return
     */
    @RequestMapping(value = {"/brand-story/detail/{activityType}/{activityLink}"})
    public String toNewsDetail(HttpSession session, HttpServletRequest request, @PathVariable(value = "activityType") String activityType,
                               @PathVariable(value = "activityLink", required = false) String activityLink,
                               @RequestParam(value = "locale", required = false) String locale, Model model) {
        locale = localeConfig;
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);

        model.addAttribute("countryCode", "");
        model.addAttribute("cityLink", "");
        model.addAttribute("type", activityType);

        Activity activity = activityService.getActivityByLink(this.chainCode, activityLink);

        String name;
        String title;
        String keywords;
        String description;
        String listUrl;
        switch (activityType) {
            case "news":
                name = "新闻动态";
                title = "新闻动态 | SLH全球奢华精品酒店最新动态";
                keywords = "关于SLH,关于全球精品奢华酒店,SLH新闻动态";
                description = "欢迎来到全球奢华精品酒店的“世界新闻报”页面。在这里，你会发现我们的新闻稿，关于SLH的营销活动及企业消息。";
                listUrl = "/brand-story/news";
                break;
            case "story":
                name = "业主故事";
                title = "业主故事 | SLH全球奢华精品酒店最新故事";
                keywords = "关于SLH,关于全球精品奢华酒店,SLH业主故事";
                description = "欢迎来到全球奢华精品酒店的“业主故事”页面。在这里，你会发现我们的最新故事，关于SLH的业主故事。";
                listUrl = "/brand-story/story";
                break;
            default:
                return "redirect:/404";

        }

        if (activity != null) {
            activity.setBanner(configService.getConfig(chainCode, "imagePath").getValue() + "/" + activity.getBanner());
            model.addAttribute("activityLink", activity.getActivityLink());
        } else {
            model.addAttribute("activityLink", activityLink);
            this.setErrorSession(session, chainCode, "404", "新闻数据不存在");
            return "redirect:/error";
        }
        model.addAttribute("currentPage", "brand-story");
        String crumbNar;
        if (activity.getTitle().length() < 20) {
            crumbNar = activity.getTitle();
        } else {
            crumbNar = activity.getTitle().substring(0, 20) + "...";
        }
        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            keywords = ZHConverter.convert(keywords, ZHConverter.TRADITIONAL);
            description = ZHConverter.convert(description, ZHConverter.TRADITIONAL);
            continents.parallelStream().forEach(continent -> {
                continent.setName(ZHConverter.convert(continent.getName(), ZHConverter.TRADITIONAL));
                continent.getCountries().parallelStream().forEach(country ->
                        country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL))
                );
            });
            crumbNar = ZHConverter.convert(crumbNar, ZHConverter.TRADITIONAL);
            if (activity != null) {
                activity.setTitle(ZHConverter.convert(activity.getTitle(), ZHConverter.TRADITIONAL));
                activity.setContent(ZHConverter.convert(activity.getContent(), ZHConverter.TRADITIONAL));
            }
        } else {
            return "redirect:/error";
        }
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }
        model.addAttribute("locale", localeAttribute);
        model.addAttribute("continents", continents);
        model.addAttribute("name", name);
        model.addAttribute("title", title);
        model.addAttribute("keywords", keywords);
        model.addAttribute("description", description);
        model.addAttribute("listUrl", listUrl);
        model.addAttribute("crumbNav", crumbNar);
        model.addAttribute("activity", activity);
        HttpLog.addAccessLog("slh", "trail-api-access", "info", "新闻详情页访问", request);
        return "new-detail";
    }

    @RequestMapping(value = {"/club"})
    public String toClub(HttpSession session, HttpServletRequest request, @RequestParam(value = "locale", required = false) String locale, Model model) {
        locale = localeConfig;
        String title = "特邀会员|全球奢华精品酒店";
        String keywords = "全球奢华竞品酒店特邀会员计划，特邀会员，特启会员，特享会员";
        String description = "特邀会员计划让您在第一次入住全球奢华精品酒店时即可享受到会员尊享福利，更棒的是，您在酒店入住的时间越长，享受的福利越多,从会员专享礼遇到免费房晚，各种惊喜等您体验。";
        model.addAttribute("currentPage", "club");

        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            keywords = ZHConverter.convert(keywords, ZHConverter.TRADITIONAL);
            description = ZHConverter.convert(description, ZHConverter.TRADITIONAL);
            continents.parallelStream().forEach(continent -> {
                continent.setName(ZHConverter.convert(continent.getName(), ZHConverter.TRADITIONAL));
                continent.getCountries().parallelStream().forEach(country ->
                        country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL))
                );
            });
        } else {
            return "redirect:/error";
        }
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }
        model.addAttribute("locale", localeAttribute);
        model.addAttribute("continents", continents);
        model.addAttribute("title", title);
        model.addAttribute("keywords", keywords);
        model.addAttribute("description", description);
        HttpLog.addAccessLog("slh", "trail-api-access", "info", "会员页访问", request);
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            return "club";
        } else if (locale.equals("zh_TW")) {
            return "club_TW";
        }
        return "club";
    }

    @RequestMapping(value = {"/invited"})
    public String toInvited(HttpSession session, HttpServletRequest request, @RequestParam(value = "locale", required = false) String locale, Model model) {
        locale = localeConfig;
        String title = "特邀会员|全球奢华精品酒店";
        String keywords = "全球奢华竞品酒店特邀会员计划，特邀会员，特启会员，特享会员";
        String description = "特邀会员计划让您在第一次入住全球奢华精品酒店时即可享受到会员尊享福利，更棒的是，您在酒店入住的时间越长，享受的福利越多,从会员专享礼遇到免费房晚，各种惊喜等您体验。";
        model.addAttribute("currentPage", "club");

        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
        String localeAttribute = "zh_CN";
        //繁体
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            Locale localeDefault = new Locale("zh", "CN");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        } else if (locale.equals("zh_TW")) {
            Locale localeDefault = new Locale("zh", "TW");
            request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
            localeAttribute = "zh_TW";
            title = ZHConverter.convert(title, ZHConverter.TRADITIONAL);
            keywords = ZHConverter.convert(keywords, ZHConverter.TRADITIONAL);
            description = ZHConverter.convert(description, ZHConverter.TRADITIONAL);
            continents.parallelStream().forEach(continent -> {
                continent.setName(ZHConverter.convert(continent.getName(), ZHConverter.TRADITIONAL));
                continent.getCountries().parallelStream().forEach(country ->
                        country.setCountryName(ZHConverter.convert(country.getCountryName(), ZHConverter.TRADITIONAL))
                );
            });
        } else {
            return "redirect:/error";
        }
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }
        model.addAttribute("locale", localeAttribute);
        model.addAttribute("continents", continents);
        model.addAttribute("title", title);
        model.addAttribute("keywords", keywords);
        model.addAttribute("description", description);
        HttpLog.addAccessLog("slh", "trail-api-access", "info", "会员页访问", request);
        if (StringUtils.isBlank(locale) || locale.equals("zh_CN")) {
            return "club";
        } else if (locale.equals("zh_TW")) {
            return "club_TW";
        }
        return "club";
    }

    @RequestMapping(value = {"/club-registration"})
    public String toClubRegistration(HttpSession session, HttpServletRequest request, Model model) {
        model.addAttribute("title", "会员注册 | 欢迎加入SLH全球奢华精品酒店会员俱乐部");
        model.addAttribute("keywords", "全球奢华精品酒店会员俱乐部,S特别会员,L宠爱会员,H尊荣会员");
        model.addAttribute("description", "立即免费注册全球奢华精品酒店会员俱乐部吧！首次入住即可享受优惠。加入时间越长，享受优惠更多。");
        model.addAttribute("currentPage", "club");

        HttpLog.addAccessLog("slh", "trail-api-access", "info", "会员注册页访问", request);
        return "club-registration";
    }

    private void setErrorSession(HttpSession session, String chainCode, String errorCode, String errorMsg) {
        session.setAttribute("chainCode", chainCode);
        session.setAttribute("errorCode", errorCode);
        session.setAttribute("errorMsg", errorMsg);
    }

    @RequestMapping(value = {"/error"})
    public String error(HttpSession session, HttpServletRequest request, Model model) {
        Locale localeDefault = new Locale("zh", localeConfig.split("_")[1]);
        request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        //String localeAttribute = "zh_CN";
        model.addAttribute("locale", localeConfig);
        String chainCode = this.chainCode;
        if (session.getAttribute("chainCode") != null) {
            chainCode = session.getAttribute("chainCode").toString();
        } else {
            session.setAttribute("chainCode", chainCode);
        }
        String errorCode = "404";
        if (session.getAttribute("errorCode") != null) {
            errorCode = session.getAttribute("errorCode").toString();
        } else {
            session.setAttribute("errorCode", errorCode);
        }
        String errorMsg = "";
        if (session.getAttribute("errorMsg") != null) {
            errorMsg = session.getAttribute("errorMsg").toString();
        } else {
            session.setAttribute("errorMsg", errorMsg);
        }

        model.addAttribute("title", String.format("全球奢华精品酒店 | 全球%d多个国家%d多家独立酒店独一无二的入住体验", FrontCountryCount, FrontHotelCount));
        model.addAttribute("keywords", "SLH,全球奢华精品酒店,small luxury hotels,全球小型豪华酒店,世界小型豪华酒店,世界小型奢华酒店,全球小型奢华酒店");
        model.addAttribute("description",
                String.format("欢迎您下榻全球奢华精品酒店！我们位于全球各地的最佳奢华精品酒店可为您提供独一无二的入住体验。目前，我们在全球的%d多个国家共有%d多家独立奢华精品酒店，在这些精品酒店里，客人们可以享受到至臻完美的服务!注册会员，可享更多优惠福利。免费服务电话：************", FrontCountryCount, FrontHotelCount));
        this.renderContinents(chainCode, "", model);

        HttpLog.addAccessLog("slh", "trail-api-access", "error", "错误页", request);
        return errorCode;
    }

    @RequestMapping(value = {"/travel-advisory-re-novel-coronavirus"})
    public String toCoronaVirus(HttpSession session, HttpServletRequest request,
                                @RequestParam(value = "locale", required = false) String locale, Model model) {
        locale = localeConfig;
        Locale localeDefault;
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }
        model.addAttribute("continents", continents);
        Activity activity = activityService.getActivitiesByType(this.chainCode, "coronavirus").get(0);
        if (!StringUtils.isBlank(locale) && locale.equals("zh_TW")) {
            localeDefault = new Locale("zh", "TW");
        } else {
            localeDefault = new Locale("zh", "CN");
        }
        request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        model.addAttribute("activity", activity);
        model.addAttribute("locale", localeConfig);
        model.addAttribute("title", activity.getPageTitle());
        model.addAttribute("keywords", activity.getKeyWords());
        model.addAttribute("description", activity.getDescription());
        return "coronavirus";
    }

    @RequestMapping(value = {"/slhforheroes"})
    public String forHeros(HttpSession session, HttpServletRequest request,
                           @RequestParam(value = "locale", required = false) String locale, @RequestParam(value = "show", required = false) Integer show, Model model) {
        locale = localeConfig;
        Locale localeDefault;
        Activity activity = null;
        List<Activity> activities = activityService.getActivitiesByType(this.chainCode, "slhforheroes", show);
        if (activities != null && activities.size() > 0) {
            activity = activities.get(0);
            model.addAttribute("activity", activity);
            model.addAttribute("title", activity.getPageTitle());
            model.addAttribute("keywords", activity.getKeyWords());
            model.addAttribute("description", activity.getDescription());
        }
        if (!StringUtils.isBlank(locale) && locale.equals("zh_TW")) {
            localeDefault = new Locale("zh", "TW");
        } else {
            localeDefault = new Locale("zh", "CN");
        }
        model.addAttribute("locale", localeConfig);
        request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }
        model.addAttribute("continents", continents);

        return "slhforheroes";
    }

    @RequestMapping(value = {"/slh-for-heroes-terms-and-conditions"})
    public String forHerosTerms(HttpSession session, HttpServletRequest request,
                                @RequestParam(value = "locale", required = false) String locale, Model model) {
        locale = localeConfig;
        Locale localeDefault;
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }
        model.addAttribute("continents", continents);
        Activity activity = activityService.getActivitiesByType(this.chainCode, "slh-for-heroes-terms-and-conditions").get(0);
        if (!StringUtils.isBlank(locale) && locale.equals("zh_TW")) {
            localeDefault = new Locale("zh", "TW");
        } else {
            localeDefault = new Locale("zh", "CN");
        }
        request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        model.addAttribute("activity", activity);
        model.addAttribute("locale", localeConfig);
        model.addAttribute("title", activity.getPageTitle());
        model.addAttribute("keywords", activity.getKeyWords());
        model.addAttribute("description", activity.getDescription());
        return "slh-for-heroes-terms-and-conditions";
    }

    @RequestMapping(value = {"/staysmallstaysafe"})
    public String smallStaySmallSafe(HttpSession session, HttpServletRequest request,
                                     @RequestParam(value = "locale", required = false) String locale, Model model) {
        locale = localeConfig;
        Locale localeDefault;
        List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
        for (Continent continent : continents) {
            continent.setNameEn(continent.getNameEn().replace(" ", ""));
            model.addAttribute("continent_" + continent.getNameEn(), continent);
        }
        model.addAttribute("continents", continents);
        Activity activity = activityService.getActivitiesByType(this.chainCode, "staysmallstaysafe").get(0);
        if (!StringUtils.isBlank(locale) && locale.equals("zh_TW")) {
            localeDefault = new Locale("zh", "TW");
        } else {
            localeDefault = new Locale("zh", "CN");
        }
        request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
        model.addAttribute("activity", activity);
        model.addAttribute("locale", localeConfig);
        model.addAttribute("title", activity.getPageTitle());
        model.addAttribute("keywords", activity.getKeyWords());
        model.addAttribute("description", activity.getDescription());
        return "staysmallstaysafe";
    }

    /**
     @RequestMapping(value = {"/seek-simplicity", "/seek-simplicity/{type}"})
     public String SeekSimplicity(HttpSession session, HttpServletRequest request,
     @RequestParam(value = "locale", required = false) String locale,
     @PathVariable(value = "type", required = false) String type, Model model) {
     locale = localeConfig;
     Locale localeDefault;
     if (type == null || "".equals(type)) {
     type = "";
     Activity activity = null;
     List<Activity> activities = activityService.getActivitiesByType(this.chainCode, "seek-simplicity");
     if (activities != null && activities.size() > 0) {
     activity = activities.get(0);
     model.addAttribute("activity", activity);
     model.addAttribute("title", activity.getPageTitle());
     model.addAttribute("keywords", activity.getKeyWords());
     model.addAttribute("description", activity.getDescription());
     }
     } else {
     type = "-" + type;
     }
     if (!StringUtils.isBlank(locale) && locale.equals("zh_TW")) {
     localeDefault = new Locale("zh", "TW");
     } else {
     localeDefault = new Locale("zh", "CN");
     }
     model.addAttribute("locale", localeConfig);
     request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
     List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
     for (Continent continent : continents) {
     continent.setNameEn(continent.getNameEn().replace(" ", ""));
     model.addAttribute("continent_" + continent.getNameEn(), continent);
     }
     model.addAttribute("continents", continents);

     return "seek-simplicity" + type;
     }

     @RequestMapping(value = {"/seek-simplicity-form-rules"})
     public String SeekSimplicity(HttpSession session, HttpServletRequest request,
     @RequestParam(value = "locale", required = false) String locale, Model model) {
     locale = localeConfig;
     Locale localeDefault;
     Activity activity = null;
     List<Activity> activities = activityService.getActivitiesByType(this.chainCode, "seek-simplicity-form-rules");
     if (activities != null && activities.size() > 0) {
     activity = activities.get(0);
     model.addAttribute("activity", activity);
     model.addAttribute("title", activity.getPageTitle());
     model.addAttribute("keywords", activity.getKeyWords());
     model.addAttribute("description", activity.getDescription());
     }
     if (!StringUtils.isBlank(locale) && locale.equals("zh_TW")) {
     localeDefault = new Locale("zh", "TW");
     } else {
     localeDefault = new Locale("zh", "CN");
     }
     model.addAttribute("locale", localeConfig);
     request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
     List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
     for (Continent continent : continents) {
     continent.setNameEn(continent.getNameEn().replace(" ", ""));
     model.addAttribute("continent_" + continent.getNameEn(), continent);
     }
     model.addAttribute("continents", continents);

     return "seek-simplicity-form-rules";
     }

     @RequestMapping(value = {"/seek-simplicity-form"})
     public String SeekSimplicityForm(HttpSession session, HttpServletRequest request,
     @RequestParam(value = "locale", required = false) String locale, Model model) {
     locale = localeConfig;
     Locale localeDefault;
     if (!StringUtils.isBlank(locale) && locale.equals("zh_TW")) {
     localeDefault = new Locale("zh", "TW");
     } else {
     localeDefault = new Locale("zh", "CN");
     }
     model.addAttribute("locale", localeConfig);
     request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
     List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
     for (Continent continent : continents) {
     continent.setNameEn(continent.getNameEn().replace(" ", ""));
     model.addAttribute("continent_" + continent.getNameEn(), continent);
     }
     model.addAttribute("continents", continents);

     return "seek-simplicity-form";
     }

     @RequestMapping(value = {"/seek-simplicity-form/save"})
     @ResponseBody public String SaveSeekSimplicityForm(HttpServletRequest request,
     @RequestParam(value = "locale", required = false) String locale,
     @ModelAttribute("pojo") SeekSimplicity ss, Model model) {
     ss.setCreateTime(new Date());
     locale = localeConfig;
     Locale localeDefault;
     if (!StringUtils.isBlank(locale) && locale.equals("zh_TW")) {
     localeDefault = new Locale("zh", "TW");
     } else {
     localeDefault = new Locale("zh", "CN");
     }
     model.addAttribute("locale", localeConfig);
     request.getSession().setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, localeDefault);
     List<Continent> continents = continentService.getContinentsAllInfo(this.chainCode);
     for (Continent continent : continents) {
     continent.setNameEn(continent.getNameEn().replace(" ", ""));
     model.addAttribute("continent_" + continent.getNameEn(), continent);
     }
     model.addAttribute("continents", continents);
     if (seekSimplicityService.add(ss)) {
     return "form-success";
     } else {
     return "form-fail";x
     }
     }

     @RequestMapping(value = "/seek-simplicity-form/upload", method = RequestMethod.POST)
     @ResponseBody public String upload(HttpSession session, @RequestParam(value = "img", required = false)  MultipartFile file,
     @RequestParam(value = "format", required = false, defaultValue = "400*300c.jpg") String format,
     HttpServletRequest request, HttpServletResponse response) {
     String imageName = "";
     Map<String,String[]> pramMap = request.getParameterMap();
     File newFile = null;
     try {
     String filename = file.getOriginalFilename();
     String path = configService.getConfig(chainCode, "imageFolder").getValue()
     + java.io.File.separator + "seek" + java.io.File.separator;
     File dirs = new File(path);
     if (!dirs.exists()) {
     dirs.mkdirs();
     }
     path = path + java.io.File.separator + System.currentTimeMillis() + filename;// 临时目录
     newFile = new File(path);
     if (!newFile.exists()) {
     newFile.createNewFile();
     }
     InputStream is = file.getInputStream();
     OutputStream os = new FileOutputStream(newFile);
     int bytesRead = 0;
     byte[] buffer = new byte[1024];
     while ((bytesRead = is.read(buffer, 0, 1024)) != -1) {
     os.write(buffer, 0, bytesRead);
     }
     os.close();
     is.close();

     HttpClient httpPostClient = new HttpClient();
     PostMethod postMethod = new PostMethod(imageUploadUrl);

     StringPart sp = new StringPart("format", format);
     FilePart fp = new FilePart("uploadfile", filename, newFile);

     MultipartRequestEntity mrp = new MultipartRequestEntity(new Part[]{sp, fp}, postMethod.getParams());
     postMethod.setRequestEntity(mrp);

     int status = httpPostClient.executeMethod(postMethod);
     if (status == HttpStatus.SC_OK) {
     BufferedInputStream bis = new BufferedInputStream(postMethod.getResponseBodyAsStream());
     byte[] bytes = new byte[1024];
     ByteArrayOutputStream bos = new ByteArrayOutputStream();
     int count = 0;
     while ((count = bis.read(bytes)) != -1) {
     bos.write(bytes, 0, count);
     }
     byte[] strByte = bos.toByteArray();
     String content = new String(strByte, 0, strByte.length, "utf-8");
     String[] images = content.split("\n");
     if (images.length >= 1) {
     imageName = images[0].trim();
     }
     bos.close();
     bis.close();
     }
     postMethod.releaseConnection();
     //newFile.delete();
     } catch (Exception e) {
     e.printStackTrace();
     }
     return newFile.getName();
     }
     **/
}
