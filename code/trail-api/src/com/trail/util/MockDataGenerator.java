package com.trail.util;

import com.trail.model.HotelServiceData;
import java.util.ArrayList;
import java.util.List;

/**
 * 模拟数据生成器
 * 用于测试和演示第三方API数据展示功能
 */
public class MockDataGenerator {
    
    /**
     * 生成模拟的酒店服务数据
     * @param hotelCode 酒店代码
     * @return 模拟的酒店服务数据
     */
    public static HotelServiceData generateMockHotelServiceData(String hotelCode) {
        HotelServiceData serviceData = new HotelServiceData();
        serviceData.setHotelCode(hotelCode);
        serviceData.setHotelName("测试酒店 - " + hotelCode);
        serviceData.setStatus("success");
        serviceData.setMessage("数据获取成功");
        
        List<HotelServiceData.ServiceItem> services = new ArrayList<>();
        
        // 餐饮服务
        HotelServiceData.ServiceItem dining = new HotelServiceData.ServiceItem();
        dining.setId("dining_001");
        dining.setName("精品餐厅");
        dining.setDescription("提供国际化美食，包括中式、西式和当地特色菜品。营业时间：6:00-22:00");
        dining.setCategory("餐饮服务");
        dining.setPrice("人均 ¥300-500");
        dining.setAvailable(true);
        services.add(dining);
        
        // 健身服务
        HotelServiceData.ServiceItem fitness = new HotelServiceData.ServiceItem();
        fitness.setId("fitness_001");
        fitness.setName("健身中心");
        fitness.setDescription("24小时开放的现代化健身中心，配备专业健身器材和私人教练服务");
        fitness.setCategory("健身娱乐");
        fitness.setPrice("免费使用");
        fitness.setAvailable(true);
        services.add(fitness);
        
        // SPA服务
        HotelServiceData.ServiceItem spa = new HotelServiceData.ServiceItem();
        spa.setId("spa_001");
        spa.setName("豪华SPA");
        spa.setDescription("专业的SPA理疗服务，提供按摩、美容、芳疗等多种项目");
        spa.setCategory("健康美容");
        spa.setPrice("¥500-1500/次");
        spa.setAvailable(true);
        services.add(spa);
        
        // 商务服务
        HotelServiceData.ServiceItem business = new HotelServiceData.ServiceItem();
        business.setId("business_001");
        business.setName("商务中心");
        business.setDescription("提供打印、复印、传真、会议室预订等商务服务");
        business.setCategory("商务服务");
        business.setPrice("按项目收费");
        business.setAvailable(true);
        services.add(business);
        
        // 接送服务
        HotelServiceData.ServiceItem transport = new HotelServiceData.ServiceItem();
        transport.setId("transport_001");
        transport.setName("机场接送");
        transport.setDescription("提供24小时机场接送服务，豪华轿车接送");
        transport.setCategory("交通服务");
        transport.setPrice("¥200-500");
        transport.setAvailable(true);
        services.add(transport);
        
        // 礼宾服务
        HotelServiceData.ServiceItem concierge = new HotelServiceData.ServiceItem();
        concierge.setId("concierge_001");
        concierge.setName("礼宾服务");
        concierge.setDescription("专业礼宾团队提供旅游咨询、票务预订、餐厅推荐等服务");
        concierge.setCategory("客户服务");
        concierge.setPrice("免费咨询");
        concierge.setAvailable(true);
        services.add(concierge);
        
        // 儿童服务（可能不可用）
        HotelServiceData.ServiceItem childcare = new HotelServiceData.ServiceItem();
        childcare.setId("childcare_001");
        childcare.setName("儿童托管");
        childcare.setDescription("专业儿童托管服务，适合3-12岁儿童");
        childcare.setCategory("家庭服务");
        childcare.setPrice("¥100/小时");
        childcare.setAvailable(false); // 设置为不可用作为示例
        services.add(childcare);
        
        serviceData.setServices(services);
        
        return serviceData;
    }
    
    /**
     * 生成空的酒店服务数据（用于测试无数据情况）
     * @param hotelCode 酒店代码
     * @return 空的酒店服务数据
     */
    public static HotelServiceData generateEmptyHotelServiceData(String hotelCode) {
        HotelServiceData serviceData = new HotelServiceData();
        serviceData.setHotelCode(hotelCode);
        serviceData.setHotelName("测试酒店 - " + hotelCode);
        serviceData.setStatus("success");
        serviceData.setMessage("该酒店暂无特殊服务信息");
        serviceData.setServices(new ArrayList<>()); // 空列表
        
        return serviceData;
    }
    
    /**
     * 生成错误状态的酒店服务数据（用于测试错误情况）
     * @param hotelCode 酒店代码
     * @return 错误状态的酒店服务数据
     */
    public static HotelServiceData generateErrorHotelServiceData(String hotelCode) {
        HotelServiceData serviceData = new HotelServiceData();
        serviceData.setHotelCode(hotelCode);
        serviceData.setStatus("error");
        serviceData.setMessage("获取酒店服务信息失败，请稍后重试");
        serviceData.setServices(null);
        
        return serviceData;
    }
}
