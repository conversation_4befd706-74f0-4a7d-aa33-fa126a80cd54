package com.trail.util;

import com.trail.model.HotelServiceData;
import java.util.ArrayList;
import java.util.List;

/**
 * 模拟数据生成器
 * 用于测试和演示第三方API数据展示功能
 */
public class MockDataGenerator {
    
    /**
     * 生成模拟的酒店服务数据
     * @param hotelCode 酒店代码
     * @return 模拟的酒店服务数据
     */
    public static HotelServiceData generateMockHotelServiceData(String hotelCode) {
        HotelServiceData serviceData = new HotelServiceData();
        serviceData.setHotelCode(hotelCode);
        serviceData.setHotelName("测试酒店 - " + hotelCode);
        serviceData.setStatus("success");
        serviceData.setMessage("数据获取成功");
        
        List<HotelServiceData.ServiceItem> services = new ArrayList<>();

        // 礼宾部/行李员服务 - 匹配实际API数据格式
        HotelServiceData.ServiceItem bellStaff = new HotelServiceData.ServiceItem();
        bellStaff.setId(64);
        bellStaff.setServiceCode(106);
        bellStaff.setServiceName("礼宾部/行李员服务");
        bellStaff.setServiceNameEn("Bell staff/porter");
        bellStaff.setServiceType("HAC");
        services.add(bellStaff);

        // 24小时前台服务
        HotelServiceData.ServiceItem frontDesk = new HotelServiceData.ServiceItem();
        frontDesk.setId(65);
        frontDesk.setServiceCode(107);
        frontDesk.setServiceName("24小时前台服务");
        frontDesk.setServiceNameEn("24-hour front desk");
        frontDesk.setServiceType("HAC");
        services.add(frontDesk);

        // 客房服务
        HotelServiceData.ServiceItem roomService = new HotelServiceData.ServiceItem();
        roomService.setId(66);
        roomService.setServiceCode(108);
        roomService.setServiceName("客房服务");
        roomService.setServiceNameEn("Room service");
        roomService.setServiceType("HAC");
        services.add(roomService);

        // 洗衣服务
        HotelServiceData.ServiceItem laundry = new HotelServiceData.ServiceItem();
        laundry.setId(67);
        laundry.setServiceCode(109);
        laundry.setServiceName("洗衣服务");
        laundry.setServiceNameEn("Laundry service");
        laundry.setServiceType("HAC");
        services.add(laundry);

        // 商务中心
        HotelServiceData.ServiceItem businessCenter = new HotelServiceData.ServiceItem();
        businessCenter.setId(68);
        businessCenter.setServiceCode(110);
        businessCenter.setServiceName("商务中心");
        businessCenter.setServiceNameEn("Business center");
        businessCenter.setServiceType("BUS");
        services.add(businessCenter);

        // 健身中心
        HotelServiceData.ServiceItem fitness = new HotelServiceData.ServiceItem();
        fitness.setId(69);
        fitness.setServiceCode(111);
        fitness.setServiceName("健身中心");
        fitness.setServiceNameEn("Fitness center");
        fitness.setServiceType("REC");
        services.add(fitness);

        // SPA服务
        HotelServiceData.ServiceItem spa = new HotelServiceData.ServiceItem();
        spa.setId(70);
        spa.setServiceCode(112);
        spa.setServiceName("SPA服务");
        spa.setServiceNameEn("SPA service");
        spa.setServiceType("REC");
        services.add(spa);
        
        serviceData.setServices(services);
        
        return serviceData;
    }
    
    /**
     * 生成空的酒店服务数据（用于测试无数据情况）
     * @param hotelCode 酒店代码
     * @return 空的酒店服务数据
     */
    public static HotelServiceData generateEmptyHotelServiceData(String hotelCode) {
        HotelServiceData serviceData = new HotelServiceData();
        serviceData.setHotelCode(hotelCode);
        serviceData.setHotelName("测试酒店 - " + hotelCode);
        serviceData.setStatus("success");
        serviceData.setMessage("该酒店暂无特殊服务信息");
        serviceData.setServices(new ArrayList<>()); // 空列表
        
        return serviceData;
    }
    
    /**
     * 生成错误状态的酒店服务数据（用于测试错误情况）
     * @param hotelCode 酒店代码
     * @return 错误状态的酒店服务数据
     */
    public static HotelServiceData generateErrorHotelServiceData(String hotelCode) {
        HotelServiceData serviceData = new HotelServiceData();
        serviceData.setHotelCode(hotelCode);
        serviceData.setStatus("error");
        serviceData.setMessage("获取酒店服务信息失败，请稍后重试");
        serviceData.setServices(null);
        
        return serviceData;
    }
}
