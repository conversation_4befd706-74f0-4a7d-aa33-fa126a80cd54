package com.trail.util;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * API配置验证工具类
 * 用于验证第三方API配置是否正确
 */
@Component
public class ApiConfigValidator {
    
    private static final Logger log = Logger.getLogger(ApiConfigValidator.class);
    
    @Value("${api.base_url}")
    private String apiBaseUrl;
    
    /**
     * 应用启动时验证配置
     */
    @PostConstruct
    public void validateConfiguration() {
        log.info("Starting API configuration validation...");
        
        // 验证API基础URL配置
        if (StringUtils.isEmpty(apiBaseUrl)) {
            log.error("API base URL is not configured! Please check config.properties file.");
            return;
        }
        
        log.info("API base URL configured: " + apiBaseUrl);
        
        // 验证URL格式
        if (!isValidUrl(apiBaseUrl)) {
            log.error("Invalid API base URL format: " + apiBaseUrl);
            return;
        }
        
        // 测试API连通性（可选）
        testApiConnectivity();
        
        log.info("API configuration validation completed.");
    }
    
    /**
     * 验证URL格式是否正确
     */
    private boolean isValidUrl(String url) {
        try {
            new URL(url);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 测试API连通性
     */
    private void testApiConnectivity() {
        try {
            // 构建一个简单的测试URL
            String testUrl = apiBaseUrl;
            if (!testUrl.endsWith("/")) {
                testUrl += "/";
            }
            
            URL url = new URL(testUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 5秒超时
            connection.setReadTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            
            if (responseCode >= 200 && responseCode < 300) {
                log.info("API connectivity test passed. Response code: " + responseCode);
            } else if (responseCode == 404) {
                log.warn("API base URL returns 404, but server is reachable. Response code: " + responseCode);
            } else {
                log.warn("API connectivity test returned unexpected response code: " + responseCode);
            }
            
        } catch (Exception e) {
            log.warn("API connectivity test failed: " + e.getMessage() + 
                    ". This may be normal if the API server is not running or not accessible from this environment.");
        }
    }
    
    /**
     * 获取API基础URL
     */
    public String getApiBaseUrl() {
        return apiBaseUrl;
    }
    
    /**
     * 验证特定的API端点
     */
    public boolean validateEndpoint(String endpoint) {
        if (StringUtils.isEmpty(endpoint)) {
            log.warn("Empty endpoint provided for validation");
            return false;
        }
        
        try {
            String fullUrl = apiBaseUrl + endpoint;
            URL url = new URL(fullUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD"); // 使用HEAD请求减少网络开销
            connection.setConnectTimeout(3000);
            connection.setReadTimeout(3000);
            
            int responseCode = connection.getResponseCode();
            boolean isValid = responseCode >= 200 && responseCode < 500; // 2xx, 3xx, 4xx都认为是有效的
            
            log.debug("Endpoint validation for '" + endpoint + "': " + 
                     (isValid ? "VALID" : "INVALID") + " (Response code: " + responseCode + ")");
            
            return isValid;
            
        } catch (Exception e) {
            log.debug("Endpoint validation failed for '" + endpoint + "': " + e.getMessage());
            return false;
        }
    }
}
