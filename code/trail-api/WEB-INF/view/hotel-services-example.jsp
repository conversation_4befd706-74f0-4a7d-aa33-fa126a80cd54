<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>酒店服务信息示例</title>
    <style>
        .hotel-services {
            margin: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .service-item {
            margin: 10px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 3px solid #007cba;
        }
        .error-message {
            color: #d32f2f;
            font-weight: bold;
        }
        .success-message {
            color: #388e3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="hotel-services">
        <h2>酒店服务信息</h2>
        
        <!-- 检查是否有第三方API数据 -->
        <c:choose>
            <c:when test="${not empty hotelServicesData}">
                <div class="success-message">
                    ✓ 成功获取第三方接口数据
                </div>
                
                <!-- 显示原始JSON数据（用于调试） -->
                <div style="margin: 20px 0;">
                    <h3>原始API响应数据：</h3>
                    <pre style="background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto;">
${hotelServicesData}
                    </pre>
                </div>
                
                <!-- 这里可以根据实际的JSON结构来解析和显示数据 -->
                <div style="margin: 20px 0;">
                    <h3>解析后的服务信息：</h3>
                    <div class="service-item">
                        <strong>酒店代码：</strong> ${hotel.hotelCode}
                    </div>
                    
                    <!-- 示例：如果API返回的JSON包含services数组 -->
                    <script>
                        // JavaScript解析JSON数据的示例
                        try {
                            var hotelData = ${hotelServicesData};
                            console.log('Hotel Services Data:', hotelData);
                            
                            // 根据实际的JSON结构来处理数据
                            if (hotelData.services) {
                                var servicesHtml = '';
                                hotelData.services.forEach(function(service) {
                                    servicesHtml += '<div class="service-item">';
                                    servicesHtml += '<strong>' + (service.name || '未知服务') + '</strong><br>';
                                    servicesHtml += service.description || '无描述';
                                    servicesHtml += '</div>';
                                });
                                document.getElementById('services-container').innerHTML = servicesHtml;
                            }
                        } catch (e) {
                            console.error('Error parsing hotel services data:', e);
                        }
                    </script>
                    
                    <div id="services-container">
                        <!-- 动态生成的服务信息将显示在这里 -->
                    </div>
                </div>
                
            </c:when>
            <c:otherwise>
                <div class="error-message">
                    ✗ 未能获取第三方接口数据
                </div>
                <p>可能的原因：</p>
                <ul>
                    <li>第三方API服务不可用</li>
                    <li>网络连接问题</li>
                    <li>API配置错误</li>
                    <li>酒店代码无效</li>
                </ul>
            </c:otherwise>
        </c:choose>
        
        <!-- 显示酒店基本信息 -->
        <div style="margin-top: 30px;">
            <h3>酒店基本信息：</h3>
            <div class="service-item">
                <strong>酒店名称：</strong> ${hotel.hotelName}
            </div>
            <div class="service-item">
                <strong>酒店代码：</strong> ${hotel.hotelCode}
            </div>
            <div class="service-item">
                <strong>城市：</strong> ${hotel.cityName}
            </div>
            <div class="service-item">
                <strong>国家：</strong> ${hotel.countryName}
            </div>
        </div>
    </div>
</body>
</html>
