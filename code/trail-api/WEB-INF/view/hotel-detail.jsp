<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
  <!DOCTYPE html>
  <html lang="${locale}">
  <head>
    <%@ include file="/WEB-INF/view/partials/__top.jsp" %>
    <link rel="stylesheet" href="${f:mix('src/css/hotel-detail.scss')}">
    <%@ include file="/WEB-INF/view/partials/gmap.jsp" %>
  </head>
  <body class="slh_layout" id="slh_layout"  data-role="hotelDetail">
    <%@ include file="/WEB-INF/view/partials/aside.jsp" %>
    <div class="layout_main flex flex-col" id="layout_main">
      <%@ include file="/WEB-INF/view/partials/header/header_hotel_detail.jsp"%>
      <div class="layout_content page-hotel-detail">
        <div class="com-page-header">
          <div class="header__banner role__hotel">
            <c:choose>
              <c:when test="${hotel.banners != null && hotel.banners.size() > 0}">
                <img class="aspect-ele" src="https://slh-cf-image-cdn.slhhotels.cn/proxy?url=${hotel.banners.get(0).sourceUrl}" alt="">
              </c:when>
              <c:otherwise>
                <img class="aspect-ele" src="<%=path%>/static/assets/imgs/hotel_img_loading.jpg" alt="">
              </c:otherwise>
            </c:choose>
          </div>
          <div class="header__cover">
            <div
              class="slh-container flex flex-col h-full items-center md:items-start justify-center md:justify-end md:pb-6 text-white text-center md:text-left">
              <h1 class="cover__title slh-font-sunti">${hotel.hotelName}</h1>
              <h2 class="cover__subtitle">
                <c:choose>
                  <c:when test="${hotel.hotelAliasEn!='' }">${hotel.hotelAliasEn }</c:when>
                  <c:otherwise>${hotel.hotelNameEn }</c:otherwise>
                </c:choose>
              </h2>
              <p class="pt-3 md:pt-4 lg:pt-6">
                <span class="inline-flex items-start text-white">
                  <i class="iconfont icon-location text-sm md:text-base"></i>
                  <span class="pl-2 text-sm md:text-base">
                    ${country.countryName} ${city.cityName}
                  </span>
                </span>
              </p>
            </div>
          </div>
        </div>
        <div class="book__box" id="bookBox">
          <div class="open__btn" id="openBtn">
            <button class="w-full slh-btn-brand"><fmt:message key="view.hotel_detail.booking"></fmt:message></button>
          </div>
          <div class="box__main">
            <div id="slhSearchbar" role="hotel" hotelCode="${hotel.hotelCode}" hotelName="${hotel.hotelName}"></div>
          </div>
        </div>
        <div class="w-full bg-white shadow 2xl:shadow-none sticky top-12.5 md:top-0 z-10">
          <div class="slh-container relative">
            <div class="hotel__navs">
              <ul class="navs__inner" id="pageNav">
                <li class="nav__item">
                  <a href="#section1">
                    <fmt:message key="hotel_detail.hotelInformation"></fmt:message>
                  </a>
                </li>
                <li class="nav__item"><a href="#section3"><fmt:message key="view.hotel_detail.hotel_img"></fmt:message></a></li>
                <li class="nav__item"><a href="#section4"><fmt:message key="view.hotel_detail.room_introduction"></fmt:message></a></li>
                <li class="nav__item">
                  <a href="#section5">
                    <fmt:message key="hotel_detail.locationAndPerimeter"></fmt:message>
                  </a>
                </li>
                <c:if test="${isFeatures}">
                <li class="nav__item">
                  <a href="#section6">
                    <fmt:message key="hotel_detail.hotelFacilities"></fmt:message>
                  </a>
                </li>
                </c:if>
              </ul>
            </div>
          </div>
        </div>
        <div id="section1" class="detail__section py-8 md:py-12 lg:py-14 xl:py-16">
          <div class="slh-container 2xl:relative">
            <c:if test="${hotel.emergencyNotice!=null&&hotel.emergencyNotice!=''}">
              <div class="hotel_emergency_notice">
                ${hotel.emergencyNotice}
              </div>
            </c:if>
            <div class="hotel__info">
              <div class="info__inner">
                <div class="detail__content">
                  <p class="hotle_des">${hotel.brief}</p>
                  <!-- <div class="hotle_guelities">
                    ${hotel.uniqueQuelities}
                  </div> -->
                  <div
                    class="com-ellipsis slh-detail-content"
                    open-text='<fmt:message key="view.hotel_detail.open"></fmt:message>'
                    close-text='<fmt:message key="view.hotel_detail.close"></fmt:message>'>
                    <div class="ellipsis__content line-clamp-10">
                      ${hotel.detail}
                    </div>
                  </div>
                </div>
                <div class="detail__info">
                  <div class="hotel__msgs">
                    <c:if test="${hotel.totalNumberOfRooms>0}">
                      <div class="msg__item">
                        <i class="iconfont icon-rooms"></i>
                        <span>${hotel.totalNumberOfRooms}<fmt:message key="hotel_info.room"></fmt:message></span>
                      </div>
                    </c:if>
                    <c:if test="${hotel.numberOfRestaurants>0}">
                      <div class="msg__item">
                        <i class="iconfont icon-breakfast1"></i>
                        <span>${hotel.numberOfRestaurants}<fmt:message key="hotel_info.restaurant"></fmt:message></span>
                      </div>
                    </c:if>
                    <c:if test="${hotel.numberOfGuestRoomFloors>0}">
                      <div class="msg__item">
                        <i class="iconfont icon-floor"></i> 
                        <span>${hotel.numberOfGuestRoomFloors}<fmt:message key="hotel_info.floor"></fmt:message></span>
                      </div>
                    </c:if>
                  </div>
                  <div class="hotel__features">
                    <c:forEach var="iconFeature" items="${iconFeatures}">
                      <div class="feature__item">
                        <div class="feature__img">
                          <img src="${iconFeature.imageUrl}" alt="">
                        </div>
                        <p class="feature__name"><c:if test="${locale=='zh_TW'}">${iconFeature.name_tw}</c:if><c:if test="${locale!='zh_TW'}">${iconFeature.name}</c:if></p>
                      </div>
                       </c:forEach>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <c:if test="${not empty hotelExt.considerate}">
          <div id="section2" class="detail__section bg-primary-light_3">
            <div class="slh-container">
              <div class="section__hd flex-col md:flex-row">
                <h3 class="section__title slh-font-sunti"><fmt:message key="view.hotel_detail.considerate_title"></fmt:message></h3>
                <span class="section__en__title mt-1 md:mt-0"><fmt:message key="view.hotel_detail.considerate_title_en"></fmt:message></span>
              </div>
              <div class="actively-col-swiper swiper md:pt-0" perView="3">
                <div class="swiper-wrapper">
                  <c:forEach var="considerate" items="${hotelExt.considerate}">
                    <div class="swiper-slide actively__card">
                      <div class="card__img">
                        <img class="aspect-ele" src="${considerate.cover}" alt="">
                      </div>
                      <div class="card__info">
                        <h3 class="card__title slh-font-sunti">${considerate.title}</h3>
                        <div
                          class="card__desc com-ellipsis"
                          open-text='<fmt:message key="view.hotel_detail.more"></fmt:message>'
                          close-text='<fmt:message key="view.hotel_detail.close"></fmt:message>'>
                          <div class="ellipsis__content line-clamp-6">
                            ${considerate.desc}
                          </div>
                        </div>
                      </div>
                    </div>
                  </c:forEach>
                </div>
                <div class="swiper-scrollbar"></div>
              </div>
            </div>
          </div>
        </c:if>
        <div id="section3" class="detail__section py-8 md:py-12 lg:py-14 xl:py-16 hidden">
          <div class="slh-container">
            <div class="section__hd">
              <h3 class="section__title slh-font-sunti"><fmt:message key="view.hotel_detail.hotel_img"></fmt:message></h3>
            </div>
            <div class="hotel__gallery">
              <div class="swiper mainSwiper" id="hotel-gallery">
                <div class="swiper-wrapper" id="bestGalleryWrapper"></div>
                <div class="swiper-controllers">
                  <div class="swiper-button-prev"><i class="iconfont icon-arrow-left4"></i></div>
                  <div class="swiper-pagination"></div>
                  <div class="swiper-button-next"><i class="iconfont icon-arrow-right4"></i></div>
                </div>
              </div>
              <div thumbsSlider="" class="swiper thumbSwiper">
                <div class="swiper-wrapper" id="smallGalleryWrapper"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="slh-container">
          <c:if test="${not empty hotelExt.videoUrl}">
            <div class="w-full bg-block h-auto md:h-[500px] lg:h-[600px] xl:h-[670px]">
              <video
                      src="${hotelExt.videoUrl}" poster="${hotelExt.videoCover}"
                      controls
                      class="object-contain w-full h-full bg-block">
              </video>
            </div>
          </c:if>

          <div id="section4" class="detail__section hotel-col-swiper-box">
            <div class="section__hd">
              <h3 class="section__title slh-font-sunti"><fmt:message key="view.hotel_detail.room_introduction"></fmt:message></h3>
              <c:if test="${fn:length(hotelRooms)>0 }">
                <div class="hotel-col-swiper-btns">
                  <button class="swiper__btn swiper__btn__prev">
                    <i class="btn__icon iconfont icon-arrow-left"></i>
                  </button>
                  <button class="swiper__btn swiper__btn__next">
                    <i class="iconfont icon-arrow-right"></i>
                  </button>
                </div>
              </c:if>
            </div>
            <c:if test="${fn:length(hotelRooms)>0 }">
              <div class="hotel-col-swiper swiper md:pt-0" perView="2.5" loop="false" center="false">
                <div class="swiper-wrapper">
                  <c:forEach var="room" items="${hotelRooms }">
                    <div class="swiper-slide room__card">
                      <div class="relative cursor-pointer room-gallery">
                        <c:choose>
                          <c:when test="${not empty room.images and fn:length(room.images) > 0}">
                            <c:forEach var="item" items="${room.images}" varStatus="status">
                              <c:choose>
                                <c:when test="${status.index == 0}">
                                  <a class="room__img" href="https://slh-cf-image-cdn.slhhotels.cn/proxy?url=${item.sourceUrl}" data-pswp-width="${item.width}" data-pswp-height="${item.height}" target="_blank">
                                </c:when>
                                <c:otherwise>
                                  <a class="hidden" href="https://slh-cf-image-cdn.slhhotels.cn/proxy?url=${item.sourceUrl}" data-pswp-width="${item.width}" data-pswp-height="${item.height}" target="_blank">
                                </c:otherwise>
                              </c:choose>
                                <c:choose>
                                  <c:when test="${item.url!='' && item.url!=null}">
                                    <img class="aspect-ele slh-empty-img" src="https://slh-cf-image-cdn.slhhotels.cn/proxy?url=${item.url}" alt="" loading="lazy">
                                  </c:when>
                                  <c:otherwise>
                                    <img class="aspect-ele slh-empty-img" src="<%=path%>/static/assets/imgs/hotel_img_loading.jpg" alt="" loading="lazy">
                                  </c:otherwise>
                                </c:choose>
                              </a>
                            </c:forEach>
                            <i class="iconfont icon-qietu-tuji imgs__icon"></i>
                          </c:when>
                          <c:otherwise>
                            <a class="room__img">
                              <img class="aspect-ele slh-empty-img" src="<%=path%>/static/assets/imgs/hotel_img_loading.jpg" alt="">
                            </a>
                          </c:otherwise>
                        </c:choose>
                      </div>
                      <div class="room__info">
                        <c:choose>
                          <c:when test="${not empty room.roomNameEn}">
                            <h3 class="room__title slh-font-sunti">${room.roomNameEn}</h3>
                          </c:when>
                          <c:otherwise>
                            <h3 class="room__title slh-font-sunti">${room.roomName}</h3>
                          </c:otherwise>
                        </c:choose>
                        <div
                          class="com-ellipsis room__desc"
                          open-text='<fmt:message key="order.open"></fmt:message>'
                          close-text='<fmt:message key="order.close"></fmt:message>'>
                          <div class="ellipsis__content line-clamp-5">
                            <div>${room.bookerText}</div>
                          </div>
                        </div>
                        <!-- <div class="room__features__box">
                          <div class="room__features">
                            <span class="feature__item inline-flex items-center leading-none">
                              <i class="feature__icon iconfont icon-chuang"></i>
                              <span class="pl-2">200cm大床</span>
                            </span>
                            <span class="feature__item inline-flex items-center leading-none">
                              <i class="feature__icon iconfont icon-daxiao"></i>
                              <span class="pl-2">面积约58-78㎡</span>
                            </span>
                          </div>
                        </div> -->
                      </div>
                    </div>
                  </c:forEach>
                </div>
                <div class="swiper-scrollbar"></div>
              </div>
            </c:if>
            <c:if test="${fn:length(hotelRooms)==0 }">
              <div class="py-10 px-5 bg-bg_gary-light text-center">
                <p>
                  <fmt:message key="room_list.content"></fmt:message>
                </p>
              </div>
            </c:if>
          </div>
          <div id="section5" class="detail__section">
            <div class="section__hd">
              <h3 class="section__title slh-font-sunti"><fmt:message key="hotel_detail.locationAndPerimeter"></fmt:message></h3>
            </div>
            <div class="hotel__location">
              <div class="location__map"
                id="gmap"
                data-title="${hotel.hotelName}"
                data-countrycode="${hotel.countryCode}"
                data-zipcode="${hotel.postalCode}"
                data-address="${hotel.address}"
                data-addressen="${hotel.addressEn}"
                data-cityname="${city.cityName}"
                data-citynameen="${city.cityNameEn}"
                data-countrymame="${country.countryName}"
                data-countrymameen="${country.countryNameEn}"
                data-latitude="${hotel.latitude}"
                data-longitude="${hotel.longitude}"
              >
              </div>
              <div class="location__info">
                <div class="info__inner" id="locationInfoInner">
                  <div class="info__item">
                    <c:if test="${fn:length(landmarks) > 0}">
                      <h3 class="info__title slh-font-sunti">
                        <fmt:message key="hotel_map.landmarks"></fmt:message>
                      </h3>
                    </c:if>
                    <div class="info__cells">
                      <c:forEach var="landmark" items="${landmarks }">
                        <p>
                          <c:choose>
                            <c:when test="${fn:length(landmark.name)>0}">
                              ${landmark.name}
                            </c:when>
                            <c:otherwise>
                              ${landmark.nameEn}
                            </c:otherwise>
                          </c:choose>
                          <span class="text-primary">
                            <c:choose>
                              <c:when test="${landmark.distance !='' }">
                                ${landmark.distance}<fmt:message key="hotel_map.kilometre"></fmt:message>
                              </c:when>
                              <c:otherwise>
                                0<fmt:message key="hotel_map.kilometre"></fmt:message>
                              </c:otherwise>
                            </c:choose>
                          </span>
                        </p>
                      </c:forEach>
                    </div>
                  </div>
                  <div class="info__item">
                    <c:if test="${fn:length(transports) > 0}">
                      <h3 class="info__title slh-font-sunti">
                        <fmt:message key="hotel_map.transports"></fmt:message>
                      </h3>
                    </c:if>
                    <div class="info__cells">
                      <c:forEach var="transport" items='${transports }'>
                        <div class="info__cell">
                          <c:choose>
                            <c:when test="${transport.type=='Railway' }">
                              <span class="cell__lable"><fmt:message key="hotel_map.railway"></fmt:message>:</span>
                            </c:when>
                            <c:when test="${transport.type=='Subway' }">
                              <span class="cell__lable"></span><fmt:message key="hotel_map.subway"></fmt:message>:</span>
                            </c:when>
                            <c:when test="${transport.type=='Air' }">
                              <span class="cell__lable"></span><fmt:message key="hotel_map.air"></fmt:message>:</span>
                            </c:when>
                            <c:otherwise></c:otherwise>
                          </c:choose>
                          <div class="cell__info">
                            <c:if test="${fn:length(transport.name)>0}">${fn:replace(transport.name, '：', '')}</c:if>
                            <c:if test="${fn:length(transport.name)==0}">${fn:replace(transport.nameEn, '：', '')}</c:if>
                            <span class="text-primary">
                              ${transport.distance}
                              <fmt:message key="hotel_map.kilometre"></fmt:message>
                            </span>
                          </div>
                        </div>
                      </c:forEach>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <c:if test="${isFeatures}">
          <div id="section6" class="detail__section py-8 md:py-12 lg:py-14 xl:py-16">
            <div class="section__hd">
              <h3 class="section__title slh-font-sunti"><fmt:message key="hotel_detail.hotelFacilities"></fmt:message></h3>
            </div>
            <div class="bg-bg_gary-light p-2.5 md:p-3 lg:p-4 xl:p-5">
              <div class="bg-white py-6 md:py-10 lg:py-12 xl:py-16 px-4 md:px-20 lg:px-32 xl:px-60">
                <div class="facility__list">
                  <c:forEach var="feature" items="${featuresMap}">
                    <c:if test="${fn:length(feature.value) > 0}">
                      <c:forEach var="value" items="${feature.value}">
                        <div class="list__item"><span class="item__tip"><i class="tip__icon iconfont icon-right"></i></span>
                          ${value.name}
                        </div>
                      </c:forEach>
                    </c:if>
                  </c:forEach>
                </div>
              </div>
            </div>
          </div>
          </c:if>
        </div>
        <c:if test="${fn:length(cityHotels) > 0}">
          <div class="detail__section py-8 md:py-12 lg:py-14 xl:py-16 bg-bg_gary-light">
            <div class="slh-container">
              <div class="section__hd">
                <h3 class="section__title slh-font-sunti">${hotel.cityName}<fmt:message key="view.hotel_detail.other_hotel"></fmt:message></h3>
              </div>
              <div class="com-banner-swiper swiper theme__3">
                <div class="swiper-wrapper">
                  <c:forEach items="${cityHotels}" var="cityHotel">
                    <div class="swiper-slide swiper__slide">
                      <div class="slide__inner">
                        <div class="hotel__img__box">
                          <div class="hotel__img">
                            <c:choose>
                              <c:when test="${not empty cityHotel.coverUrl}">
                                <img class="aspect-ele" src="https://slh-cf-image-cdn.slhhotels.cn/proxy?url=${cityHotel.coverUrl}" alt="${cityHotel.hotelName}">
                              </c:when>
                              <c:otherwise>
                                <img class="aspect-ele" src="<%=path%>/static/assets/imgs/hotel_img_loading.jpg" alt="${cityHotel.hotelName}">
                              </c:otherwise>
                            </c:choose>
                          </div>
                        </div>
                        <div class="hotel__info">
                          <h3 class="hotel__name slh-font-sunti">${cityHotel.hotelName}</h3>
                          <h4 class="hotel__en__name">${cityHotel.hotelNameEn}</h4>
                          <p class="hotel__desc">
                            ${cityHotel.brief}</p>
                          <p class="hotel__address">
                            <i class="iconfont icon-location address__icon"></i>
                            <span class="pl-2">${country.countryName}·${city.cityName}</span>
                          </p>
                          <div class="hotel__actions">
                            <a
                              class="slh-btn-brand w-full md:max-w-[153px]"
                              href="<%=path%>/booking/rate_list?hotelCode=${cityHotel.hotelCode}"
                              onclick="window?.SlhGlobal?.gtagEvent('hotel_detail_recommend', '{hotelCode: ${cityHotel.hotelCode},hotelName: ${cityHotel.hotelName}}')"
                            >
                              <fmt:message key="hotel_list.view_price"></fmt:message>
                            </a>
                            <a class="slh-btn-default w-full md:max-w-[153px]"
                              href="<%=path%>/hotel/${cityHotel.hotelLink}"><fmt:message key="hotel_list.view_detail"></fmt:message></a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </c:forEach>
                </div>
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
              </div>
            </div>
          </div>
        </c:if>
        <%@ include file="/WEB-INF/view/partials/sub_footer.jsp" %>
      </div>
      <%@ include file="/WEB-INF/view/partials/footer.jsp" %>
    </div>
    <div id="hotelLeoScript"
      data-videoautoplay="false"
      data-autotabs="true"
      data-pid="${trustCode}"
      data-ids="150507"
      data-lang="en"
      data-app="vfmGalleryDock"
      data-sourcePath="https://hotel-image-proxy.slhhotels.cn/galleries/player/"
      data-theme="default"
      data-id="vfmviewer"
      data-responsive="${dataResponsive}"
      type="text/javascript">
    </div>
    <script type="module" src="${f:mix('src/hotel-detail.js')}"></script>
  </body>
  </html>