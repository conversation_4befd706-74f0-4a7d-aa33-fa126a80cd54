<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="cn.lhw.common.utils.SlhUtils"%>
<c:choose>
    <c:when test="${locale == 'zh_TW'}">
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KPS3RGG"
                          height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
    </c:when>
    <c:otherwise>
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PSCMHFL"
                          height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
    </c:otherwise>
</c:choose>
<div class="pc-header after-clear">
  <h1 class="logo">
  	<a href="<%=path%>/?locale=${locale}">
  		<c:choose>
        	<c:when test="${locale == 'zh_TW'}">
                <img src="<%=path1%>/static/images/logo_tra.png" class="pc-logo" /><img src="<%=path%>/static/images/mobile/logo_tra.png" class="m-logo"/>
     		</c:when>
     		<c:otherwise>
            	<img src="<%=path1%>/static/images/logo.png" class="pc-logo" /><img src="<%=path%>/static/images/mobile/logo.png" class="m-logo"/>
     		</c:otherwise>
 		</c:choose>
	</a>
  </h1>
  <div class="rightBox">
    <ul class="mainNav after-clear">
        <li><a href="<%=path%>" id="index_page" <c:if test="${currentPage=='index' }" >class="cur"</c:if>><fmt:message key="header_pc.index"></fmt:message></a></li>
        <li><a href="<%=path%>/country/china" id="domestic_page" <c:if test="${currentPage=='domestic' }" >class="cur"</c:if>><fmt:message key="header_pc.domestic"></fmt:message></a></li>
        <li><a class="Ihotel <c:if test="${currentPage=='international' }" >cur</c:if>" href="#" id="international_page"><fmt:message key="header_pc.international"></fmt:message></a></li>
        <li><a href="<%=path%>/invited" <c:if test="${currentPage=='club' }" >class="cur"</c:if>><fmt:message key="header_pc.club"></fmt:message></a></li>
        <li><a href="<%=path%>/offers" <c:if test="${currentPage=='activity' }" >class="cur"</c:if>><fmt:message key="header_pc.activity"></fmt:message></a></li>
        <li><a href="<%=path%>/brand-story/about-us" <c:if test="${currentPage=='brand-story' }" >class="cur"</c:if>><fmt:message key="header_pc.brandStroy"></fmt:message></a></li>
    </ul>
      <div class="mobile-lang langSelect" style="display: none;">
          <div class="con">
              <c:choose>
                  <c:when test="${locale == 'zh_TW'}">
                      <p data-value="中文繁體" class="selected">中文繁體</p>
                  </c:when>
                  <c:otherwise>
                      <p data-value="中文简体" class="selected">中文简体</p>
                  </c:otherwise>
              </c:choose>
              <i class="arrow"></i>
          </div>
          <ul class="langList clx">
              <li data-value="中文简体" class="cur"><a href="http://www.slhhotels.cn">中文简体</a></li>
              <li data-value="中文繁體"><a href="http://www.slhhotels.tw">中文繁體</a></li>
              <li data-value="English"><a href="http://www.slh.com" target="_blank">English</a></li>
          </ul>
      </div>      
    <div class="telBox">
      <p class="tel"><fmt:message key="index.telephone"></fmt:message></p>
      <div class="bBox clx">
<!--         <a href="https://www.yourreservation.net/tb3/index.cfm?bf=SLHChainChinese&modification&plprun=1&_=1487843517281" target="_blank" onclick="ga('send', 'event', 'button', 'click', '预订查询');">预订查询</a> -->
        <c:choose>
            <c:when test="${locale == 'zh_TW'}">
                <a href="https://be.synxis.com/signIn?adult=2&arrive=<%=SlhUtils.getDefaultCheckInDate()%>&chain=22402&child=0&depart=<%=SlhUtils.getDefaultCheckoutDate()%>&currency=CNY&level=chain&locale=zh-TW&rooms=1&sbe_ri=0" target="_blank" onclick="ga('send', 'event', 'button', 'click', '预订查询');"><fmt:message key="header_pc.reservationSearch"></fmt:message></a>
            </c:when>
            <c:otherwise>
                <a href="https://be.synxis.com/signIn?adult=2&arrive=<%=SlhUtils.getDefaultCheckInDate()%>&chain=22402&child=0&depart=<%=SlhUtils.getDefaultCheckoutDate()%>&currency=CNY&level=chain&locale=zh-CN&rooms=1&sbe_ri=0" target="_blank" onclick="ga('send', 'event', 'button', 'click', '预订查询');"><fmt:message key="header_pc.reservationSearch"></fmt:message></a>
            </c:otherwise>
        </c:choose>
        <span>|</span>
          <div class="langSelect selectBox">
              <div class="con">
                  <c:choose>
                      <c:when test="${locale == 'zh_TW'}">
                          <p data-value="中文繁體" class="selected">中文繁體</p>
                      </c:when>
                      <c:otherwise>
                          <p data-value="中文简体" class="selected">中文简体</p>
                      </c:otherwise>
                  </c:choose>
                  <i class="arrow"></i>
              </div>
              <ul class="listBox clx">
                  <li class="item" data-value="中文简体"><a href="http://www.slhhotels.cn">中文简体</a></li>
                  <li class="item" data-value="中文繁體"><a href="http://www.slhhotels.tw">中文繁體</a></li>
                  <li class="item" data-value="English"><a href="http://www.slh.com" target="_blank">English</a></li>
              </ul>
          </div>
          <span>|</span>
          <div class="currencySelect selectBox">
          	<div class="con">
            	<c:choose>
                	<c:when test="${locale == 'zh_TW'}">
                    	<p data-value="twd" class="selected">臺幣</p>
                    </c:when>
                    <c:otherwise>
                    	<p data-value="人民币" class="selected">人民币</p>
                    </c:otherwise>
                </c:choose>
                <i class="arrow"></i>          	
          	</div>
          	<ul class="listBox clx">
	        	<li class="item" data-value="cny"><a href="javascript:void(0)">人民幣</a></li>
	          	<li class="item" data-value="twd"><a href="javascript:void(0)">臺幣</a></li>
	            <li class="item" data-value="hkd"><a href="javascript:void(0)">港幣</a></li>
	            <li class="item" data-value="mop"><a href="javascript:void(0)">澳門幣</a></li>
	          	<li class="item" data-value="usd"><a href="javascript:void(0)">美元</a></li>
          	</ul>
          </div>
      </div>
    </div>
    <!-- <div class="search-nav">
        <div class="search-nav-cont">
          <span class="icon-search"></span>  
        </div>
        <div class="search-box cxl">
          <div class="search-box-inner">
            <div class="search-bar"><input type="text" placeholder="请输入您要入住的国家、城市或酒店名称"></div>
            <div class="search-result">
              <div class="item item-empty text-center">
                <p>没有符合<em>多少分多少</em>的结果</p>
                <p>请重新搜索</p>
              </div>
              <div class="item">
                <h3>国家</h3>
                <ul class="list">
                  <li class="list-item"><a href="hotel-list.html?countryCode=CN">大中华区</a><span>共 <em>13</em> 家酒店</span></li>
                  <li class="list-item"><a href="hotel-list.html?countryCode=CN">大中华区</a><span>共 <em>13</em> 家酒店</span></li>
                  <li class="list-item"><a href="hotel-list.html?countryCode=CN">大中华区</a><span>共 <em>13</em> 家酒店</span></li>
                </ul>
              </div>
              <div class="item">
                <h3>国家</h3>
                <ul class="list">
                  <li class="list-item"><a href="hotel-list.html?countryCode=CN">大中华区</a><span>共 <em>13</em> 家酒店</span></li>
                  <li class="list-item"><a href="hotel-list.html?countryCode=CN">大中华区</a><span>共 <em>13</em> 家酒店</span></li>
                  <li class="list-item"><a href="hotel-list.html?countryCode=CN">大中华区</a><span>共 <em>13</em> 家酒店</span></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
    </div>-->
    <a href="javascript:" class="smallTopNav" id="J_pcTopNav"><span></span><span></span><span></span></a>
    <a href="javascript:" class="smallClose smallClose-hide rotateIn animated" id="J_pcCloseNav">close</a>
    <a href="javascript:" class="smallClose smallClose-hide rotateIn animated" id="J_CloseMobleNav">close</a>
    <a href="javascript:" class="smallClose smallClose-hide rotateIn animated" id="J_CloseHotelsOver">close</a>
  </div>
  <div class="sub_nav after-clear">
    <div class="europe item w240">
      <h4><a href="javascript:void(0);"><fmt:message key="header_pc.Europe"></fmt:message></a></h4>
      <div class="destContent" id="overseaContinent_Europe">
      <c:forEach var="country" items="${continent_Europe.countries}">
      	<a href="<%=path%>/country/${country.countryLink}?locale=${locale}">${country.countryName}</a>
      </c:forEach>
      </div>
    </div>
    <div class="Asian item w120">
      <h4><a href="javascript:void(0);"><fmt:message key="header_pc.Asian"></fmt:message></a></h4>
      <div class="destContent" id="overseaContinent_Asia">
	      <c:forEach var="country" items="${continent_Asia.countries}">
	      	<a href="<%=path%>/country/${country.countryLink}?locale=${locale}">${country.countryName}</a>
	      </c:forEach>
      </div>
    </div>
    <div class="Indian item w120">
      <h4><a href="javascript:void(0);"><fmt:message key="header_pc.IndianOcean"></fmt:message></a></h4>
      <div class="destContent" id="overseaContinent_IndianOcean">
	      <c:forEach var="country" items="${continent_IndianOcean.countries}">
	      	<a href="<%=path%>/country/${country.countryLink}?locale=${locale}">${country.countryName}</a>
	      </c:forEach></div>
      <div class="blank20"></div>
      <h4><a href="javascript:void(0);"><fmt:message key="header_pc.Oceania"></fmt:message></a></h4>
      <div class="destContent" id="overseaContinent_Australasia">
	      <c:forEach var="country" items="${continent_Australasia.countries}">
	      	<a href="<%=path%>/country/${country.countryLink}?locale=${locale}">${country.countryName}</a>
	      </c:forEach></div>
    </div>
    <div class="America item w120">
      <h4><a href="javascript:void(0);"><fmt:message key="header_pc.America"></fmt:message></a></h4>
      <div class="destContent" id="overseaContinent_Americas">
	      <c:forEach var="country" items="${continent_Americas.countries}">
	      	<a href="<%=path%>/country/${country.countryLink}?locale=${locale}">${country.countryName}</a>
	      </c:forEach></div>
    </div>
    <div class="CARIBBEAN item w120">
      <h4><a href="javascript:void(0);"><fmt:message key="header_pc.Caribbean"></fmt:message></a></h4>
      <div class="destContent" id="overseaContinent_Caribbean">
	      <c:forEach var="country" items="${continent_Caribbean.countries}">
	      	<a href="<%=path%>/country/${country.countryLink}?locale=${locale}">${country.countryName}</a>
	      </c:forEach></div>
    </div>
    <div class="Africa item w120">
      <h4><a href="javascript:void(0);"><fmt:message key="header_pc.Africa"></fmt:message></a></h4>
      <div class="destContent" id="overseaContinent_Africa">
	      <c:forEach var="country" items="${continent_Africa.countries}">
	      	<a href="<%=path%>/country/${country.countryLink}?locale=${locale}">${country.countryName}</a>
	      </c:forEach></div>
      <div class="blank20"></div>
      <h4><a href="javascript:void(0);"><fmt:message key="header_pc.MiddleEast"></fmt:message></a></h4>
      <div class="destContent" id="overseaContinent_MiddleEast">
	      <c:forEach var="country" items="${continent_MiddleEast.countries}">
	      	<a href="<%=path%>/country/${country.countryLink}?locale=${locale}">${country.countryName}</a>
	      </c:forEach></div>
    </div>
  </div>
</div>