<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/view/include/include-top.jsp"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<%@ include file="/WEB-INF/view/include/head.jsp"%>

<link rel="stylesheet" href="<%=path%>/static/js/swiper/css/swiper.css">
<link rel="stylesheet" href="<%=path%>/static/css/detail.css?v=20171122">
<script src="<%=path%>/static/js/jquery.nav.js"></script>
<c:if test="${locale == 'zh_CN'}">
	<script src="<%=path%>/static/js/baiduTransTracker.js"></script>
</c:if>

<script id="hotelLeoScript"
		data-videoautoplay="false"
		data-autotabs="true"
		data-pid="${trustCode}"
		data-ids="150507"
		data-lang="en"
		data-app="vfmGalleryDock"
		data-sourcePath="https://hotel-image-proxy.slhhotels.cn/galleries/player/"
		data-theme="default"
		data-id="vfmviewer"
		data-responsive="${dataResponsive}"
		type="text/javascript"
		src="<%=path%>/static/js/loader.min.js"
		charset="UTF-8" ></script>
</head>
<body>

	<!-- pc header start -->
	<%@ include file="/WEB-INF/view/include/header_pc.jsp"%>
	<!-- pc header end -->

	<!-- mobile nav start -->
	<%@ include file="/WEB-INF/view/include/mobile-nav.jsp"%>
	<!-- mobile nav end -->

	<!-- mobile search start -->
	<%@ include file="/WEB-INF/view/include/mobile-search.jsp"%>
	<!-- mobile search end -->

	<!--全部城市-->
	<%@ include file="/WEB-INF/view/include/allCities-m.jsp"%>

	<!-- 时间选择-->
	<%@ include file="/WEB-INF/view/include/hotel-detail-date-picker.jsp"%>

	<!-- page wrapper start -->
	<div class="wrapper">
		<!--hotelBanner satrt-->
		<%@ include file="/WEB-INF/view/include/hotel-detail-banner.jsp"%>
		<!--hotelBanner end-->
		<!-- crumb nav start-->
		<div class="crumb-nav">
			<div class="fixed-wrapper" id="crumb" name="crumb">
				<a href="<%=path%>/"><span><fmt:message key="hotel_detail.index"></fmt:message></span></a><i class="line">&gt;</i><a
					href="<%=path%>/country/${hotel.countryLink}?locale=${locale}"><span>${hotel.countryName}</span></a><i
					class="line">&gt;</i><span class="cur"><c:choose>
						<c:when test="${hotel.hotelName!='' }">${fn:replace(hotel.hotelName,'<br>','') }</c:when>
						<c:when test="${hotel.hotelAliasEn!='' }">${hotel.hotelAliasEn }</c:when>
						<c:otherwise>${hotel.hotelNameEn }</c:otherwise>
					</c:choose></span>
			</div>
		</div>
		<!-- crumb nav start-->
		<!-- hotel-detail start -->
		<div class="hotel-detail">
		<c:if test="${hotel.emergencyNotice!=null&&hotel.emergencyNotice!=''}">
			<div class="line" style="padding: 20px;">
				<div style="margin: 0 auto;width: fit-content;">
					<p style="font-weight: bolder;font-size: 18px;">${hotel.emergencyNotice}</p>
				</div>
			</div>
		</c:if>
			<div class="detail-nav" id="detail-nav">
				<div class="fixed-wrapper text-center">
					<ul class="navs after-clear" id="hotel-detail-ul">
						<li class="float-left hotel-detail-top">
							<a href="#roomList" class="hotel-click"><fmt:message key="hotel_detail.roomReservation"></fmt:message></a>
						</li>
						<li class="float-left hotel-detail-top">
							<a href="#hotelInfo" class="hotel-click"><fmt:message key="hotel_detail.hotelInformation"></fmt:message></a>
						</li>
						<li class="float-left hotel-detail-top">
							<a href="#mapInfo" id="mapNav" class="hotel-click"><fmt:message key="hotel_detail.locationAndPerimeter"></fmt:message></a>
						</li>
						<li class="float-left hotel-detail-top">
							<a href="#facilitiesInfo" class="hotel-click"><fmt:message key="hotel_detail.hotelFacilities"></fmt:message></a>
						</li>
						<!--
						<c:if test="${hotel.virusPolicy!=null&&hotel.virusPolicy!=''}">
							<li class="float-left hotel-detail-top">
								<a href="#policy" class="hotel-click"><fmt:message key="hotel_detail.virusPolicy"></fmt:message></a>
							</li>
						</c:if>
						-->
						<li class="float-right" id="header-book">
							<div class="btnBox">
								<a class="btn booking hotel-close" id="J_findBtn" onclick="javascript:booking('','');"><fmt:message key="hotel_detail.reservation"></fmt:message></a>
							</div>
						</li>
						<li class="float-right" id="web">
						<!--<a href="#facilitiesInfo">-->
						<!-- hotelFilter start -->
							<div id="hotel-detail-posi" class="hotel-detail-hover">
								<div class="" id="fixed-wrapper">
									<div class="detail-content" id="detail-content">
										<%@ include file="/WEB-INF/view/include/hotel-detail-hotel-filter.jsp"%>
									</div>
								</div>
							</div>
						<!-- hotelFilter end -->
						</li>
					</ul>
				</div>
			</div>
			<div class="detail-content fixed-wrapper">
			<!-- room list start -->
			<%@ include file="/WEB-INF/view/include/room-list.jsp"%>
			<!-- room list end -->
			<!-- hotel-info start -->
			<%@ include file="/WEB-INF/view/include/hotel-info.jsp"%>
			<!-- hotel-info end -->
			<!-- map-info start -->
			<%@ include file="/WEB-INF/view/include/hotel-map.jsp"%>
			<!-- map-info end -->
			<!-- facilities-info start -->
			<%@ include file="/WEB-INF/view/include/hotel-facilities.jsp"%>
			<!-- facilities-info end -->
			<%@ include file="/WEB-INF/view/include/nearby-hotels.jsp"%>
			</div>
			</div>
			<!-- hotel-detail start -->
			</div>
	<!-- page wrapper end -->

	<!-- pc footer start -->
	<%@ include file="/WEB-INF/view/include/footer_pc.jsp"%>
	<!-- pc footer end -->

	<!-- mobile footer start -->
	<%@ include file="/WEB-INF/view/include/footer_m.jsp"%>
	<!-- mobile footer end -->

	<!-- right sidebar start -->
	<%@ include file="/WEB-INF/view/include/right-sidebar.jsp"%>
	<!-- right sidebar start -->
<!--     <div class="notice_pop">
        <div class="notice_box">
            <a href="javascript:void(0);" class="noticeCloseBtn"></a>
            <div class="con">
                <span class="imgBg"></span>
                <p>由于预订系统维护</br>
                    <i>2019年2月9日19:00 - 2月10日0:00</i>网上预订暂停服务</br>
                    如需预订酒店,请拨打</br>
                    贵宾服务热线：<a href="tel:4001-203276">4001-203276</a></p>
            </div>
        </div>
    </div> -->
	<!-- 页面公用方法 -->
	<script>
		var locale = "${locale}";
		var path = "<%=path%>";
		var templatePath = "<%=path%>/template";
		var hotelCode = "${hotelCode}";
		var sabreCode = "${sabreCode}";
		var originChainCode = "${originChainCode}";
		var checkinDate = "${checkinDate}";
		var checkoutDate = "${checkoutDate}";
		var nights = "${nights}";
		var adultNumber = "${adultNumber}";
		var roomNumber = "${roomNumber}";
		
		var longitude = "${hotel.longitude}";
		var latitude = "${hotel.latitude}";
		var address = "${hotel.address}";
		var addressEn = "${hotel.addressEn}";
		var zipcode = "${hotel.postalCode}";
		var cityName = "${city.cityName}";
		var cityNameEn = "${city.cityNameEn}";
		var countryCode = "${hotel.countryCode}";
		var countryName = "${country.countryName}";
		var countryNameEn = "${country.countryNameEn}";
		var hotelName = "${hotel.hotelName}";
		var hotelNameEn = "${hotel.hotelNameEn}";
		var hotelAliasEn  = "${hotel.hotelAliasEn}";
		var currency  = "${hotel.currency}";
		
		var iataNumber = "${iataNumber}";
		var rateAccessCode = "${rateAccessCode}";
		console.log("iataNumber23:"+iataNumber)
		console.log("rateAccessCode23:"+rateAccessCode)
	</script>

	<script>
	    $("#lookmap").click(function(event){
	      event.preventDefault();
	      $('#mapNav').trigger('click');
	    });
		// var ue = UE.getEditor('itemContent');
		// ue.ready(function() {
		// });
	</script>
	<%-- <script src='<%=path%>/static/js/hotelPage.js'></script> --%>
	<script src="<%=path%>/static/js/new_common.js?v=20180427"></script>
	<script src="<%=path%>/static/js/hotel.js"></script>

	<script src='<%=path%>/static/slh/config.js?v=20180426'></script>
	<script src="<%=path%>/static/js/handlebars-v4.0.5.js"></script>
	<script src="<%=path%>/static/js/nodetpl.js"></script>
	<script src="<%=path%>/static/js/fetch-jsonp.js"></script>
	<script src='<%=path%>/static/slh/common.js?v=20180426'></script>
	<script src="<%=path%>/static/slh/handlebars-helper.js?v=20180328"></script>
	<script src='<%=path%>/static/slh/swiper.js'></script>
	<script src='<%=path%>/static/slh/hotel.js?v=20171129'></script>
	<script src='<%=path%>/static/slh/destination.js?v=20170904'></script>
	<script src='<%=path%>/static/slh/hoteldetail-data.js?v=20170904'></script>
	<script src='<%=path%>/static/slh/booking.js?v=20180522'></script>
<%--	<c:choose>
		<c:when test="${locale =='zh_TW' }">
			<script src='https://gmaps.dragongap.cn/maps/api/js?key=AIzaSyAvAu1D-6wjlms4giwxxQPW4w2MOrE4wB0'></script>
		</c:when>
		<c:otherwise>
			<script src='https://gmaps.dragongap.cn/maps/api/js?key=AIzaSyAvAu1D-6wjlms4giwxxQPW4w2MOrE4wB0'></script>
		</c:otherwise>
	</c:choose>--%>
	<!--<script type="text/javascript"
		src="http://ditu.google.cn/maps/api/js?language=zh-CN&key=AIzaSyATSWsNyOIlm5BXm9fk8ghj2kv6aCEnTXk&amp;sensor=false&amp;v=3"></script> -->
	<!--<script type="text/javascript" src="http://maps.googleapis.com/maps/api/js?key=AIzaSyCA29XsSGpCrd3s89qM1dcGDYwLsR5VEdM&amp;sensor=false&amp;v=3"></script>-->
	<!--sensor=false& <script type="text/javascript" src="http://ditu.google.cn/maps?file=api&amp;v=2&amp;key=AIzaSyATSWsNyOIlm5BXm9fk8ghj2kv6aCEnTXk&amp;language=zh-CN"></script> -->
	<script type="text/javascript" src="<%=path%>/static/js/gmap3.js"></script>
	<script src='<%=path%>/static/slh/map.js'></script>
</body>
</html>
