<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/view/include/include-top.jsp"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8" />
<meta name="author" content="zepo" />
<meta name="viewport"
	content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title>${title }</title>
<meta name="format-detection" content="telephone=no">
<meta id="keywords" name="keywords" content="${keywords }">
<meta id="description" name="description" content="${description }">
<link rel="stylesheet" href="<%=path%>/static/css/global.css?v=20170904">
<link rel="stylesheet" href="<%=path2%>/static/js/swiper/css/swiper.css?v=20170906">
<link rel="stylesheet" href="<%=path2%>/static/css/main.css?v=20170906" type="text/css" />
<link rel="stylesheet" media="(max-width: 768px)"
	href="<%=path3%>/static/css/mobile.css?v=20170904" />
<link rel="stylesheet" href="<%=path4%>/static/css/h5.css?v=20170904">
<link rel="stylesheet" href="<%=path%>/static/css/app.css?v=20210916">
<link rel="stylesheet" href="<%=path%>/static/css/index.css?v=20171123">
<link rel="shortcut icon" href="<%=path1%>/static/images/favicon.ico" />
<script src="<%=path2%>/static/js/jquery-1.8.3.min.js"></script>
<!--[if lt IE 9]>
<script src="<%=path3%>/static/js/jquery-1.8.3.min.js"></script>
<script src="<%=path4%>/static/js/html5shiv.js"></script>
<script src="<%=path%>/static/js/css3-mediaqueries.js"></script>
<script src="<%=path1%>/static/js/upgrade_browser.js"></script>
<![endif]-->
<script src="<%=path2%>/static/js/swiper/js/swiper.jquery.min.js"></script>

<!--如下js只在首页加载-->
<script src='<%=path%>/static/js/imagesloaded.pkgd.min.js'></script>
<script src='<%=path1%>/static/js/searche.js?v=20171101'></script>

<!--时间选择插件-->
<link rel="stylesheet" href="<%=path%>/static/css/daterangepicker.css">
<script src="<%=path%>/static/js/moment.min.js"></script>
<script src="<%=path%>/static/js/jquery.daterangepicker.min.js"></script>

<script src='<%=path2%>/static/slh/baidu_google.js'></script>
<script src='<%=path4%>/static/slh/httplog.js'></script>
<c:if test="${locale=='zh_TW'}">
	<script async src="https://www.googletagmanager.com/gtag/js?id=G-J3LLN8L1HW"></script>
</c:if>
	<c:if test="${locale !='zh_TW'}">
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-8PF1VSZSHD"></script>
	</c:if>
<!-- Google Tag Manager -->
<script>
var locale = "${locale}";
if( locale == 'zh_TW'){
	window.dataLayer = window.dataLayer || [];
	function gtag(){dataLayer.push(arguments);}
	gtag('js', new Date());
	gtag('config', 'G-J3LLN8L1HW');
	//百度监测
	var _hmt = _hmt || [];
	(function() {
	  var hm = document.createElement("script");
	  hm.src = "https://hm.baidu.com/hm.js?a92b18f963549db3d1bc39b4604fdd4b";
	  var s = document.getElementsByTagName("script")[0]; 
	  s.parentNode.insertBefore(hm, s);
	})();
	<!-- Google Tag Manager -->
	(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
				new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
			j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
			'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
	})(window,document,'script','dataLayer','GTM-KPS3RGG');
	<!-- End Google Tag Manager -->
}else{
	window.dataLayer = window.dataLayer || [];
	function gtag(){dataLayer.push(arguments);}
	gtag('js', new Date());
	gtag('config', 'G-8PF1VSZSHD');
	<!-- Google Tag Manager -->
	(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
				new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
			j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
			'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
	})(window,document,'script','dataLayer','GTM-PSCMHFL');
	<!-- End Google Tag Manager -->
}
</script>
<!-- End Google Tag Manager -->
</head>
<body>
<!-- 	<div class="notice_pop">
		<div class="notice_box">
            <a href="javascript:void(0);" class="noticeCloseBtn"></a>
			<div class="con">
				<span class="imgBg"></span>
				<p>由于预订系统维护</br>
                    <i>2019年2月9日19:00 - 2月10日0:00</i>网上预订暂停服务</br>
					如需预订酒店,请拨打</br>
					贵宾服务热线：<a href="tel:4001-203276">4001-203276</a></p>
			</div>
		</div>
	</div> -->
	<!-- pc header start -->
	<%@ include file="/WEB-INF/view/include/header_pc.jsp"%>
	<!-- pc header end -->

	<!-- mobile nav start -->
	<%@ include file="/WEB-INF/view/include/mobile-nav.jsp"%>
	<!-- mobile nav end -->

	<!-- mobile search start -->
	<%@ include file="/WEB-INF/view/include/mobile-search.jsp"%>
	<!-- mobile search end -->

	<!--全部城市-->
	<%@ include file="/WEB-INF/view/include/allCities-m.jsp"%>

	<!-- 时间选择-->
	<%@ include file="/WEB-INF/view/include/hotel-rate-date-picker-index.jsp"%>

	<!--手机端目的地城市-->
	<section class='area-hotel content-area' id="m_destinationBox">
		<div class='area-hotel-inner'>
			<ul class="area-menu">
				<li><a href="#"><fmt:message key="index.hot"></fmt:message></a></li>
				<li><a href="#"><fmt:message key="index.domestic"></fmt:message></a></li>
				<li><a href="#"><fmt:message key="index.Asian"></fmt:message></a></li>
				<li><a href="#"><fmt:message key="index.America"></fmt:message></a></li>
				<li><a href="#"><fmt:message key="index.Europe"></fmt:message></a></li>
				<li><a href="#"><fmt:message key="index.Oceania"></fmt:message></a></li>
				<li><a href="#"><fmt:message key="index.Africa"></fmt:message></a></li>
				<li><a href="#"><fmt:message key="index.other"></fmt:message></a></li>
			</ul>
			<ul class="area-content">
				<li class="item after-clear" id="">
					<div class="clx">
						<dl>
							<dt><fmt:message key="index.hotCities"></fmt:message></dt>
							<dd class="clx" id="m_hotsite_city_destination">
								<jsp:include page="include/hotsite_cities.jsp" />
							</dd>
						</dl>
						<dl>
							<dt><fmt:message key="index.hotCountries"></fmt:message></dt>
							<dd class="clx" id="m_hotsite_country_destination">
								<jsp:include page="include/hotsite_countries.jsp" />
							</dd>
						</dl>
					</div>
				</li>
				<li class="item after-clear" id="m_destinationContinent_domestic">
					<jsp:include page="include/domestic_m.jsp" />
				</li>
				<li class="item after-clear" id="m_destinationContinent_Asia">
					<jsp:include page="include/destination_countries_list_m.jsp">
						<jsp:param value="Asia" name="continentName" />
					</jsp:include>
				</li>
				<li class="item after-clear" id="m_destinationContinent_Americas">
					<jsp:include page="include/destination_countries_list_m.jsp">
						<jsp:param value="Americas" name="continentName" />
					</jsp:include>
				</li>
				<li class="item after-clear" id="m_destinationContinent_Europe">
					<jsp:include page="include/destination_countries_list_m.jsp">
						<jsp:param value="Europe" name="continentName" />
					</jsp:include>
				</li>
				<li class="item after-clear" id="m_destinationContinent_Australasia">
					<jsp:include page="include/destination_countries_list_m.jsp">
						<jsp:param value="Australasia" name="continentName" />
					</jsp:include>
				</li>
				<li class="item after-clear" id="m_destinationContinent_Africa">
					<jsp:include page="include/destination_countries_list_m.jsp">
						<jsp:param value="Africa" name="continentName" />
					</jsp:include>
				</li>
				<li class="item after-clear" id="">
					<div class="clx">
						<dl>
							<dt><fmt:message key="index.IndianOcean"></fmt:message></dt>
						</dl>
						<div class="clx" id="m_destinationContinent_IndianOcean">
							<jsp:include page="include/destination_countries_list_m.jsp">
								<jsp:param value="IndianOcean" name="continentName" />
							</jsp:include>
						</div>
						<dl>
							<dt><fmt:message key="index.Caribbean"></fmt:message></dt>
						</dl>
						<div class="clx" id="m_destinationContinent_Caribbean">
							<jsp:include page="include/destination_countries_list_m.jsp">
								<jsp:param value="Caribbean" name="continentName" />
							</jsp:include>
						</div>
						<dl>
							<dt><fmt:message key="index.MiddleEast"></fmt:message></dt>
						</dl>
						<div class="clx" id="m_destinationContinent_MiddleEast">
							<jsp:include page="include/destination_countries_list_m.jsp">
								<jsp:param value="MiddleEast" name="continentName" />
							</jsp:include>
						</div>
					</div>
				</li>
			</ul>
		</div>
	</section>

	<div id="wrapper">

		<div class="pcSite">
			<!--活动悬浮层
			<div id="float-active" style="width:290px;height:267px;position:fixed;left:0px;bottom:10px;z-index: 1000000;">
				<div id="" style="position: relative;">
					<img src ="<%=path1%>/static/images/float-close.png" style="width:12px;height:12px;position:absolute;top: 10px;right: 20px;cursor: pointer;" id="float-close">
					<img src ="<%=path2%>/static/images/1.png" >
				</div>
			</div>-->
			<!-- pc端搜索筛选开始 -->
			<div id="pc-search">
				<div class="fixed-wrapper after-clear">
					<form class="row-item float-left search-box" action="" method="post" id="queryForm" name="queryForm" onsubmit="submitQueryForm();">
						<div class="row-item-inner">
							<p class="label"><fmt:message key="index.destination"></fmt:message></p>
							<input type="hidden" id="searchType">
							<input type="hidden" id="searchCode">
							<div class="input-box"><input id="search-hotel" autocomplete="off" type="text" class="txtCss" placeholder="<fmt:message key="index.placeholder"></fmt:message>" /></div>
						</div>
						<div class="searchContent">
							<div class="keyinput" style="display:none;">
								<div class="keyBox"></div>
							</div>
							<div class="onfocus" id="searchBox">
								<ul class="addList clx">
									<li><a class="cur" href="#"><fmt:message key="index.hot"></fmt:message></a></li>
									<li><a href="#"><fmt:message key="index.China"></fmt:message></a></li>
									<li><a href="#"><fmt:message key="index.Asian"></fmt:message></a></li>
									<li><a href="#"><fmt:message key="index.America"></fmt:message></a></li>
									<li><a href="#"><fmt:message key="index.Europe"></fmt:message></a></li>
									<li><a href="#"><fmt:message key="index.Oceania"></fmt:message></a></li>
									<li><a href="#"><fmt:message key="index.Africa"></fmt:message></a></li>
									<li><a href="#"><fmt:message key="index.other"></fmt:message></a></li>
								</ul>
								<div class="content">
									<div class="itemData itemData-show">
										<div class="item">
											<h3><fmt:message key="index.hotCities"></fmt:message></h3>
											<div class="box clx" id="hotsite_city_searchbar"
												name="hotsite_city_searchbar">
												<jsp:include page="include/search_hotsite_cities.jsp" />
											</div>
										</div>
										<div class="item">
											<h3><fmt:message key="index.hotCountries"></fmt:message></h3>
											<div class="box clx" id="hotsite_country_searchbar">
												<jsp:include page="include/search_hotsite_countries.jsp" />
											</div>
										</div>
									</div>
									<div class="itemData">
										<div class="con clx" id="searchCity_domestic">
											<jsp:include page="include/search_domestic_pc.jsp" />
										</div>
									</div>

									<div class="itemData" id="searchContinent_Asia">
										<div class="con clx">
											<jsp:include page="include/search_countries_list_pc.jsp" >
												<jsp:param value="Asia" name="continentName"/>
											</jsp:include>
										</div>
									</div>
									<div class="itemData" id="searchContinent_Americas">
										<div class="con clx">
											<jsp:include page="include/search_countries_list_pc.jsp" >
												<jsp:param value="Americas" name="continentName"/>
											</jsp:include>
										</div>
									</div>
									<div class="itemData" id="searchContinent_Europe">
										<div class="con clx">
											<jsp:include page="include/search_countries_list_pc.jsp" >
												<jsp:param value="Europe" name="continentName"/>
											</jsp:include>
										</div>
									</div>
									<div class="itemData" id="searchContinent_Australasia">
										<div class="con clx">
											<jsp:include page="include/search_countries_list_pc.jsp" >
												<jsp:param value="Australasia" name="continentName"/>
											</jsp:include>
										</div>
									</div>
									<div class="itemData" id="searchContinent_Africa">
										<div class="con clx">
											<jsp:include page="include/search_countries_list_pc.jsp" >
												<jsp:param value="Africa" name="continentName"/>
											</jsp:include>
										</div>
									</div>
									<div class="itemData">
										<div class="item">
											<h3 class="title"><fmt:message key="index.IndianOcean"></fmt:message></h3>
											<div class="con clx" id="searchContinent_IndianOcean"
												style="margin-left: -20px;">
												<jsp:include page="include/search_countries_list_pc.jsp" >
													<jsp:param value="IndianOcean" name="continentName"/>
												</jsp:include>
											</div>
										</div>
										<div class="item">
											<h3 class="title"><fmt:message key="index.Caribbean"></fmt:message></h3>
											<div class="con clx" id="searchContinent_Caribbean"
												style="margin-left: -20px;">
												<jsp:include page="include/search_countries_list_pc.jsp" >
													<jsp:param value="Caribbean" name="continentName"/>
												</jsp:include>
											</div>
										</div>
										<div class="item">
											<h3 class="title"><fmt:message key="index.MiddleEast"></fmt:message></h3>
											<div class="con clx" id="searchContinent_MiddleEast"
												style="margin-left: -20px;">
												<jsp:include page="include/search_countries_list_pc.jsp" >
													<jsp:param value="MiddleEast" name="continentName"/>
												</jsp:include>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</form>
					<div class="row-item float-left picker">
						<ul class="row-item-inner after-clear">
							<li class="picker-item picker-item-date float-left">
								<p class="label"><fmt:message key="index.checkIn"></fmt:message></p>
								<p class="picker-value startTime" id="startTime"></p>
							</li> 
							<li class="picker-item picker-item-arrow float-left text-center"><i class="icon-arrow"></i></li>
							<li class="picker-item picker-item-date float-left">
								<p class="label"><fmt:message key="index.checkOut"></fmt:message></p>
								<p class="picker-value endTime" id="endTime"></p>
							</li>
							<li class="picker-item float-left">
								<p class="label"><fmt:message key="index.nightsQuantity"></fmt:message></p>
								<p class="picker-value nights" id="nights"></p>
							</li>
							<li class="picker-item float-right">
								<p class="label"><fmt:message key="index.adult"></fmt:message></p>
								<p class="picker-value persons"></p>
							</li>
						</ul>
						<div class="picker-drop-down">
							<input type="hidden" id="date-range">
							<div class="timePlugin" id="date-range-container"></div>
							<div class="select-box">
								<span><fmt:message key="index.adultQuantity"></fmt:message></span>
								<select class="input-box" id="persons">	
								  <option>1</option>
								  <option>2</option>
								  <option>3</option>
								  <option>4</option>
								  <option>5</option>
								  <option>6</option>
								</select>
							</div>
						</div>
					</div>
					<div class="row-item float-right btn-box">
						<button class="book-btn" id="search_btn_pc" ><fmt:message key="index.searchHotel"></fmt:message></button>
					</div>
				</div>
			</div>
			<!-- pc端搜索筛选结束 -->
			<div class="swiper-container index_swiper" id="index_swiper">
				<div class="swiper-wrapper">
					<div class="swiper-slide slideItem">
						<div class="swiper-container" id="bannerSwiper">
							<div class="swiper-wrapper">
							<c:forEach var="activity" items="${swiperPC}" begin="0" end="500">
								<div class="swiper-slide"
									<c:choose>
										<c:when test="${activity.bannerTra != '' && locale == 'zh_TW'}">
											style="background-image:url('${activity.bannerTra}')"
										</c:when>
										<c:otherwise>
											style="background-image:url('${activity.banner}')"
										</c:otherwise>
									</c:choose>
								>
								<c:if test="${'' != activity.link}">
									<c:if test="${'external'!=activity.linkType}"><a href="<%=path%>${activity.link}?locale=${locale}" class="hotelDetailUrl"></a></c:if>
									<c:if test="${'external'==activity.linkType}"><a href="${activity.link}" target="_blank"></a></c:if>
								</c:if>
								</div>
							</c:forEach>
		                    </div>
		                    <div class="swiper-pagination"></div>
		                </div>
					</div>
					<!--item end-->

					<div class="swiper-slide slideItem">
						<section class="tjzt" id="J_tjzt">
							<h2>
								<!-- 推荐主题 -->
							</h2>
							<div class="outletBox wiper-container" id="J_outletBox">
								<%@ include
									file="/WEB-INF/view/include/index-experience-list_pc.jsp"%>
							</div>
							<!--outletBox  end-->
						</section>
					</div>
					<!--item end-->

					<div class="swiper-slide slideItem">
						<section class="huodong">
							<div class="box">
								<div class="tit">
									<h2><fmt:message key="index.latestActivity"></fmt:message></h2>
									<div class="line"></div>
								</div>
								<div class="active-wrapper" id="J_hdBox">
									<%@ include
										file="/WEB-INF/view/include/index-promotion-list_pc.jsp"%>
								</div>
							</div>

						</section>
						<!--huodong end-->
					</div>
					<!--item end-->

					<div class="swiper-slide slideItem">
						<section class="wxjx ver">
							<div class="container-fluid clx" id="masonry">
								<%@ include
									file="/WEB-INF/view/include/index-weChat-list_pc.jsp"%>
							</div>
							<!--container-fluid  end-->
						</section>
						<!--wxjx end-->

					</div>
					<!--item end-->



					<div class="swiper-slide slideItem destinationArea" style="width: 100%;">
						<div id="pc-swiper-footer" class="swiper-container swiper-container-vertical swiper-container-free-mode">
							<div class="swiper-wrapper"> 
								<div class="swiper-slide">
									<section class="destination">
										<div class="box">
											<h2><fmt:message key="index.destination"></fmt:message></h2>
											<div class="line"></div>
											<section class="desBox" id="J_desBox">
												<ul class="dList clx">
													<li><a class="cur" href="javascript:"><fmt:message key="index.hot"></fmt:message></a></li>
													<li><a href="javascript:"><fmt:message key="index.domestic"></fmt:message></a></li>
													<li><a href="javascript:"><fmt:message key="index.Asian"></fmt:message></a></li>
													<li><a href="javascript:"><fmt:message key="index.America"></fmt:message></a></li>
													<li><a href="javascript:"><fmt:message key="index.Europe"></fmt:message></a></li>
													<li><a href="javascript:"><fmt:message key="index.Oceania"></fmt:message></a></li>
													<li><a href="javascript:"><fmt:message key="index.Africa"></fmt:message></a></li>
													<li><a href="javascript:"><fmt:message key="index.other"></fmt:message></a></li>
												</ul>
												<div class="tabsContent">
													<div class="itemData itemData-show">
														<div class="item">
															<div class="title">
																<h3><fmt:message key="index.hotCities"></fmt:message></h3>
																<div class="line"></div>
															</div>
															<div class="con clx" id="hotsite_city_destination">
																<jsp:include page="include/hotsite_cities.jsp" />
															</div>
														</div>
														<div class="item">
															<div class="title">
																<h3><fmt:message key="index.hotCountries"></fmt:message></h3>
																<div class="line"></div>
															</div>
															<div class="con clx" id="hotsite_country_destination">
																<jsp:include page="include/hotsite_countries.jsp" />
															</div>
														</div>
														<div class="hotHotels">
															<div class="title">
																<h3><fmt:message key="index.latestHotel"></fmt:message></h3>
																<div class="line"></div>
															</div>
															<div class="con clx" id="hotsite_hotel_destination">
																<jsp:include page="include/hotsite_hotels.jsp" />
															</div>

														</div>
													</div>
													<div class="itemData" id="destinationCity_domestic">
														<jsp:include page="include/domestic_pc.jsp" />
													</div>
													<div class="itemData" id="destinationContinent_Asia">
														<jsp:include page="include/destination_countries_list.jsp" >
															<jsp:param value="Asia" name="continentName"/>
														</jsp:include>
													</div>
													<div class="itemData">
														<div class="item">
															<div class="con clx" id="destinationContinent_Americas">
																<jsp:include page="include/destination_countries_list.jsp" >
																	<jsp:param value="Americas" name="continentName"/>
																</jsp:include>
															</div>
														</div>
													</div>
													<div class="itemData">
														<div class="item">
															<div class="con clx" id="destinationContinent_Europe">
																<jsp:include page="include/destination_countries_list.jsp" >
																	<jsp:param value="Europe" name="continentName"/>
																</jsp:include>
															</div>
														</div>
													</div>
													<div class="itemData">
														<div class="item">
															<div class="con clx" id="destinationContinent_Australasia">
																<jsp:include page="include/destination_countries_list.jsp" >
																	<jsp:param value="Australasia" name="continentName"/>
																</jsp:include>
															</div>
														</div>
													</div>
													<div class="itemData">
														<div class="item">
															<div class="con clx" id="destinationContinent_Africa">
																<jsp:include page="include/destination_countries_list.jsp" >
																	<jsp:param value="Africa" name="continentName"/>
																</jsp:include>
															</div>
														</div>
													</div>
													<div class="itemData"
														id="destinationContinent_other">
														<div class="item">
															<div class="title">
																<h3><fmt:message key="index.IndianOcean"></fmt:message></h3>
																<div class="line"></div>
															</div>
															<div class="con clx" id="destinationContinent_IndianOcean">
																<jsp:include page="include/destination_countries_list_other.jsp" >
																	<jsp:param value="IndianOcean" name="continentName"/>
																</jsp:include>
															</div>
														</div>
														<div class="item">
															<div class="title">
																<h3><fmt:message key="index.Caribbean"></fmt:message></h3>
																<div class="line"></div>
															</div>
															<div class="con clx" id="destinationContinent_Caribbean">
																<jsp:include page="include/destination_countries_list_other.jsp" >
																	<jsp:param value="Caribbean" name="continentName"/>
																</jsp:include>
															</div>
														</div>
														<div class="item">
															<div class="title">
																<h3><fmt:message key="index.MiddleEast"></fmt:message></h3>
																<div class="line"></div>
															</div>
															<div class="con clx" id="destinationContinent_MiddleEast">
																<jsp:include page="include/destination_countries_list_other.jsp" >
																	<jsp:param value="MiddleEast" name="continentName"/>
																</jsp:include>
															</div>
														</div>
													</div>
												</div>
												<!--tabsContent end-->
											</section>
										</div>
										<!--box end-->
									</section>
									<!--destination end-->
									<!-- pc footer start -->
									<%@ include file="/WEB-INF/view/include/footer_pc.jsp"%>
									<!-- pc footer end -->
								</div>
							</div>
							<div class="swiper-scrollbar"></div>
						</div>
					</div>
					<!--item end-->

				</div>
				<!--swiper-wrapper end-->

			</div>
			<!--index_swiper end-->


			<!--floatSidebar end-->
			<a href="javascript:" id="J_nextPCPage" class="nextPCPage">next</a>

		</div>
		<!--pcSite end-->

		<div class="mobileSite">
			<div class="hbox"></div>
			<div class="swiper-container" id="bannerSwiper_mobile">
				<div class="swiper-wrapper">
				<c:forEach var="activity" items="${swiperMobile }" begin="0" end="500">
					<div class="swiper-slide">
					<c:if test="${'' != activity.link}">
						<c:if test="${'external'!=activity.linkType }"><a href="<%=path%>${activity.link}?locale=${locale}" ></c:if>
						<c:if test="${'external'==activity.linkType }"><a href="${activity.link }" target="_blank"></c:if>
							<c:choose>
								<c:when test="${activity.bannerTra != '' && locale == 'zh_TW'}">
									<img src="${activity.bannerTra}" alt="">
								</c:when>
								<c:otherwise>
									<img src="${activity.banner}" alt="">
								</c:otherwise>
							</c:choose>
					    </a>
					</c:if>
					<c:if test="${'' == activity.link}">
						<img src="${activity.banner }" alt="">
					</c:if>
					</div>
				</c:forEach>
				</div>
				<div class="swiper-pagination"></div>
			</div>
			<div class="moble-search">
				<div class="moble-search-inner">
					<p class="label"><fmt:message key="index.destination"></fmt:message></p>
					<div class="search-item search-inpit-box" id="J_inputSearchBox"><fmt:message key="index.placeholder"></fmt:message></div>
					<div class="search-filter after-clear" id="J_mfiltDate">
						<div class="search-item filter-left float-left fter-clear">
							<div class="col-item float-left">
								<p class="label"><fmt:message key="index.checkIn"></fmt:message></p>
								<p class="startTime"></p>
							</div>
							<div class="col-item middle float-left text-center">
								<p><span class="nights"></span><fmt:message key="index.nights"></fmt:message></p>
							</div>
							<div class="col-item float-right">
								<p class="label"><fmt:message key="index.checkOut"></fmt:message></p>
								<p class="endTime"></p>
							</div>
						</div>
						<div class="search-item filter-right float-right">
							<p class="label"><fmt:message key="index.adult"></fmt:message></p>
							<p class="persons"></p>
						</div>
					</div>
					<div><button class="book-btn" id='search_btn_mobile' ><fmt:message key="index.searchHotel"></fmt:message></button></div>
				</div>
			</div>
<!-- 			<div class="mSearch">
				<img class="bg" src="<%=path%>/static/images/demo/m0.jpg" />
				<div class="seacrhBox ver">
					<div class="box">
						<h2>为特立独行的游客寻找与众不同的酒店</h2>
						<div class="inputBox" id="J_inputSearchBox">
							<i class="s vamiddle"></i> <span class="vamiddle">请输入您要入住的国家、城市 或 酒店名称</span>
						</div>
					</div>
				</div>
			</div> -->
			<!--mSearch end-->

			<div class="mContainer">
				<div class="tuijzt">
					<div class="tit">
						<div class="line"></div>
						<h2><fmt:message key="index.recommendedTopics"></fmt:message></h2>
					</div>
					<div class="listBox swiper-container" id="m_J_outletBox" style="max-height:120px">
						<ul class="list swiper-wrapper clx">
						<%@ include
							file="/WEB-INF/view/include/index-experience-list_m.jsp"%>
						</ul>
					</div>
					<!--listBox end-->
<!-- 					<script>
				(function(){
					function listBoxW(){
						var liNum = $('.tuijzt .listBox .list li').length;
						var liWw = $('.tuijzt .listBox .list li').outerWidth() + 16;
						var ulWw = liNum * liWw;
						$('.tuijzt .listBox .list').width(ulWw);
					}
					listBoxW();
					$(window).resize(function() {
						var _WW = $(window).width();
						if(_WW < 768){listBoxW();}
					});

				})()
			</script> -->
				</div>
				<!--tuijzt end-->
				<div class="zuixhd">
					<div class="tit">
						<div class="line"></div>
						<h2><fmt:message key="index.latestActivity"></fmt:message></h2>
					</div>
					<div class="hdSlide swiper-container">
						<a href="javascript:" class="icoLeft" id="m_active_prevbtn"></a>
						<div class="imgbox swiper-wrapper" id="m_J_hdBox">
							<%@ include
								file="/WEB-INF/view/include/index-promotion-list_m.jsp"%>
						</div>
						<a href="javascript:" class="icoRight" id="m_active_nextbtn"></a>
					</div>
					<!--hdSlide end-->
				</div>
				<!--zuixhd end-->
				<div class="weixjx">
					<div class="tit">
						<div class="line"></div>
						<h2><fmt:message key="index.weixinChoiceness"></fmt:message></h2>
					</div>
					<div class="listBox" id="m_masonry">
						<%@ include file="/WEB-INF/view/include/index-weChat-list_m.jsp"%>
					</div>
					<!--hdSlide end-->
				</div>
				<!--weixjx end-->
				<div class="mudd">
					<div class="tit">
						<div class="line"></div>
						<h2><fmt:message key="index.destination"></fmt:message></h2>
					</div>
					<div class="listBox" id="m_J_desBox">
						<ul class="list clx">
							<li><a class="cur" href="javascript:" role="hot"><fmt:message key="index.hot"></fmt:message></a></li>
							<li><a href="javascript:" role="china"><fmt:message key="index.domestic"></fmt:message></a></li>
							<li><a href="javascript:" role="asia"><fmt:message key="index.Asian"></fmt:message></a></li>
							<li><a href="javascript:" role="america"><fmt:message key="index.America"></fmt:message></a></li>
							<li><a href="javascript:" role="europe"><fmt:message key="index.Europe"></fmt:message></a></li>
							<li><a href="javascript:" role="oceania"><fmt:message key="index.Oceania"></fmt:message></a></li>
							<li><a href="javascript:" role="africa"><fmt:message key="index.Africa"></fmt:message></a></li>
							<li><a href="javascript:" role="other"><fmt:message key="index.other"></fmt:message></a></li>
						</ul>
					</div>
					<!--hdSlide end-->
				</div>
				<!--mudd end-->
			</div>
			<!--mContainer end-->

		</div>
		<!--wrapper end-->

		<!-- mobile footer start -->
		<%@ include file="/WEB-INF/view/include/footer_m.jsp"%>
		<!-- mobile footer end -->

		<!-- right sidebar start -->
		<%@ include file="/WEB-INF/view/include/right-sidebar.jsp"%>
		<!-- right sidebar start -->

		<div id="test_div"></div>
		
		<form id="searchResultFrom" method="post" target="_self" style="display:none;">
			<input id="cityLink" name="cityLink" value=""/>
			<input id="checkinDate" name="checkinDate" value=""/>
			<input id="checkoutDate" name="checkoutDate" value=""/>
			<input id="nights" name="nights" value=""/>
			<input id="adultNumber" name="adultNumber" value=""/>
			<input id="roomNumber" name="roomNumber" value=""/>
		</form>
		
		<script>
		var locale = "${locale}";
		var path = "<%=path%>";
		var templatePath = "<%=path%>/template";
		var checkinDate = "${checkinDate}";
		var checkoutDate = "${checkoutDate}";
		var nights = "${nights}";
		
		//缓存全局所有国家、城市、酒店名称信息，供搜索框查询使用
		var queryCache = ${queryCache};
		//console.log(queryCache);
		</script>

		<!-- 页面公用方法 -->
		<script src="<%=path%>/static/js/jquery.masonry.min.js"></script>
		<script src='<%=path1%>/static/js/new_common.js?v=20180427'></script>
		<script src='<%=path2%>/static/js/index.js?20220804'></script>

		<%-- <script src="<%=path4%>/static/js/handlebars-v4.0.5.js"></script> --%>
		<%-- <script src="<%=path%>/static/js/nodetpl.js"></script> --%>
		<%-- <script src="<%=path1%>/static/js/fetch-jsonp.js"></script> --%>
		<script src='<%=path3%>/static/slh/config.js?v=20180426'></script>
		<script src='<%=path2%>/static/slh/common.js?v=20220804'></script>
		<%-- <script src="<%=path4%>/static/slh/handlebars-helper.js"></script> --%>
		<script src='<%=path%>/static/slh/searchbar.js?20171101'></script>
		<%-- <script src='<%=path1%>/static/slh/index-data.js'></script> --%>
		<%-- <script src='<%=path2%>/static/slh/experience.js'></script> --%>
		<script src='<%=path3%>/static/slh/destination.js?v=20170904'></script> 
		<%-- <script src='<%=path4%>/static/slh/activity.js'></script> --%>
		<%-- <script src='<%=path%>/static/slh/hotsite.js'></script> --%>
		
<script>
//增加微信地区链接
function getQueryString(name) {
    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    var r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
}
if($(window).width()<1024){
  var areaName = getQueryString('areaName');
  $("#m_J_desBox a[role='"+areaName+"']").trigger('click');
}

function submitQueryForm(){
	//alert("search: "+$("#matchName").val());
	ga('send', 'pageview', '/search_results?searchwords='+$("#matchName").val());
}

var deviceModel = getDevice();
$(window).resize(function() {
	console.log(getDevice());
	if(deviceModel!=getDevice()){
		deviceModel = getDevice();
		console.log("initHotelsData()");
		initIndexData();
	}
});

$(function(){
	initIndexData();
});

function initIndexData(){
	var ie = !!window.ActiveXObject;
	//初始化数据
	if(!_static){
		var browser = getBrowser();

		Home.SetHotelInfo(checkinDate,checkoutDate,nights,2);
		
		if("PC"==getDevice()){
			if(ie){
				document.getElementById('search-hotel').onpropertychange = fuzzyQuery;
			}else{
				document.getElementById('search-hotel').addEventListener("input",fuzzyQuery,false); 
			}
		}else{
			if(ie){
				document.getElementById('m_search-hotel').onpropertychange = fuzzyQuery;
			}else{
				document.getElementById('m_search-hotel').addEventListener("input",fuzzyQuery,false); 
			}
		}
	}
}

</script>
</body>
</html>