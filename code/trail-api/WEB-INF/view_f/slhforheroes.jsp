<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/view/include/include-top.jsp"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<%@ include file="/WEB-INF/view/include/head.jsp"%>
</head>
<body>

	<!-- pc header start -->
	<%@ include file="/WEB-INF/view/include/header_pc.jsp"%>
	<!-- pc header end -->

	<!-- mobile nav start -->
	<%@ include file="/WEB-INF/view/include/mobile-nav.jsp"%>
	<!-- mobile nav end -->

	<!-- mobile search start -->
	<%@ include file="/WEB-INF/view/include/mobile-search.jsp"%>
	<!-- mobile search end -->

	<!--全部城市-->
	<%@ include file="/WEB-INF/view/include/allCities-m.jsp"%>

	<!-- page wrapper start -->
	<div class="wrapper">
		<style>
			.__banner{position: relative;}
			.__banner .mobile_img{display: none;}
			.__banner .pc_img{display: block;}
			.__banner img{width: 100%;}
			.__link_btn{background-color: #f4cc2c; font-size:16px; padding:0.5em 2em; color: #282828; margin-top:0.5em; display:inline-block; line-height:1.5; font-weight:bold;}
			.__banner .link_btn{display:block; position: absolute; bottom:30%; left:50%; transform:translate(-50%,0);}
			.page-details *+p{margin-top:1em;}
			.page-details{font-size:20px;}
			.__details__col{align-items:center; font-size:20px; line-height:2; position: relative;}
			.__details__col .__text_area{position: absolute; width:50%; top:50%; transform:translate(0,-50%);}
			.__details__col span{display:block; box-sizing:border-box;}
			.__details__col.__text__left{padding-left:50%;}
			.__details__col.__text__left .__text_area{left:0; padding-right:60px;}
			.__details__col.__text__right{padding-right:50%;}
			.__details__col.__text__right .__text_area{right:0; padding-left:60px;}
			@media (max-width: 768px){
				.__banner .link_btn{bottom:20%;}
				.__banner .mobile_img{display: block;}
				.__banner .pc_img{display: none;}
				.page-details{font-size:18px;}
				.__details__col{flex-direction:column; font-size:16px; line-height:1,75;}
				.__details__col .__text_area{position: static; width:100%; top:50%; transform:translate(0,0);}
				.__details__col span{display:block; box-sizing:border-box;}
				.__details__col.__text__left{padding-left:0;}
				.__details__col.__text__left .__text_area{padding-bottom:20px; padding-right:0;}
				.__details__col.__text__right{padding-right:0;}
				.__details__col.__text__right .__text_area{padding-bottom:20px; padding-left:0;}
			}
		</style>
		<div class="__banner">
			<div class="img-box">
				<c:choose>
					<c:when test="${locale =='zh_TW' }">
						<img class="mobile_img" src="<%=path%>/static/images/hero/SLH_TW_MOBILE_MED.jpg" alt="">
						<img class="pc_img" src="<%=path%>/static/images/hero/SLH_TW_DESKTOP_MED.jpg" alt="">
					</c:when>
					<c:otherwise>
						<img class="mobile_img" src="<%=path%>/static/images/hero/SLH_CN_MOBILE_MED.jpg" alt="">
						<img class="pc_img" src="<%=path%>/static/images/hero/SLH_CN_DESKTOP_MED.jpg" alt="">
					</c:otherwise>
				</c:choose>
			</div>
			<a class="link_btn __link_btn" href="" target="_blank" rel="noopener" type="submit" target="_blank" onclick="ga('send', 'event', 'slh for heroes', 'click', '自动生成');">
				<span class="ui-button__content">
					<c:choose>
						<c:when test="${locale =='zh_TW' }">
							開始提名
						</c:when>
						<c:otherwise>
							开始提名
						</c:otherwise>
					</c:choose> &nbsp;»</span>
			</a>
		</div>
		<!-- page content start-->
		<div class="fixed-wrapper page-content">
			<div class="page-title text-center">
				<div class="page-title-inner">
					<span>${activity.title}</span>
				</div>
			</div>
			<!-- page details -->
			<div class="page-details" id="about-us-banner">
				${activity.content}
			</div>
		</div>
		<!-- page content end -->
	</div>
	<!-- page wrapper end -->

	<!-- pc footer start -->
	<%@ include file="/WEB-INF/view/include/footer_pc.jsp"%>
	<!-- pc footer end -->

	<!-- mobile footer start -->
	<%@ include file="/WEB-INF/view/include/footer_m.jsp"%>
	<!-- mobile footer end -->

	<!-- right sidebar start -->
	<%@ include file="/WEB-INF/view/include/right-sidebar.jsp"%>
	<!-- right sidebar start -->

	<script>
		var locale = "${locale}";
		var path = "<%=path%>";
		var templatePath = "<%=path%>/template";
	</script>

	<!-- 页面公用方法 -->
	<script src="<%=path%>/static/js/new_common.js?v=20180427"></script>

	<script src="<%=path%>/static/js/handlebars-v4.0.5.js"></script>
	<script src="<%=path%>/static/js/nodetpl.js"></script>
	<script src="<%=path%>/static/js/fetch-jsonp.js"></script>
	<script src='<%=path%>/static/slh/common.js?v=20180426'></script>
	<script src='<%=path%>/static/slh/config.js?v=20180426'></script>
	<script src="<%=path%>/static/slh/handlebars-helper.js?v=20180328"></script>
	<script src='<%=path%>/static/slh/destination.js?v=20170904'></script>
</body>
</html>