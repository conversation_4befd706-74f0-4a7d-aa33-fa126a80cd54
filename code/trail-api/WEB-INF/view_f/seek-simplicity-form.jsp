<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="/WEB-INF/view/include/include-top.jsp" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <%@ include file="/WEB-INF/view/include/head.jsp" %>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.0-beta2/css/bootstrap.min.css" rel="stylesheet">
    <!-- Latest compiled and minified JavaScript -->
    <link href="<%=path%>/static/css/fileup.min.css" rel="stylesheet">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/1.10.0/jquery.min.js"></script>
    <script src="<%=path%>/static/js/fileup.min.js"></script>
</head>
<body>

<!-- pc header start -->
<%@ include file="/WEB-INF/view/include/header_pc.jsp" %>
<!-- pc header end -->

<!-- mobile nav start -->
<%@ include file="/WEB-INF/view/include/mobile-nav.jsp" %>
<!-- mobile nav end -->

<!--全部城市-->
<%@ include file="/WEB-INF/view/include/allCities-m.jsp" %>

<!-- page wrapper start -->
<div class="wrapper">
    <div class="" style="background-position:bottom;background-image:url('<%=path%>/static/images/seek_simplicity/INVITED-Teaser-XL.jpg');height: 350px;">
    </div><!-- social-media start -->
    <div class="fixed-wrapper page-content social-media">
        <div class="page-title text-center">
            <div class="page-title-inner">
                <span><fmt:message key="seek_simplicity.title"></fmt:message></span>
            </div>
        </div>
        <div class="mediaInForm">
            <div class="formBox">
                <div id="vueCheck">
                    <h3 class="from-title">
                        <span class="input-tip">*</span><fmt:message key="seek_simplicity.name"></fmt:message>
                    </h3>
                    <div class="item">
                        <input class="txtCss"
                               id="name" v-model='name' value="" maxlength="20"
                               placeholder="<fmt:message key="seek_simplicity.name"></fmt:message>"/>
                    </div>
                    <h3 class="from-title">
                        <span class="input-tip">*</span><fmt:message key="seek_simplicity.email"></fmt:message>
                    </h3>
                    <div class="item">
                        <input class="txtCss" id="email" maxlength="50"
                               v-model='email' value=""
                               placeholder="<fmt:message key="seek_simplicity.email"></fmt:message>"/>
                    </div>
                    <h3 class="from-title">
                        <span class="input-tip">*</span><fmt:message key="seek_simplicity.wechatId"></fmt:message>
                    </h3>
                    <div class="item">
                        <input class="txtCss" id="wechat_id" v-model='wechat_id' value="" maxlength="20"
                               placeholder="<fmt:message key="seek_simplicity.wechatId"></fmt:message>"/>
                    </div>
                    <h3 class="from-title">
                        <span class="input-tip">*</span><fmt:message key="seek_simplicity.pleasure"></fmt:message>
                    </h3>
                    <div class="item">
							<textarea class="textCss" id="pleasure" cols=40 v-model='pleasure'
                                      rows=4 maxlength="500"
                                      placeholder="<fmt:message key="seek_simplicity.pleasure-info"></fmt:message>"></textarea>
                    </div>
                </div>
                <input type="hidden" id="uploadId" name="uploadImages" value=""/>
                <div class="content">

                    <form id="from" enctype="multipart/form-data">
                        <div id="uploadImagesQueue"></div>
                        <div class="btn btn-success fileup-btn">
                            上传图片
                            <input type="file" id="uploadImages" name="filedata" value="" multiple accept="image/*"/>
                        </div>
                    </form>
                        <%--<form id="fileUpForm" method="post" enctype="multipart/form-data">
                            <!--multiple="multiple"实现选择多个文件-->
                            <input type="file" name="img"  multiple="multiple">
                            <input type="button" onclick="fileUp()" value="上传">
                        </form>
                        <div id="img"></div>--%>
                </div>
                <div class="box">
                    <h3 class="from-title">
                        <span class="input-tip">*</span><fmt:message key="seek_simplicity.share"></fmt:message>
                    </h3>
                    <ul class="item after-clear">
                        <li style="display: none;"><input
                                name="is_share" type="hidden" value=""
                                id="is_share"/></li>
                        <li class="item-w25 radiobox" data-value="1"><span></span>是</li>
                        <li class="item-w25 radiobox" data-value="0"><span></span>否</li>
                        <li class="clear"></li>
                    </ul>
                    <!--list end-->
                </div>
                <div class="item text-center">
                    <button type="submit" id="submit" class="btn btn-green"><fmt:message
                            key="media_relations.submit"></fmt:message></button>
                    <div style="padding-top:10px;font-size: 14px;">提交即代表你已接受<a href="<%=path%>/seek-simplicity-form-rules"><font style="color: #ba954f;">《活动条件与条款》</font></a></div>
                    <div id="submit_loading" class="inline-loading">
                        <img src="<%=path%>/static/images/loading.gif" alt="">
                    </div>
                </div>
            </div>
        </div>
        <!--formBox end-->
    </div>
    <!--mediaInForm end-->
</div>
<!-- social-media end -->
</div>
<!-- page wrapper end -->

<!-- pc footer start -->
<%@ include file="/WEB-INF/view/include/footer_pc.jsp" %>
<!-- pc footer end -->

<!-- mobile footer start -->
<%@ include file="/WEB-INF/view/include/footer_m.jsp" %>
<!-- mobile footer end -->

<!-- right sidebar start -->
<%@ include file="/WEB-INF/view/include/right-sidebar.jsp" %>

<!-- right sidebar start -->
<script src="https://cdn.bootcdn.net/ajax/libs/jquery.form/4.3.0/jquery.form.min.js"></script>
<script src="http://wechatfe.github.io/vconsole/lib/vconsole.min.js?v=3.2.0"></script>
<script>
    var locale = "${locale}";
    var path = "<%=path%>";
    var templatePath = "<%=path%>/template";
    var type = "${type}";
/*    $("#upload_btn").click(function(){
        var formData = new FormData($("#from")[0]);/!*传多个文件 $("#from1")[0]*!/
        //var formData = new FormData();
        //formData.append("file",$("#upfile")[0].files[0]); /!*传单个文件 *!/
        $.ajax({
            url: '<%=path%>/seek-simplicity-form/upload',
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            success: function (data) {
                alert("上传成功！"+data);
                //console.log(data);
            },
            error: function () {
                alert("上传失败！");
            }
        });
    });*/

/*    function fileUp() {
        $('#fileUpForm').attr('action', '<%=path%>/seek-simplicity-form/upload');
        /!*ajaxSubmit是jquery.form.js封装好的方法*!/
        $("#fileUpForm").ajaxSubmit({
            type:"post",
            url:"${pageContext.request.contextPath}/seek-simplicity-form/upload",
            success:function (data) {
                $.each(data,function (index, item) {
                    $("#img").append('<img src=static/upload/'+item+' width="100px"/>');
                })
            },
            error:function (xhr, msg) {
                alert(msg);
            }
        })
    }*/
    $.fileup({
        url: '<%=path%>/seek-simplicity-form/upload',
        inputID: 'uploadImages',
        queueID: 'uploadImagesQueue',
        fieldName: "img",
        filesLimit: 3,
        sizeLimit:10485760,
        autostart: true,
        onBeforeStart: function (file_number, xhr, file) {
            console.log('onBeforeStart', file);
        },
        onSuccess: function (response, file_number, file) {
            console.log('onSuccess', response);
            var imgurl = $('#uploadId').val();
            $('#uploadId').val(imgurl == "" ? response : (imgurl + ',' + response));
            //$('#from').attr('action', '<%=path%>/seek-simplicity-form/upload');
            // Snarl.addNotification({
            //     title: 'Upload success',
            //     text: file.name,
            //     icon: '<i class="fa fa-check"></i>'
            // });
        },
        onError: function (event, file, file_number) {
            console.log(file);
            console.log(file_number);
        }
    });

</script>

<!-- 页面公用方法 -->
<script src="<%=path%>/static/js/new_common.js?v=20180427"></script>
<script src="<%=path%>/static/js/vue.js"></script>

<script src='<%=path%>/static/slh/config.js?v=20180426'></script>
<script src="<%=path%>/static/js/handlebars-v4.0.5.js"></script>
<script src="<%=path%>/static/slh/handlebars-helper.js?v=20180328"></script>
<script src="<%=path%>/static/js/nodetpl.js"></script>
<script src="<%=path%>/static/js/fetch-jsonp.js"></script>
<script src='<%=path%>/static/slh/common.js?v=20180426'></script>
<script src='<%=path%>/static/slh/config.js?v=20180426'></script>
<script src='<%=path%>/static/slh/destination.js?v=20170904'></script>
<script src="<%=path%>/static/js/seek_simplicity.js?v=20170904"></script>
</body>
</html>
