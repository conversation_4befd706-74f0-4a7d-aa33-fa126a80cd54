<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/view/include/include-top.jsp"%>
<meta charset="UTF-8" />
<meta name="author" content="zepo" />
<title >${title }</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta id ="keywords" name="keywords" content="${keywords }">
<meta id ="description" name="description" content="${description }">
<link rel="shortcut icon" href="<%=path%>/static/images/favicon.ico"/>

<link rel="stylesheet" href="http://127.0.0.1:5173/src/css/global.scss">
<!-- <link rel="stylesheet" href="<%=path%>/build/assets/global-ca5e62f5.css"> -->
<link rel="stylesheet" href="//at.alicdn.com/t/c/font_1371805_lo9hmhphiwa.css">
<script src='<%=path%>/static/slh/baidu_google.js'></script>

<!-- Global site tag (gtag.js) - Google Analytics -->
<c:if test="${locale=='zh_TW'}">
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-J3LLN8L1HW"></script>
</c:if>
<c:if test="${locale !='zh_TW'}">
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-8PF1VSZSHD"></script>
</c:if>
<!-- Google Tag Manager -->
<script>
var locale = "${locale}";
if( locale == 'zh_TW'){
	window.dataLayer = window.dataLayer || [];
	function gtag(){dataLayer.push(arguments);}
	gtag('js', new Date());
    gtag('config', 'G-J3LLN8L1HW');
	//百度监测
	var _hmt = _hmt || [];
	(function() {
	  var hm = document.createElement("script");
	  hm.src = "https://hm.baidu.com/hm.js?a92b18f963549db3d1bc39b4604fdd4b";
	  var s = document.getElementsByTagName("script")[0]; 
	  s.parentNode.insertBefore(hm, s);
	})();
    // <!-- Google Tag Manager -->
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-KPS3RGG');
    // <!-- End Google Tag Manager -->
}else{
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-8PF1VSZSHD');
    // <!-- Google Tag Manager -->
   (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-PSCMHFL');
    // <!-- End Google Tag Manager -->  
}
</script>
<!-- End Google Tag Manager -->