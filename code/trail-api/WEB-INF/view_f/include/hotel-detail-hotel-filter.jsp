<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<div class="content-item hotelFilter" id="bg-none">
	<div id="hotel-detail-fixed">
            <div class="filter-header filter-header-date after-clear" id="hotel-detail-header">
              <!--<div class="float-left dateTip" id="hotel-detail-time">入住/离店时间</div>-->
                            <div class="hotel-detail-bor">
              <div class="float-left dataSelect" id="J_mfiltDate" data-forId="J_pickerDate">
                <i class="icon icon-city float-left" id="icon-time"></i>
                <div class="city float-left" id="J_box_city">
                    <span class="time" id="time"><b class="startTime" id="startTime"></b> - <b class="endTime" id="endTime"></b></span>
                    <div style="display:none;">
                     <span class="night" id="night"><b class="nights" id="pc_nights"></b><fmt:message key="hotel_detail_hotel_filter.night"></fmt:message></span>
                        <span class="person" id="person"><b class="persons" id="pc_persons"></b><fmt:message key="hotel_detail_hotel_filter.adult"></fmt:message></span>
                	</div>
                </div>
                <i class="icon icon-up float-right" id=""></i>
                <!-- <div class="info-right float-left" id="hotel-detail-right-wap">
                      <a href="javarscript:" class="booking" onclick="" id="hotel-detail-book-wap">立即预订</a> -->
                </div>
              </div>
            </div>
            <!-- <div class="info-right float-left" id="hotel-detail-right">
                  <a href="javarscript:" class="booking" onclick="javascript:booking('','');" id="hotel-detail-book">立即预订</a> -->
            </div>
            </div>
                  </div>
                </div>
              </div>
            </li>
            <div id="hotel-detail-posi" class="hotel-detail-hover">
            <div class="fixed-wrapper" id="fixed-wrapper">
            <div class="detail-content" id="detail-content">
                  <div class="content-item hotelFilter" id="bg-none">
                <div id="hotel-detail-fixed">
                    <div class="filter-body dateSelect after-clear" id="filter-body">
                        <div class="dateBox" id="dateBox">
                        <input type="hidden" id="date-range">
                    <div class="timePlugin" id="date-range-container"></div>
                </div>
                <div class="rightBox clx" id="rightBox">
                    <div class="personBox" id="J_personBox">
                            <div class="itemBox">
                                <div class="person box clx">
                                    <span>成人</span>
                                    <select class="inputBox" id="persons">
                                      <option>1</option>
                                      <option>2</option>
                                      <option>3</option>
                                      <option>4</option>
                                      <option>5</option>
                                      <option>6</option>
                                    </select>
                                </div><!--person end-->
                                <div class="clear"></div>
                                <p style="font-size:12px; color:red; padding-top:0.5em; text-align:right;">*<fmt:message key="hotel_detail_hotel_filter.quantityPerRoom"></fmt:message></p>
                                <div class="room box clx" style="display:none;">
                                    <span><fmt:message key="hotel_detail_hotel_filter.roomQuantity"></fmt:message></span>
                                    <select class="inputBox" id="rooms">
                                      <option>1</option>
                                      <option>2</option>
                                      <option>3</option>
                                      <option>4</option>
                                      <option>5</option>
                                      <option>6</option>
                                    </select>
                                </div><!--person end-->
                            </div>  
                            </div>
                          <div class="btnBox">
                            <a href="javascript:" class="btn booking hotel-close" id="J_findBtn" onclick="javascript:booking('','');" id="hotel-detail-book-wap"><fmt:message key="hotel_detail_hotel_filter.bookNow"></fmt:message></a>
                        </div>
                        <!--personBox end-->
                    <!-- <div class="btnBox">
                        <a href="javascript:" class="btn" id="J_findBtn">查询</a>
                    </div> -->
                </div>
            </div>
        </div>
	</div>