<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<div id="hotel_list_cities_pc">
<div class="city-filter clx" >
	<c:if test="${countries!= null && fn:length(countries) > 1 }">
		<c:if test="${type == 'country' }">
		<a style="cursor:pointer;" class="btn-contry" onclick="javascript:selectCity('-1','<fmt:message key="hotel_list_cities_pc.selectDestination"></fmt:message>')" data-contry="#" ><fmt:message key="hotel_list_cities_pc.all"></fmt:message></a>
		</c:if>
		<c:forEach var="country" items="${countries }">
		<button class="btn-contry" data-contry="${country.countryCode }" id="country_${country.countryLink }" onclick="javascript:selectCountry('${country.countryLink }','${country.countryName }',event)">
			<span class="lebale"><fmt:message key="hotel_list_cities_pc.country"></fmt:message>：</span>${country.countryName } <span class="close-tip" onclick="javascript:selectCity('-1','选择目的地','${country.countryLink }',event)"></span>
		</button>
		</c:forEach>
	</c:if>
</div>
<div class="cities after-clear" id="pc_hotel_city" <c:if test="${countries!= null && fn:length(countries) <= 1 }"> style="display:block"</c:if>>
	<a style="cursor:pointer;" onclick="javascript:selectCity('-1','<fmt:message key="hotel_list_cities_pc.selectDestination"></fmt:message>')" data-contry="#" <c:if test="${countries!= null && fn:length(countries) <= 1 }"> style="display:block"</c:if>><fmt:message key="hotel_list_cities_pc.selectDestination"></fmt:message></a>
	<c:forEach var="city" items="${cities }">
	<a style="cursor:pointer;" onclick="javascript:selectCity('${city.cityLink }','${city.cityName }')"
	<c:if test="${cityLink!=null&&cityLink!=''&&cityLink==city.cityLink }"> class="cur" </c:if>
	id="city_${city.cityLink }" data-contry="${city.countryCode }" <c:if test="${countries!= null && fn:length(countries) <= 1 }"> style="display:block"</c:if>>${city.cityName }</a>
	</c:forEach>
</div>
</div>
