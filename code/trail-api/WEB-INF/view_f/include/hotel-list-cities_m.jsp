<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<section class='content-area theme-hotels' id="J_AllThemeHotels">
    <div class="area-hotel-inner">
        <ul class="mobile-city-filter item area-list after-clear" id="m_hotel_city">
        	<c:if test="${type=='country' }">
	        	<li>
	        	<div class="link-box float-left">
	        		<a style="cursor:pointer;" 
	        			<c:choose>
			                  <c:when test="${locale == 'zh_TW'}">
			                      onclick="javascript:selectCity('-1','選擇目的地')" 
			                  </c:when>
			                  <c:otherwise>
			                      onclick="javascript:selectCity('-1','选择目的地')" 
			                  </c:otherwise>
			              </c:choose>
	        		id="city_"><fmt:message key="hotel_list_cities_m.selectDestination"></fmt:message></a>
	        	</div>
	        	</li>
	        	<c:forEach var="cityGroup" items="${cityGroups }">
	            <li>
	                <h3>${cityGroup.groupName}</h3>
	                <c:forEach var="city" items="${cityGroup.cities}">
	                <div class="link-box float-left">
	                <a style="cursor:pointer;" onclick="javascript:selectCity('${city.cityLink }','${city.cityName }')" 
	                <c:if test="${cityLink!=null&&cityLink!=''&&cityLink==city.cityLink }"> class="cur" </c:if>
	                id="m_city_${city.cityLink}">${city.cityName}</a>
	                </div>
	                </c:forEach>
	            </li>
				</c:forEach>
			</c:if>
			<c:if test="${type!='country' }">
			<c:forEach var="countryGroup" items="${countryGroups }">
			<li>
                <h3>${countryGroup.groupName}</h3>
                <c:forEach var="country" items="${countryGroup.countries}">
                <div class="btn-box float-left">
                  <button class="btn-contry" data-contry="${country.countryCode}"><span class="lebale"><fmt:message key="hotel_list_cities_m.country"></fmt:message>: </span><span>${country.countryName}</span><span class="close-tip"></span></button>
                </div>
                </c:forEach>
            </li>
            </c:forEach>
        </ul>
        <div class="cities item area-list after-clear" id="m_hotel_city">
        	<c:forEach var="cityGroup" items="${cityGroups }">
	        	<c:forEach var="city" items="${cityGroup.cities}">
	            <div class="link-box float-left" data-contry="${city.countryCode}">
	            <a style="cursor:pointer;" onclick="javascript:selectCity('${city.cityLink }','${city.cityName }')" 
                <c:if test="${cityLink!=null&&cityLink!=''&&cityLink==city.cityLink }"> class="cur" </c:if>
                id="m_city_${city.cityLink}">${city.cityName}</a>
                </div>
	            </c:forEach>
            </c:forEach>
			</c:if>
        </div>
    </div>
</section>
