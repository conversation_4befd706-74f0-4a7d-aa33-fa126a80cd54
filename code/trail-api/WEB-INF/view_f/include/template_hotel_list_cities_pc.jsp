<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<script id="template_hotel_list_cities_pc" type="text/template">
<div class="city-filter clx" >
	<?if(@data.countries.length > 1){?>
		<?if(@data.type == 'country' ){?>
		<a style="cursor:pointer;" class="btn-contry" onclick="javascript:selectCity('-1','选择目的地')" data-contry="#" ><fmt:message key="template_hotel_list_cities_pc.all"></fmt:message></a>
		<?}?>
		<?for(var i=0; i<@data.countries.length;i++){?>
		<button class="btn-contry" data-contry="<?=@data.countries[i].countryCode ?>" id="country_<?=@data.countries[i].countryLink ?>" onclick="javascript:selectCountry('<?=@data.countries[i].countryLink ?>','<?=@data.countries[i].countryName ?>')">
			<span class="lebale"><fmt:message key="template_hotel_list_cities_pc.country"></fmt:message>：</span><?=@data.countries[i].countryName ?> <span class="close-tip" onclick="javascript:selectCity('-1','选择目的地','<?=@data.countries[i].countryLink ?>',event)"></span>
		</button>
		<?}?>
	<?}?>
</div>
<div class="cities after-clear" id="pc_hotel_city" <?if(@data.countries.length == 1){?> style="display:block"<?}?>>
	<a style="cursor:pointer;" onclick="javascript:selectCity('-','选择目的地')" data-contry="#" <?if(@data.countries.length == 1){?> style="display:block" <?}?>><fmt:message key="template_hotel_list_cities_pc.selectDestination"></fmt:message></a>
	<?for(var i=0;i<@data.cities.length;i++){?>
	<a style="cursor:pointer;" onclick="javascript:selectCity('<?=@data.cities[i].cityLink?>','<?=@data.cities[i].cityName?>')" 
	<?if(@data.cityLink!=null&&@data.cityLink!=''&&@data.cityLink==data.cities[i].cityLink){?> class="cur" <?}?>
	id="city_<?=@data.cities[i].cityLink?>" data-contry="<?=@data.cities[i].countryCode?>" <?if(@data.countries.length == 1){?> style="display:block" <?}?>><?=@data.cities[i].cityName?></a>
	<?}?>
</div>
</script>