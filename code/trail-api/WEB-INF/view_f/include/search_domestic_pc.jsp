<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="f" uri="/WEB-INF/tld/functions.tld"%>

<div class="item">
	<div class="title">
		<h3><fmt:message key="search_domestic_pc.city"></fmt:message></h3>
		<div class="line"></div>
	</div>
	<div class="con clx">
		<c:forEach items="${domestic.cities }" var="city">
			<a href="#" onclick="SearchResultClick('city','${city.cityName}','${city.cityLink}');">${city.cityName}</a>
		</c:forEach>
		</ul>
	</div>
</div>
<!-- 不需要展示，改成小于0，如果以后需要展示则改成大于0 -->
<c:if test="${fn:length(domestic.hotHotels)<0 }">
<div class="item hotHotels">
	<div class="title">
		<h3><fmt:message key="search_domestic_pc.latestHotel"></fmt:message></h3>
		<div class="line"></div>
	</div>
	<div class="clx">
		<ul class="hList clx">
		<c:forEach items="${domestic.hotHotels }" var="hotSite">
			<li class="clx">
				<span>${hotSite.cityName}</span>
				<c:if test="${hotSite.hotelName !='' }">
				<a href="#" onclick="SearchResultClick('hotel','${hotSite.hotelName}','${hotSite.hotelLink}');" style="width:auto;">${hotSite.hotelName}</a>
				</c:if>
				<c:if test="${hotSite.hotelName =='' && hotSite.hotelAliasEn !='' }">
				<a href="#" onclick="SearchResultClick('hotel','${hotSite.hotelAliasEn}','${hotSite.hotelLink}');" style="width:auto;">${hotSite.hotelAliasEn}</a>
				</c:if>
				<c:if test="${hotSite.hotelName =='' && hotSite.hotelAliasEn =='' }">
				<a href="#" onclick="SearchResultClick('hotel','${hotSite.hotelNameEn}','${hotSite.hotelLink}');" style="width:auto;">${hotSite.hotelNameEn}</a>
				</c:if>
			</li>
		</c:forEach>
		</ul>
	</div>
</div>
</c:if>