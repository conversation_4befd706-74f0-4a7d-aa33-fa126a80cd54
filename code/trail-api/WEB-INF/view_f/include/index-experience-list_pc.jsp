<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<div class="swiper-wrapper">
<c:forEach var="activity" items="${experiences }" varStatus="status">
	<div class="items swiper-slide">
		<div class="itemsBg"  style=" background-image:url(${activity.backgroundImageUrl});"></div>
		<div class="mask ver">
			<div class="box">
				<h3>${activity.title}</h3>
				<div class="line"></div>
				<div class="con">
					<!--<p>{{pageDescription}}</p>-->
				 </div>
				 <div class="btn">
			<c:choose>
				<c:when test="${activity.link != ''}">
					<a class="more" href="<%=path%>${activity.link}?locale=${locale}">
				</c:when>
				<c:otherwise>
					<a class="more" href="<%=path%>/hotel-experience/${activity.activityLink}?locale=${locale}">
				</c:otherwise>
			</c:choose>
						<fmt:message key="index_experience_list_pc.more"></fmt:message>
					</a>
				 </div>
			</div>    
		</div>
	</div>
</c:forEach>
</div>
