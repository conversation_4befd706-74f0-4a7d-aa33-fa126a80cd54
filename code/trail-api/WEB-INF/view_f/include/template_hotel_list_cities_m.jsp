<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<script id="template_hotel_list_cities_m" type="text/template">
	<li>
		<div class="link-box float-left">
		<a style="cursor:pointer;" onclick="javascript:selectCity('-1','选择目的地')" id="city_"><fmt:message key="template_hotel_list_cities_m.selectDestination"></fmt:message></a>
		</div>
	</li>
	<?for(var i=0; i<@data.cityGroups.length;i++){?>
		<li>
			<h3><?=@data.cityGroups[i].groupName?></h3>
			<?for(var j=0; j<@data.cityGroups[i].cities.length){?>
				<div class="link-box float-left">
                <a style="cursor:pointer;" onclick="javascript:selectCity('<?=@data.cityGroups[i].cities[j].cityLink?>','<?=@data.cityGroups[i].cities[j].cityName?>')" 
                <?if(@data.cityLink!=null&&@data.cityLink!=''&&@cityLink==@data.cityGroups[i].cities[j].city.cityLink){?> class="cur" <?}?>
                id="m_city_<?=@data.cityGroups[i].cities[j].cityLink?>">=@data.cityGroups[i].cities[j].cityName?></a>
				</div>
			<?}?>
		</li>
	<?}?>
</script>