<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="f" uri="/WEB-INF/tld/functions.tld"%>

<%
request.setAttribute("continentName", request.getParameter("continentName"));
%>
<c:forEach items="${continents }" var="continent">
<c:if test="${continent.nameEn==continentName }">
<%-- <div class="item">
	<div class="title">
		<h3>${continent.name}</h3>
		<div class="line"></div>
	</div>
	<div class="con clx"> --%>
		<c:forEach items="${continent.countries }" var="country">
			<a href="country/${country.countryLink}?locale=${locale}">${country.countryName}</a>
		</c:forEach>
	<!-- </div>
</div> -->
</c:if>
</c:forEach>