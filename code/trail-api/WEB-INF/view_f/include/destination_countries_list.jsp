<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="f" uri="/WEB-INF/tld/functions.tld"%>

<%
request.setAttribute("continentName", request.getParameter("continentName"));
%>
<c:forEach items="${continents }" var="continent">
<c:if test="${continent.nameEn==continentName }">
<c:if test="${fn:length(continent.hotCities)>0}"> 
<div class="item">
	<div class="title">
		<h3><fmt:message key="destination_countries_list.hotCities"></fmt:message></h3>
		<div class="line"></div>
	</div>
	<div class="con clx">
		<c:forEach items="${continent.hotCities }" var="city">
		<a href="#" onclick="toCity('${city.countryLink}','${city.cityLink}')">${city.cityName}</a>
		</c:forEach>
	</div>
</div>
</c:if>
<div class="item">
	<div class="title">
		<h3><fmt:message key="destination_countries_list.country"></fmt:message></h3>
		<div class="line"></div>
	</div>
	<div class="con clx">
		<c:forEach items="${continent.countries }" var="country">
			<a href="country/${country.countryLink}?locale=${locale}">${country.countryName}</a>
		</c:forEach>
	</div>
</div>
<c:if test="${fn:length(continent.hotHotels)>0}"> 
<div class="item hotHotels">
	<div class="title">
		<h3><fmt:message key="destination_countries_list.topSearchHotel"></fmt:message></h3>
		<div class="line"></div>
	</div>
	<div class="clx" style="padding:20px 0; ">
		<ul class="hList clx">
		<c:forEach items="${continent.hotHotels }" var="hotel">
		<li class="clx">
			<span>${hotel.cityName}</span>
			<a href="hotel/${hotel.hotelLink}?locale=${locale}" style="width:auto;padding-left:0px;">${hotel.hotelName}</a>
		</li>
		</c:forEach>
		</ul>
	</div>
</div>
</c:if>

</c:if>
</c:forEach>