<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="f" uri="/WEB-INF/tld/functions.tld"%>

<%
request.setAttribute("continentName", request.getParameter("continentName"));
%>
<c:forEach items="${continents }" var="continent">
<c:if test="${continent.nameEn==continentName }">
<c:if test="${fn:length(continent.hotCities)>0}"> 
<div class="item">
	<div class="title">
		<h3><fmt:message key="search_countries_list_pc.hotCities"></fmt:message></h3>
		<div class="line"></div>
	</div>
	<div class="con clx">
		<c:forEach items="${continent.hotCities }" var="city">
		<a href="#" onclick="SearchResultClick('city','${city.cityName}','${city.cityLink}');">${city.cityName}</a>
		</c:forEach>
	</div>
</div>
</c:if>
<div class="item">
	<div class="title">
		<h3><fmt:message key="search_countries_list_pc.country"></fmt:message></h3>
		<div class="line"></div>
	</div>
	<div class="con clx">
		<c:forEach items="${continent.countries }" var="country">
			<a href="#" onclick="SearchResultClick('country','${country.countryName}','${country.countryLink}');">${country.countryName}</a>
		</c:forEach>
	</div>
</div>

</c:if>
</c:forEach>