<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<div class="fixed-wrapper hotelBox">
    <div class="titBox clx" style="padding-left: 20px;">
        <h2><div class="box"><span><fmt:message key="hotel_rate_hotel_box.total"></fmt:message><em id="hotels_count">${hotelsCount }</em> <fmt:message key="hotel_rate_hotel_box.text"></fmt:message></span></div></h2>
        <div class="filtBox clx">
            <div class="paixu" id="J_paixu">
                <div class="cur clx">
                    <span><fmt:message key="hotel_rate_hotel_box.order"></fmt:message>：</span>
                    <span class="t"><fmt:message key="hotel_rate_hotel_box.hotelName"></fmt:message></span>
                    <i class="dot"></i>
                </div>
                <ul class="pxList">
                    <li class="active clx" id="orderByNameAsc" onclick="javacsript: orderHotel('nameAsc');">
                        <span><fmt:message key="hotel_rate_hotel_box.hotelName"></fmt:message></span>
                        <i></i>
                    </li>
                    <li class="clx" id="orderByPriceDesc" onclick="javacsript: orderHotel('priceDesc');">
                        <span><fmt:message key="hotel_rate_hotel_box.priceHTL"></fmt:message></span>
                        <i></i>
                    </li>
                    <li class="clx" id="orderByPriceAsc" onclick="javacsript: orderHotel('priceAsc');">
                        <span><fmt:message key="hotel_rate_hotel_box.priceLTH"></fmt:message></span>
                        <i></i>
                    </li>
                </ul>
            </div>
            <div class="pailie clx" id="J_pailie">
                <span><fmt:message key="hotel_rate_hotel_box.arrangementMode"></fmt:message></span>
                <a href="javascript:" class="v cur" id="hotel_arrangement_line"></a>
                <a href="javascript:" class="h" id="hotel_arrangement_grid"></a>
            </div>
        </div><!--filtBox end-->
    </div><!--titBox end-->
    <p class="getPiceInfo">
        <img src="<%=path%>/static/images/loading1.gif" alt="">
        <fmt:message key="hotel_rate_hotel_box.loading"></fmt:message>...
    </p>
    <%@ include file="/WEB-INF/view/include/hotel-rate-list2.jsp"%> 
    
</div>
<!--hotelBox end-->
<!-- <div class="inline-loading inline-loading-show" id="hotel_list_loading">
    <img src="/images/price_loading.gif" alt="">
    <p>正在为您查询符合条件的酒店，请稍后......</p>
</div> -->
<div class="search-ing-container">
    <img src="<%=path%>/static/images/price_loading.gif" alt="">
    <p><fmt:message key="hotel_rate_hotel_box.priceLoading"></fmt:message>......</p>
</div>