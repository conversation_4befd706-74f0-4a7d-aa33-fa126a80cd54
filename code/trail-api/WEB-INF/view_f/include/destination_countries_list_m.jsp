<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="f" uri="/WEB-INF/tld/functions.tld"%>

<%
request.setAttribute("continentName", request.getParameter("continentName"));
%>
<c:forEach items="${continents }" var="continent">
<c:if test="${continent.nameEn==continentName }">
<c:if test="${fn:length(continent.hotCities)>0}"> 
<dl>
	<dt><fmt:message key="destination_countries_list_m.hotCities"></fmt:message></dt>
	<dd>
		<c:forEach items="${continent.hotCities }" var="city">
			<a href="#" onclick="toCity('${city.countryLink}','${city.cityLink}')">${city.cityName}</a>
		</c:forEach>
	</dd>
</dl>
</c:if>
<dl>
<dt style="float: left; width: 100%;"><fmt:message key="destination_countries_list_m.country"></fmt:message></dt>
	<dd>
		<c:forEach items="${continent.countries }" var="country">
			<a href="country/${country.countryLink}?locale=${locale}">${country.countryName}</a>
		</c:forEach>
	</dd>
</dl>

</c:if>
</c:forEach>