<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<div class="swiper-container">
      <div class="swiper-wrapper">
      	<c:forEach var="activity" items="${promotions }" begin="0" end="5">
            <div class="swiper-slide">
            	<c:if test="${'external'!=activity.linkType }">
            		<a href="<%=path %>/offers/${activity.activityLink}?locale=${locale}" >
            			<c:choose>
                    		<c:when test="${activity.bannerTra != '' && locale == 'zh_TW'}">
                        		<img src="${activity.bannerTra }" />
                    		</c:when>
                    		<c:otherwise>
                        		<img src="${activity.banner }" />
                    		</c:otherwise>
                		</c:choose>
            		</a>
            	</c:if>
            	<c:if test="${'external'==activity.linkType }"><a href="${activity.link}" target="_blank"><img src="${activity.banner }" /></a></c:if>
            </div>
        </c:forEach>
      </div>
    </div>
    <ul class="hdTitList">
    	<c:forEach var="activity" items="${promotions }" begin="0" end="5" varStatus="status">
            <li class='hdTitList-item <c:if test="{status.index==0}">cur</c:if>'>
                <h3>${activity.title }</h3>
            </li>
        </c:forEach>
    </ul><!--hdTitList end-->