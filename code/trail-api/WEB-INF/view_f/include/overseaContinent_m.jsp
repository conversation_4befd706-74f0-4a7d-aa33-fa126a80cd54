<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
      <div class="item show after-clear" id="m_overseaContinent_Europe">
	      <c:if test="${fn:length(continent_Europe.hotCities)>0 }"> 
			<dl>
				<dt>热门城市</dt>
				<dd>
					<c:forEach var="city" items="${continent_Europe.hotCities}">
						<a href="#" onclick="toCity('${city.countryLink}','${city.cityLink}')">${city.cityName}</a>
					</c:forEach>
				</dd>
			</dl>
			</c:if>
			<dl>
			<dt style="float: left; width: 100%;">国家</dt>
				<dd>
					<c:forEach var="country" items="${continent_Europe.countries}">
						<a href="<%=path%>/country/${country.countryLink}">${country.countryName}</a>
					</c:forEach>
				</dd>
			</dl>
    </div>
    
    <div class="item after-clear" id="m_overseaContinent_Asia">
	      <c:if test="${fn:length(continent_Asia.hotCities)>0 }"> 
			<dl>
				<dt>热门城市</dt>
				<dd>
					<c:forEach var="city" items="${continent_Asia.hotCities}">
						<a href="#" onclick="toCity('${city.countryLink}','${city.cityLink}')">${city.cityName}</a>
					</c:forEach>
				</dd>
			</dl>
			</c:if>
			<dl>
			<dt style="float: left; width: 100%;">国家</dt>
				<dd>
					<c:forEach var="country" items="${continent_Asia.countries}">
						<a href="<%=path%>/country/${country.countryLink}">${country.countryName}</a>
					</c:forEach>
				</dd>
			</dl>
    </div>
    
    <div class="item after-clear" id="m_overseaContinent_IndianOcean">
	      <c:if test="${fn:length(continent_IndianOcean.hotCities)>0 }"> 
			<dl>
				<dt>热门城市</dt>
				<dd>
					<c:forEach var="city" items="${continent_IndianOcean.hotCities}">
						<a href="#" onclick="toCity('${city.countryLink}','${city.cityLink}')">${city.cityName}</a>
					</c:forEach>
				</dd>
			</dl>
			</c:if>
			<dl>
			<dt style="float: left; width: 100%;">国家</dt>
				<dd>
					<c:forEach var="country" items="${continent_IndianOcean.countries}">
						<a href="<%=path%>/country/${country.countryLink}">${country.countryName}</a>
					</c:forEach>
				</dd>
			</dl>
    </div>
    
    <div class="item after-clear" id="m_overseaContinent_Australasia">
	      <c:if test="${fn:length(continent_Australasia.hotCities)>0 }"> 
			<dl>
				<dt>热门城市</dt>
				<dd>
					<c:forEach var="city" items="${continent_Australasia.hotCities}">
						<a href="#" onclick="toCity('${city.countryLink}','${city.cityLink}')">${city.cityName}</a>
					</c:forEach>
				</dd>
			</dl>
			</c:if>
			<dl>
			<dt style="float: left; width: 100%;">国家</dt>
				<dd>
					<c:forEach var="country" items="${continent_Australasia.countries}">
						<a href="<%=path%>/country/${country.countryLink}">${country.countryName}</a>
					</c:forEach>
				</dd>
			</dl>
    </div>
    
    <div class="item after-clear" id="m_overseaContinent_Americas">
	      <c:if test="${fn:length(continent_Americas.hotCities)>0 }"> 
			<dl>
				<dt>热门城市</dt>
				<dd>
					<c:forEach var="city" items="${continent_Americas.hotCities}">
						<a href="#" onclick="toCity('${city.countryLink}','${city.cityLink}')">${city.cityName}</a>
					</c:forEach>
				</dd>
			</dl>
			</c:if>
			<dl>
			<dt style="float: left; width: 100%;">国家</dt>
				<dd>
					<c:forEach var="country" items="${continent_Americas.countries}">
						<a href="<%=path%>/country/${country.countryLink}">${country.countryName}</a>
					</c:forEach>
				</dd>
			</dl>
    </div>
    
    <div class="item after-clear" id="m_overseaContinent_Caribbean">
	      <c:if test="${fn:length(continent_Caribbean.hotCities)>0 }"> 
			<dl>
				<dt>热门城市</dt>
				<dd>
					<c:forEach var="city" items="${continent_Caribbean.hotCities}">
						<a href="#" onclick="toCity('${city.countryLink}','${city.cityLink}')">${city.cityName}</a>
					</c:forEach>
				</dd>
			</dl>
			</c:if>
			<dl>
			<dt style="float: left; width: 100%;">国家</dt>
				<dd>
					<c:forEach var="country" items="${continent_Caribbean.countries}">
						<a href="<%=path%>/country/${country.countryLink}">${country.countryName}</a>
					</c:forEach>
				</dd>
			</dl>
    </div>
    
    <div class="item after-clear" id="m_overseaContinent_Africa">
	      <c:if test="${fn:length(continent_Africa.hotCities)>0 }"> 
			<dl>
				<dt>热门城市</dt>
				<dd>
					<c:forEach var="city" items="${continent_Africa.hotCities}">
						<a href="#" onclick="toCity('${city.countryLink}','${city.cityLink}')">${city.cityName}</a>
					</c:forEach>
				</dd>
			</dl>
			</c:if>
			<dl>
			<dt style="float: left; width: 100%;">国家</dt>
				<dd>
					<c:forEach var="country" items="${continent_Africa.countries}">
						<a href="<%=path%>/country/${country.countryLink}">${country.countryName}</a>
					</c:forEach>
				</dd>
			</dl>
    </div>
    
      <div class="item after-clear" id="m_overseaContinent_MiddleEast">
			<c:if test="${fn:length(continent_MiddleEast.hotCities)>=0 }"> 
			<dl>
				<dt>热门城市</dt>
				<dd>
					<c:forEach var="city" items="${continent_MiddleEast.hotCities}">
						<a href="#" onclick="toCity('${city.countryLink}','${city.cityLink}')">${city.cityName}</a>
					</c:forEach>
				</dd>
			</dl>
			</c:if>
			<dl>
			<dt style="float: left; width: 100%;">国家</dt>
				<dd>
					<c:forEach var="country" items="${continent_MiddleEast.countries}">
						<a href="<%=path%>/country/${country.countryLink}">${country.countryName}</a>
					</c:forEach>
				</dd>
			</dl>
    </div>