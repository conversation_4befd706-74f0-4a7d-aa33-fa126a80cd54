<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<div class="hotelFilter hotelFilterUnTop after-clear">
	<div class="fixed-wrapper">
        <ul class="filter-nav">
            <li class="filter-header open float-left after-clear" data-forId="J_AllThemeHotels">
                <i class="icon icon-city float-left"></i>
                <div class="city float-left" id="J_box_city"><fmt:message key="hotel_rate_hotel_filter2.selectDestination"></fmt:message></div>
                <i class="icon icon-up float-right"></i>
            </li>
            <li class="filter-header float-left after-clear" data-forId="J_AllThemes">
                <i class="icon icon-hotel float-left"></i>
                <div class="city float-left" id="J_box_activity">
                <c:choose>
                	<c:when test="${activityTitle!=null }">
                	${activityTitle }
                	</c:when>
                	<c:otherwise>
                        <fmt:message key="hotel_rate_hotel_filter2.hotelTypes"></fmt:message>
                	</c:otherwise>
                </c:choose>
                </div>
                <i class="icon icon-up float-right"></i>
            </li>
            <li class="filter-header filter-header-date float-left after-clear" id="J_mfiltDate" data-forId="J_pickerDate">
                <i class="icon icon-date float-left"></i>
                <div class="city float-left" id="J_box_city">
                    <span class="time"><b class="startTime" id="startTime"></b> - <b class="endTime" id="endTime"></b></span>
                        <span class="night"><b class="nights" id="pc_nights"></b><fmt:message key="hotel_rate_hotel_filter2.nights"></fmt:message></span>
                        <span class="person"><b class="persons" id="pc_persons"></b><fmt:message key="hotel_rate_hotel_filter2.adult"></fmt:message></span>
                </div>
                <i class="icon icon-up float-right"></i>
            </li>
        </ul>
        <ul class="filter-content">
            <li class="filter-body show tabCity after-clear">
            	<%@ include file="/WEB-INF/view/include/hotel-list-cities_pc.jsp"%>
            </li>
            <li class="filter-body after-clear" id="experience-list_pc">
            	<%@ include file="/WEB-INF/view/include/experience-list_pc.jsp"%>
            </li>
            <li class="filter-body dateSelect after-clear">
                <div class="dateBox">
                    <input type="hidden" id="date-range">
                    <div class="timePlugin" id="date-range-container"></div>
                </div>
                <div class="rightBox clx">
                    <div class="personBox" id="J_personBox">
                            <div class="itemBox">
                                <div class="person box clx">
                                    <span><fmt:message key="hotel_rate_hotel_filter2.adult"></fmt:message></span>
                                    <select class="inputBox" id="persons">
                                      <option>1</option>
                                      <option>2</option>
                                      <option>3</option>
                                      <option>4</option>
                                      <option>5</option>
                                      <option>6</option>
                                    </select>
                                </div><!--person end-->
                                <div class="clear"></div>
                                <p style="font-size:12px; color:red; padding-top:0.5em; text-align:right;">*<fmt:message key="hotel_rate_hotel_filter2.quantityPerRoom"></fmt:message></p>
                                <div class="room box clx" style="display:none;">
                                    <span><fmt:message key="hotel_rate_hotel_filter2.roomQuantity"></fmt:message></span>
                                    <select class="inputBox" id="rooms">
                                      <option>1</option>
                                      <option>2</option>
                                      <option>3</option>
                                      <option>4</option>
                                      <option>5</option>
                                      <option>6</option>
                                    </select>
                                </div><!--person end-->
                            </div>  
                        </div><!--personBox end-->
                    <div class="btnBox">
                        <a href="javascript:" class="btn" id="J_findBtn" onclick="javascript:hotleListSearch(true,'',true)"><fmt:message key="hotel_rate_hotel_filter2.query"></fmt:message></a>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>