<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="/WEB-INF/view/include/include-top.jsp" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <%@ include file="/WEB-INF/view/include/head.jsp" %>
    <link rel="stylesheet" href="<%=path%>/static/css/seek-simplicity.css">
    <link rel="stylesheet" href="//at.alicdn.com/t/font_1267273_7f70hw008cl.css">
</head>
<body>

<!-- pc header start -->
<%@ include file="/WEB-INF/view/include/header_pc.jsp" %>
<!-- pc header end -->

<!-- mobile nav start -->
<%@ include file="/WEB-INF/view/include/mobile-nav.jsp" %>
<!-- mobile nav end -->

<!-- mobile search start -->
<%@ include file="/WEB-INF/view/include/mobile-search.jsp" %>
<!-- mobile search end -->

<!--全部城市-->
<%@ include file="/WEB-INF/view/include/allCities-m.jsp" %>

<!-- page wrapper start -->
<div class="wrapper">
    <div class="special__banner">
        <div class="img_xl"><img src="<%=path%>/static/images/seek_simplicity/Seek-Simplicity-XL.jpg" alt=""></div>
        <div class="img_lg"><img src="<%=path%>/static/images/seek_simplicity/Seek-Simplicity-L.jpg" alt=""></div>
        <div class="img_md"><img src="<%=path%>/static/images/seek_simplicity/Seek-Simplicity-M.jpg" alt=""></div>
        <div class="img_sm"><img src="<%=path%>/static/images/seek_simplicity/Seek-Simplicity-S.jpg" alt=""></div>
        <span class="next__btn" id="nextBtn"></span>
    </div>
    <!-- page content start-->
    <div class="special">
        <!-- page details -->
        <div class="wrapper wrapper-fixed" style="padding-top: 40px;">
            <div class="special__detail">
                ${activity.content}
                <div class="btn-box">
                    <a class="btn-sub" href="<%=path%>/seek-simplicity-form"
                       onclick="ga('send', 'event', 'seek-simplicity-form', 'click', '填写表单');">
                        <c:choose>
                            <c:when test="${locale =='zh_TW' }">
                                點擊參與
                            </c:when>
                            <c:otherwise>
                                点击参与
                            </c:otherwise>
                        </c:choose>
                    </a>
                </div>

                <div id="hotelList" class="hotelList">
                </div>

            </div>
            <!-- page content end -->
        </div>
        <!-- page wrapper end -->

        <!-- pc footer start -->
        <%@ include file="/WEB-INF/view/include/footer_pc.jsp" %>
        <!-- pc footer end -->

        <!-- mobile footer start -->
        <%@ include file="/WEB-INF/view/include/footer_m.jsp" %>
        <!-- mobile footer end -->

        <!-- right sidebar start -->
        <%@ include file="/WEB-INF/view/include/right-sidebar.jsp" %>
        <!-- right sidebar start -->

        <script>
            var locale = "${locale}";
            var path = "<%=path%>";
            var templatePath = "<%=path%>/template";
            $(function () {
                $.ajax({
                    url: _basePath3 + '/hotelList/chain/' + _chainCode,
                    data: {
                        'hotelCodes': "HUSHASS,HUPEKEB,HUCANLN,HUHGHHR,HUXMNES,HUDLUHD,HUPEKBC,HUHGHMS"
                    },
                    type: 'POST',
                    async: true,
                    success: function (data) {
                        var list_html = '';
                        var sabre = [79724,78722];
                        for (var i = 0; i < data.length; i++) {
                            if (i == 0) {
                                list_html += '<h2>【促销酒店】</h2>' +
                                    '<p>* 酒店活动套餐提供价值50美金的优惠券，仅限于酒店内消费</p>';
                            }
                            if (i == 2) {
                                list_html += '<h2>【推荐酒店】</h2>' +
                                    '<p>*了解更多SLH酒店优惠房价</p>';
                            }
                            var dotIndex = 0, imgUrl = '';
                            if (!!data[i].coverUrl) {
                                dotIndex = (data[i].coverUrl).lastIndexOf('.');
                                imgUrl = data[i].coverUrl.substring(0, dotIndex) + '_330_230' + data[i].coverUrl.substring(dotIndex);
                            }
                            list_html += '<div class="hotel-item"><div class="img">' +
                                '<a onclick="ga(\'send\', \'event\', \'seek-simplicity\', \'click\', \'' + data[i].hotelName + '\');" href="/hotel/' + data[i].hotelLink + '?locale=zh_CN" style="background: url(' + imgUrl + ') no-repeat center center; background-size: cover;"></a>' +
                                '</div>' +
                                '<div class="hotel-con">' +
                                '    <div class="info">' +
                                '        <a href="/destination/nagoya/the-tower-hotel-nagoya">' +
                                '            <h3>' + data[i].hotelName + '</h3>' +
                                '            <p class="name-en">' + data[i].hotelNameEn + '</p>           ' +
                                '        </a>' +
                                '        <p class="add"><i style="background: url(<%=path%>/static/images/seek_simplicity/gps.png) no-repeat;"></i>' + data[i].countryName + '·' + data[i].cityName + '</p>' +
                                '        <p class="txt">' + data[i].brief + '</p>' +
                                '    </div>' +
                                '    <div class="look">';
                            if (i < 2) {
                                list_html += '<a onclick="booking(\'\',\'SLMSPL\',\'' + sabre[i] + '\', \'' + data[i].hotelName + '\', \'' + data[i].hotelCode + '\');">立即预订</a>';
                            } else {
                                list_html += '<a onclick="ga(\'send\', \'event\', \'seek-simplicity\', \'click\', \'' + data[i].hotelName + '\');" href="/hotel/' + data[i].hotelLink + '?locale=zh_CN">查看详情</a>';
                            }
                            list_html += '    </div></div></div>';
                        }
                        $('#hotelList').html(list_html);
                    }
                });

                $('#nextBtn').click(function () {
                    $('html, body').animate({
                        scrollTop: $('.special__banner').height(),
                    })
                });
                $('.video-box').click(function (event) {
                    event.preventDefault();
                    videoUrl = $(this).data('videourl');
                    console.log('foodswiper-video', videoUrl);
                    if (videoUrl !== '') {
                        var videoTpl = '<div class="com-video-cover" id="videoCover">' +
                            '<div class="cover-inner">' +
                            '<div class="inner-box">' +
                            '<div class="cover-content">' +
                            '<button class="close-btn" style="z-index:99999;padding:0;"><i class="iconfont iconguanbi"></i></button>' +
                            '<video style="position: relative;z-index: 777;" src="' + videoUrl + '" controls="controls" controlslist="nodownload" webkit-playsinline playsinline x5-playsinline x-webkit-airplay="allow" oncontextmenu="return false;" webkit-playsinline="true" x-webkit-airplay="true" playsinline="true" x5-video-orientation="h5" x5-video-player-fullscreen="true">' +
                            '</video>' +
                            '</div>' +
                            '</div>' +
                            '</div>' +
                            '</div>';
                        $('body,html').addClass('un-scroll');
                        $('body').append(videoTpl);
                        var $videoCover = $('#videoCover'),
                            $closeBtn = $videoCover.find('.close-btn'),
                            $coverInner = $videoCover.find('.cover-inner'),
                            thisVideo = $videoCover.find('video')[0],
                            otherVideo = $('video')[0];
                        if (otherVideo && !otherVideo.paused) {
                            otherVideo.pause();
                        }
                        if (thisVideo.paused) {
                            thisVideo.play();
                        }
                        $closeBtn.click(function () {
                            console.log('长度', $('#qqvideobridge').length)
                            while ($('#qqvideobridge').length > 0) {
                                console.log($('#qqvideobridge').length, 'qqvideobridge1111111')
                                destroyIframe('qqvideobridge');
                                console.log($('#qqvideobridge').length, 'qqvideobridge2222222')
                            }
                            $videoCover.remove();
                        });
                        function destroyIframe(iframeID) {
                            var iframe = $('#' + iframeID).prop('contentWindow');
                            $('#' + iframeID).attr('src', 'about:blank');
                            try {
                                iframe.document.write('');
                                iframe.document.clear();
                            } catch (e) {
                            }
                            //把iframe从页面移除
                            $('#' + iframeID).remove();
                        }
                    }
                })
            });

            $('.special__banner').css({
                "max-height": $(window).height() - $('.pc-header').height()
            })

        </script>

        <!-- 页面公用方法 -->
        <script src="<%=path%>/static/js/new_common.js?v=20180427"></script>
        <script src="<%=path%>/static/js/handlebars-v4.0.5.js"></script>
        <script src="<%=path%>/static/js/nodetpl.js"></script>
        <script src="<%=path%>/static/js/fetch-jsonp.js"></script>
        <script src='<%=path%>/static/slh/common.js?v=20180426'></script>
        <script src='<%=path%>/static/slh/config.js?v=20180426'></script>
        <script src='<%=path%>/static/slh/hotel.js?v=20180426'></script>
        <script src="<%=path%>/static/slh/handlebars-helper.js?v=20180328"></script>
        <script src='<%=path%>/static/slh/destination.js?v=20170904'></script>
        <script>
            //ga获取
            function getLinker() {
                var ga = window[window['GoogleAnalyticsObject']];
                if (ga && ga.getAll){
                    return ga.getAll()[0].get('linkerParam');
                }
                return '';
            }
            /**
             * 跳转到英文预订网站的购物车页面
             * @param roomCode
             * @param rateCode
             * @returns
             */
            function booking(roomCode, rateCode, sabreCode, hotelName, hotelCode) {
                ga('send', 'event', 'seek-simplicity', 'click', hotelName);
                var checkinDate = new Date();
                var adultNumber = 2;
                checkinDate.setTime(checkinDate.getTime() + 24 * 60 * 60 * 1000);
                var checkoutDate = checkinDate.getFullYear() + "-" + (checkinDate.getMonth() + 1) + "-" + (checkinDate.getDate() + 2);
                var rate = sessionStorage.getItem("AC_CURRENCY");
                locale = locale.replace("_", "-");
                if (locale == "zh-TW") {
                    locale += "&sbe_ri=0";
                }
                var url = "https://be.synxis.com/?adult=" + adultNumber + "&arrive=" + checkinDate + "&chain=22402&child=0&currency=" + rate + "&depart=" + checkoutDate
                    + "&filter=CHAIN&guestemail=CHAIN&hotel=" + sabreCode + "&level=chain&locale=" + locale + "&rooms=1&src=SLHCN&themecode=slhtheme&" + getLinker(); //SLPAMX
                console.log("url: " + url);
                addLog("slh", "booking", "info", url);
                ga('send', 'event', 'button', 'click', hotelCode + '预订点击');
                window.open(url, "booking");
            }
        </script>
</body>
</html>