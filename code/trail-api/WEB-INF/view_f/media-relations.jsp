<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/view/include/include-top.jsp"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<%@ include file="/WEB-INF/view/include/head.jsp"%>
</head>
<body>

	<!-- pc header start -->
	<%@ include file="/WEB-INF/view/include/header_pc.jsp"%>
	<!-- pc header end -->

	<!-- mobile nav start -->
	<%@ include file="/WEB-INF/view/include/mobile-nav.jsp"%>
	<!-- mobile nav end -->

	<!--全部城市-->
	<%@ include file="/WEB-INF/view/include/allCities-m.jsp"%>

	<!-- page wrapper start -->
	<div class="wrapper">
		<!-- page banner -->
		<div class="page-banner about-banner text-center">
			<p>
				<fmt:message key="media_relations.for"></fmt:message><span class="text-gold"><fmt:message key="media_relations.particular"></fmt:message></span><fmt:message key="media_relations.text"></fmt:message>
			</p>
			<h3 class="banner-title"><fmt:message key="media_relations.particularHotel"></fmt:message></h3>
		</div>
		<!-- crumb nav start -->
		<div class="crumb-nav">
			<div class="fixed-wrapper">
				<a href="<%=path%>/?locale=${locale}"><fmt:message key="media_relations.index"></fmt:message></a><i class="line">&gt;</i><a
					href="<%=path%>/brand-story/about-us?locale=${locale}"><fmt:message key="media_relations.brandStory"></fmt:message></a><i class="line">&gt;</i><span
					class="cur"><fmt:message key="media_relations.mediaRelations"></fmt:message></span>
			</div>
		</div>
		<!-- page-tabnav -->
		<div class="page-tabnav-box" id="news_second_menu">
			<ul class="page-tabnav text-center after-clear">
				<li><a href="<%=path%>/brand-story/about-us?locale=${locale}" id="about-us"><fmt:message key="media_relations.aboutUs"></fmt:message><span
						class="nav-tip"></span></a></li>
				<li><a href="<%=path%>/brand-story/news?locale=${locale}" id="news"><fmt:message key="media_relations.news"></fmt:message><span
						class="nav-tip"></span></a></li>
				<li><a href="<%=path%>/brand-story/weChat?locale=${locale}" id="weChat"><fmt:message key="media_relations.weixinChoiceness"></fmt:message><span
						class="nav-tip"></span></a></li>
				<li><a href="<%=path%>/brand-story/media-relations?locale=${locale}" class="cur"
					id="social-media"><fmt:message key="media_relations.mediaRelations"></fmt:message><span class="nav-tip"></span></a></li>
				<li><a href="<%=path%>/brand-story/contact-us?locale=${locale}" id="contact-us"><fmt:message key="media_relations.contactUs"></fmt:message><span
						class="nav-tip"></span></a></li>
				<!-- <li><a href="join.html" id="club-registration">加入我们<span class="nav-tip"></span></a></li> -->
			</ul>
		</div>
		<!-- page-tabnav -->
		<!-- social-media start -->
		<div class="fixed-wrapper page-content social-media">
			<div class="page-title text-center">
				<div class="page-title-inner">
					<span><fmt:message key="media_relations.application"></fmt:message></span>
				</div>
			</div>
			<div class="text-center form-instruction">
				<p><fmt:message key="media_relations.text_center1"></fmt:message></p>
				<p>
					<fmt:message key="media_relations.text_center2"></fmt:message><a
						href="https://app.leonardoworldwide.net/slh/login"
						target="_blank"><fmt:message key="media_relations.text_center3"></fmt:message></a><fmt:message key="media_relations.text_center4"></fmt:message><br /><fmt:message key="media_relations.text_center5"></fmt:message>
				</p>
			</div>
			<div class="item address-info after-clear">
				<ul class="float-left">
					<li><h3 class="from-title"><fmt:message key="media_relations.ChinaRegion"></fmt:message>：</h3></li>
					<li><fmt:message key="media_relations.manager"></fmt:message></li>
					<li><a href="mailto:<EMAIL>"><EMAIL></a></li>
					<%--<li>Tel: <a href="tel:+86 21 60323585">+86 21 60323585</a></li>--%>
					<li><fmt:message key="media_relations.address"></fmt:message></li>
				</ul>
				<ul class="float-right">
					<li><h3 class="from-title"><fmt:message key="media_relations.Asian-PacificRegion"></fmt:message>:</h3></li>
					<li>Juliana Tan – <fmt:message key="media_relations.DirectorPRAP"></fmt:message></li>
					<li><a href="mailto:<EMAIL>"><EMAIL></a></li>
					<%--<li>Tel: <a href="tel:+65 6632 7288">+65 6632 7288</a></li>--%>
					<li>60 Paya Lebar Road, #04-03 Paya Lebar Square, Singapore 409051</li>
				</ul>
			</div>
			<div class="mediaInForm">
				<div class="formBox">
					<div class="text-center form-instruction">
						<p><fmt:message key="media_relations.text_center6"></fmt:message></p>
						<p><fmt:message key="media_relations.text_center7"></fmt:message></p>
					</div>
					<div id="vueCheck">
						<p class="ie9Lable"><fmt:message key="media_relations.hotelName"></fmt:message>:</p>
						<div class="item">
							<span class="input-tip">*</span> <input class="txtCss"
								id="requestedHotel" value="" v-model='requestedHotel'
								placeholder="<fmt:message key="media_relations.hotelName"></fmt:message>" />
						</div>
						<div class="item">
							<span class="input-tip">*</span> <select id="title"
								v-model='title'>
								<option value=""><fmt:message key="media_relations.title"></fmt:message></option>
								<option value="Dr"><fmt:message key="media_relations.Mr"></fmt:message></option>
								<option value="Miss"><fmt:message key="media_relations.Mrs"></fmt:message></option>
								<option value="Mr"><fmt:message key="media_relations.lady"></fmt:message></option>
								<option value="Mrs"><fmt:message key="media_relations.Miss"></fmt:message></option>
								<option value="Ms"><fmt:message key="media_relations.doctor"></fmt:message></option>
								<option value="Prof"><fmt:message key="media_relations.professor"></fmt:message></option>
							</select>
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.firstName"></fmt:message>:</p>
						<div class="item">
							<span class="input-tip">*</span> <input class="txtCss"
								id="firstName" v-model='firstName' value="" placeholder="<fmt:message key="media_relations.firstName"></fmt:message>" />
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.lastName"></fmt:message>:</p>
						<div class="item">
							<span class="input-tip">*</span> <input class="txtCss"
								id="lastName" v-model='lastName' value="" placeholder="<fmt:message key="media_relations.lastName"></fmt:message>" />
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.country"></fmt:message>:</p>
						<div class="item">
							<span class="input-tip">*</span> <input class="txtCss"
								id="cityAndCountry" v-model='cityAndCountry' value=""
								placeholder="<fmt:message key="media_relations.country"></fmt:message>" />
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.emailAddress"></fmt:message>:</p>
						<div class="item">
							<span class="input-tip">*</span> <input class="txtCss" id="email"
								v-model='email' value="" placeholder="<fmt:message key="media_relations.emailAddress"></fmt:message>" />
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.phoneNumber"></fmt:message>:</p>
						<div class="item">
							<input class="txtCss" id="phoneNumber" value="" placeholder="<fmt:message key="media_relations.phoneNumber"></fmt:message>" />
						</div>
						<div class="item">
							<span class="input-tip">*</span> <select id="position"
								v-model='position'>
								<option value=""><fmt:message key="media_relations.position"></fmt:message></option>
								<option value="Editor"><fmt:message key="media_relations.editor"></fmt:message></option>
								<option value="Managing Director"><fmt:message key="media_relations.managingDirector"></fmt:message></option>
								<option value="News Editor"><fmt:message key="media_relations.newsEditor"></fmt:message></option>
								<option value="Business Editor"><fmt:message key="media_relations.businessEditor"></fmt:message></option>
								<option value="Travel Editor"><fmt:message key="media_relations.travelEditor"></fmt:message></option>
								<option value="Travel Writer"><fmt:message key="media_relations.travelWriter"></fmt:message></option>
								<option value="Food Writer"><fmt:message key="media_relations.foodWriter"></fmt:message></option>
								<option value="Spa Writer"><fmt:message key="media_relations.spaWriter"></fmt:message></option>
								<option value="Freelancer"><fmt:message key="media_relations.freelancer"></fmt:message></option>
								<option value="Blogger"><fmt:message key="media_relations.blogger"></fmt:message></option>
								<option value="Photographer"><fmt:message key="media_relations.photographer"></fmt:message></option>
								<option value="Producer"><fmt:message key="media_relations.producer"></fmt:message></option>
								<option value="Other"><fmt:message key="media_relations.other"></fmt:message></option>
							</select>
						</div>
						<div class="box" v-show="otherShow">
							<p class="ie9Lable"><fmt:message key="media_relations.otherPosition"></fmt:message>:</p>
							<div class="item">
								<span class="input-tip">*</span> <input class="txtCss"
									id="other" v-model='other' value="" placeholder="<fmt:message key="media_relations.otherPosition"></fmt:message>" />
							</div>
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.publicationName"></fmt:message>:</p>
						<div class="item">
							<span class="input-tip">*</span> <input class="txtCss"
								id="publicationName" v-model='publicationName' value=""
								placeholder="<fmt:message key="media_relations.publicationName"></fmt:message>" />
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.publicationURL"></fmt:message>:</p>
						<div class="item">
							<input class="txtCss" id="publicationURL" value=""
								placeholder="<fmt:message key="media_relations.publicationURL"></fmt:message>" />
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.publicationDescription"></fmt:message>:</p>
						<div class="item">
							<textarea class="textCss" id="descriptionOfPublication" cols=40
								rows=4 placeholder="<fmt:message key="media_relations.publicationDescription"></fmt:message>"></textarea>
						</div>
					</div>
					<div class="box">
						<h3 class="from-title">
							<span class="input-tip">*</span><fmt:message key="media_relations.media"></fmt:message>
						</h3>
						<ul class="item after-clear">
							<li class="item-w25" id="medium">
								<div class="checkbox" data-value="Print - newspaper">
									<span></span><input name="favouriteLeisureActivities"
										type="hidden" value="" /><fmt:message key="media_relations.printedNewspapers"></fmt:message>
								</div>
							</li>
							<li class="item-w25">
								<div class="checkbox" data-value="Print - magazine">
									<span></span><input name="favouriteLeisureActivities"
										type="hidden" value="" /><fmt:message key="media_relations.printedMagazine"></fmt:message>
								</div>
							</li>
							<li class="item-w25">
								<div class="checkbox" data-value="Broadcast">
									<span></span><input name="favouriteLeisureActivities"
										type="hidden" value="" /><fmt:message key="media_relations.broadcast"></fmt:message>
								</div>
							</li>
							<li class="item-w25">
								<div class="checkbox" data-value="Radio">
									<span></span><input name="favouriteLeisureActivities"
										type="hidden" value="" /><fmt:message key="media_relations.radio"></fmt:message>
								</div>
							</li>
							<li class="item-w25">
								<div class="checkbox" data-value="Online">
									<span></span><input name="favouriteLeisureActivities"
										type="hidden" value="" /><fmt:message key="media_relations.online"></fmt:message>
								</div>
							</li>
							<li class="item-w25">
								<div class="checkbox" data-value="Blog">
									<span></span><input name="favouriteLeisureActivities"
										type="hidden" value="" /><fmt:message key="media_relations.blog"></fmt:message>
								</div>
							</li>
							<li class="item-w25">
								<div class="checkbox" data-value="Social Media">
									<span></span><input name="favouriteLeisureActivities"
										type="hidden" value="" /><fmt:message key="media_relations.socialMedia"></fmt:message>
								</div>
							</li>
							<li class="item-w25">
								<div class="checkbox" data-value="Other">
									<span></span><input name="favouriteLeisureActivities"
										type="hidden" value="" /><fmt:message key="media_relations.other"></fmt:message>
								</div>
							</li>
							<li class="clear"></li>
						</ul>
						<!--list end-->
					</div>
					<div class="box">
						<h3 class="from-title"><fmt:message key="media_relations.fromTitle"></fmt:message></h3>
						<p class="ie9Lable"><fmt:message key="media_relations.instagram"></fmt:message>:</p>
						<div class="item">
							<input class="txtCss" id="instagram" value=""
								placeholder="<fmt:message key="media_relations.instagram"></fmt:message>" />
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.twitter"></fmt:message>:</p>
						<div class="item">
							<input class="txtCss" id="twitter" value=""
								placeholder="<fmt:message key="media_relations.twitter"></fmt:message>" />
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.facebook"></fmt:message>:</p>
						<div class="item">
							<input class="txtCss" id="facebook" value=""
								placeholder="<fmt:message key="media_relations.facebook"></fmt:message>" />
						</div>
						<div class="box">
							<h3 class="from-title">
								<span class="input-tip">*</span><fmt:message key="media_relations.frequency"></fmt:message>
							</h3>
							<ul class="item after-clear">
								<li style="display: none;"><input
									name="publicationFrequency" type="hidden" value=""
									id="publicationFrequency" /></li>
								<li class="item-w25 radiobox" data-value="Daily"><span></span><fmt:message key="media_relations.daily"></fmt:message></li>
								<li class="item-w25 radiobox" data-value="Weekly"><span></span><fmt:message key="media_relations.weekly"></fmt:message></li>
								<li class="item-w25 radiobox" data-value="Monthly"><span></span><fmt:message key="media_relations.monthly"></fmt:message></li>
								<li class="item-w25 radiobox" data-value="Other"><span></span><fmt:message key="media_relations.other"></fmt:message></li>
								<li class="clear"></li>
							</ul>
							<!--list end-->
						</div>
						<div id="vueCheck2">
							<p class="ie9Lable"><fmt:message key="media_relations.vueCheck2"></fmt:message>:</p>
							<div class="item">
								<span class="input-tip">*</span> <input class="txtCss"
									id="circulation" v-model="circulation" value=""
									placeholder="<fmt:message key="media_relations.vueCheck2"></fmt:message>" />
							</div>
							<p class="ie9Lable"><fmt:message key="media_relations.audience/audio-visualGroup"></fmt:message>:</p>
							<div class="item">
								<span class="red cc">*</span>
								<textarea class="textCss" id="audienceProfile"
									v-model="audienceProfile" cols=40 rows=4 placeholder="<fmt:message key="media_relations.audience/audio-visualGroup"></fmt:message>"></textarea>
							</div>
							<p class="ie9Lable"><fmt:message key="media_relations.quantity"></fmt:message>:</p>
							<div class="item">
								<span class="input-tip">*</span> <input class="txtCss"
									id="coverage" value="" v-model="coverage"
									placeholder="<fmt:message key="media_relations.quantity"></fmt:message>" />
							</div>
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.reportingDate"></fmt:message>:</p>
						<div class="item">
							<span class="input-tip">*</span>
							<!-- <span class="date-tip"></span> -->
							<input class="txtCss time" value="" placeholder="<fmt:message key="media_relations.reportingDate"></fmt:message>"
								id="J_bdsj" />
							<div class="timeBox" id="time_bdsj"></div>
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.contentPointOfView"></fmt:message>:</p>
						<div class="item" id="vueCheck3">
							<span class="red cc">*</span>
							<textarea class="textCss" id="storyAngle" v-model='storyAngle'
								cols=40 rows=4 placeholder="<fmt:message key="media_relations.contentPointOfView"></fmt:message>"></textarea>
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.arrivalDate"></fmt:message>:</p>
						<div class="item">
							<span class="input-tip">*</span>
							<!-- <span class="date-tip"></span> -->
							<input class="txtCss time" value="" placeholder="<fmt:message key="media_relations.arrivalDate"></fmt:message>"
								id="J_dida" />
							<div class="timeBox" id="time_dida"></div>
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.arrivalTime"></fmt:message>:</p>
						<div class="item" id="vueCheck6">
							<span class="input-tip">*</span> <input class="txtCss"
								id="arrivalTime" value="" v-model='arrivalTime'
								placeholder="<fmt:message key="media_relations.arrivalTime"></fmt:message>" />
							<p class="tip"><fmt:message key="media_relations.format"></fmt:message></p>
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.checkOutDate"></fmt:message>:</p>
						<div class="item">
							<span class="input-tip">*</span>
							<!-- <span class="date-tip"></span> -->
							<input class="txtCss time" value="" placeholder="<fmt:message key="media_relations.checkOutDate"></fmt:message>"
								id="J_lidian" />
							<div class="timeBox" id="time_lidian"></div>
						</div>
						<div class="item">
							<select id="numberofTravellers">
								<option value=""><fmt:message key="media_relations.guestQuantity"></fmt:message></option>
								<option value="1">1</option>
								<option value="2">2</option>
								<option value="3">3</option>
								<option value="4">4</option>
							</select>
						</div>
						<div class="item">
							<select id="expectedBoardBasis">
								<option value=""><fmt:message key="media_relations.expectedBoardBasis"></fmt:message></option>
								<option value="B & B"><fmt:message key="media_relations.breakfastIncluded"></fmt:message></option>
								<option value="Half-board"><fmt:message key="media_relations.halfBoard"></fmt:message></option>
								<option value="All inclusive"><fmt:message key="media_relations.allInclusive"></fmt:message></option>
							</select>
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.specialRequirements"></fmt:message>:</p>
						<div class="item">
							<textarea class="textCss" id="specialRequests" cols=40 rows=4
								placeholder="<fmt:message key="media_relations.specialRequirements"></fmt:message>"></textarea>
						</div>
						<div class="item" id="vueCheck4">
							<span class="input-tip">*</span> <select id="stayBasis"
								v-model='stayBasis'>
								<option value=""><fmt:message key="media_relations.checkInWay"></fmt:message></option>
								<option value="Complimentary"><fmt:message key="media_relations.complimentary"></fmt:message></option>
								<option value="Press rate"><fmt:message key="media_relations.pressRate"></fmt:message></option>
							</select>
						</div>
						<p class="ie9Lable"><fmt:message key="media_relations.replyDate"></fmt:message>:</p>
						<div class="item">
							<span class="input-tip">*</span>
							<!-- <span class="date-tip"></span> -->
							<input class="txtCss time" value="" placeholder="<fmt:message key="media_relations.replyDate"></fmt:message>"
								id="J_dafu" />
							<div class="timeBox" id="time_dafu"></div>
						</div>
						<div class="item text-center">
							<button type="submit" id="submit" class="btn btn-green"><fmt:message key="media_relations.submit"></fmt:message></button>
							<div id="submit_loading" class="inline-loading">
								<img src="<%=path%>/static/images/loading.gif" alt="">
							</div>
						</div>
					</div>
				</div>
				<!--formBox end-->
			</div>
			<!--mediaInForm end-->
		</div>
		<!-- social-media end -->
	</div>
	<!-- page wrapper end -->

	<!-- pc footer start -->
	<%@ include file="/WEB-INF/view/include/footer_pc.jsp"%>
	<!-- pc footer end -->

	<!-- mobile footer start -->
	<%@ include file="/WEB-INF/view/include/footer_m.jsp"%>
	<!-- mobile footer end -->

	<!-- right sidebar start -->
	<%@ include file="/WEB-INF/view/include/right-sidebar.jsp"%>
	<!-- right sidebar start -->

	<script>
		var locale = "${locale}";
		var path = "<%=path%>";
		var templatePath = "<%=path%>/template";
		var type = "${type}";
	</script>

	<!-- 页面公用方法 -->
	<script src="<%=path%>/static/js/new_common.js?v=20180427"></script>
	<script src="<%=path%>/static/js/vue.js"></script>

	<script src='<%=path%>/static/slh/config.js?v=20180426'></script>
	<script src="<%=path%>/static/js/handlebars-v4.0.5.js"></script>
	<script src="<%=path%>/static/slh/handlebars-helper.js?v=20180328"></script>
	<script src="<%=path%>/static/js/nodetpl.js"></script>
	<script src="<%=path%>/static/js/fetch-jsonp.js"></script>
	<script src='<%=path%>/static/slh/common.js?v=20180426'></script>
	<script src='<%=path%>/static/slh/config.js?v=20180426'></script>
	<script src='<%=path%>/static/slh/destination.js?v=20170904'></script>
	<script src="<%=path%>/static/js/media_new.js?v=20170904"></script>
</body>
</html>
