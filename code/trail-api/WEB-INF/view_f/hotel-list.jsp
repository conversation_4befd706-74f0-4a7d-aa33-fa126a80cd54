<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/view/include/include-top.jsp"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<%@ include file="/WEB-INF/view/include/head.jsp"%>
</head>
<body>

	<!-- pc header start -->
	<%@ include file="/WEB-INF/view/include/header_pc.jsp"%>
	<!-- pc header end -->

	<!-- mobile nav start -->
	<%@ include file="/WEB-INF/view/include/mobile-nav.jsp"%>
	<!-- mobile nav end -->

	<!-- mobile search start -->
	<%@ include file="/WEB-INF/view/include/mobile-search.jsp"%>
	<!-- mobile search end -->

	<!--全部城市-->
	<%@ include file="/WEB-INF/view/include/allCities-m.jsp"%>

	<!-- 时间选择-->
	<%@ include file="/WEB-INF/view/include/hotel-rate-date-picker.jsp"%>

	<!-- 手机版的城市列表(主题、国家、活动都一样)-->
	<%@ include file="/WEB-INF/view/include/hotel-list-cities_m.jsp"%>

	<!-- 手机版主题列表-->
	<%@ include file="/WEB-INF/view/include/experience-list_m.jsp"%>

	<!-- page wrapper start -->
	<div class="wrapper">
		<!--hotelBanner satrt-->
		<div class="hotelBanner clx" id="country_detail">
			<div class="thumb"
				style="background-image: url(&quot;${banner}&quot;);">
				<img src="${banner}">
			</div>
			<div class="content">
				<div class="con">
					<h3>${countryName }</h3>
					<div class="line"></div>
					<div class="txt">${detail }</div>
				</div>
			</div>
		</div>
		<!--hotelBanner end-->
		<!-- crumb nav start-->
		<div class="crumb-nav">
			<div class="fixed-wrapper" id="crumb" name="crumb">
				<a href="<%=path%>/?locale=${locale}"><span><fmt:message key="hotel_list.index"></fmt:message></span></a><i class="line">&gt;</i><span
					class="cur">${crumbNav }</span>
			</div>
		</div>
		<!-- crumb nav start-->
		<!--hotelFilter start-->
		<%@ include file="/WEB-INF/view/include/hotel-rate-hotel-filter.jsp"%>
		<!--hotelFilter end-->
		<%@ include file="/WEB-INF/view/include/hotel-rate-hotel-box.jsp"%>


	</div>
	<!-- page wrapper end -->

	<!-- pc footer start -->
	<%@ include file="/WEB-INF/view/include/footer_pc.jsp"%>
	<!-- pc footer end -->

	<!-- mobile footer start -->
	<%@ include file="/WEB-INF/view/include/footer_m.jsp"%>
	<!-- mobile footer end -->

	<!-- right sidebar start -->
	<%@ include file="/WEB-INF/view/include/right-sidebar.jsp"%>
	<!-- right sidebar start -->
<!-- 	<div class="notice_pop">
		<div class="notice_box">
			<a href="javascript:void(0);" class="noticeCloseBtn"></a>
			<div class="con">
				<span class="imgBg"></span>
				<p>由于预订系统维护</br>
					<i>2019年2月9日19:00 - 2月10日0:00</i>网上预订暂停服务</br>
					如需预订酒店,请拨打</br>
					贵宾服务热线：<a href="tel:4001-203276">4001-203276</a></p>
			</div>
		</div>
	</div> -->

<%@ include file="/WEB-INF/view/include/template_hotel_type_m.jsp"%>
<%@ include file="/WEB-INF/view/include/template_hotel_type_pc.jsp"%>
<%@ include file="/WEB-INF/view/include/template_hotel_list_cities_m.jsp"%>
<%@ include file="/WEB-INF/view/include/template_hotel_list_cities_pc.jsp"%>
	<script>
		var locale = "${locale}";
		var path = "<%=path%>";
		var templatePath = "<%=path%>/template";
		var countryCode = "${countryCode}";
		var countryLink = "${countryLink}";
		var cityLink = "${cityLink}";
		var activityLink = "${activityLink}";
		var subjectId;
		var type = "${type}";
		var checkinDate = "${checkinDate}";
		var checkoutDate = "${checkoutDate}";
		var nights = "${nights}";
		var adultNumber = "${adultNumber}";
		var roomNumber = "${roomNumber}";
	</script>

	<!-- 页面公用方法 -->
	<script src="<%=path%>/static/js/new_common.js?v=20221013"></script>
	
	<script src='<%=path%>/static/slh/config.js?v=20180426'></script>
	<script src='<%=path%>/static/slh/common.js?v=20180426'></script>
	<script src="<%=path%>/static/js/handlebars-v4.0.5.js"></script>
	<script src="<%=path%>/static/js/nodetpl.js"></script>
	<script src="<%=path%>/static/slh/handlebars-helper.js?v=20180328"></script>
	<script src='<%=path%>/static/slh/hotel.js?v=20171129'></script>
	<script src='<%=path%>/static/slh/destination.js?v=20170904'></script>
	<script src='<%=path%>/static/slh/hotels-data.js?v=20180426'></script>
	<script src="<%=path%>/static/js/hotelList.js?v=20180426"></script>	
</body>
</html>
</html>