<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/view/include/include-top.jsp"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <%@ include file="/WEB-INF/view/include/head.jsp"%>
    <link href="<%=path%>/static/css/travelcomfortzones/css/ui.min.css?t=1.2" rel="stylesheet">
    <link href="<%=path%>/static/css/travelcomfortzones/css/AppsSearchLazy-1.min.css" rel="stylesheet">
    <link href="<%=path%>/static/css/travelcomfortzones/css/AppsSearchLazy-2.min.css" rel="stylesheet">
    <link rel="apple-touch-icon" sizes="180x180" href="<%=path%>/static/css/travelcomfortzones/img/apple-touch-icon.png">
    <link rel="mask-icon" href="<%=path%>/static/css/travelcomfortzones/img/safari-pinned-tab.svg" color="#282828">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="theme-color" content="#ffffff">
    <script src="<%=path%>/static/css/travelcomfortzones/js/glide.min.js"></script>
</head>
<body>
<!-- pc header start -->
<%@ include file="/WEB-INF/view/include/header_pc.jsp"%>
<!-- pc header end -->
<!-- mobile nav start -->
<%@ include file="/WEB-INF/view/include/mobile-nav.jsp"%>
<!-- mobile nav end -->

<!--全部城市-->
<%@ include file="/WEB-INF/view/include/allCities-m.jsp"%>

<div class="sc-layout" id="top">
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TG4CV7V" height="0" width="0"
                      style="display:none;visibility:hidden"></iframe></noscript>

    <div class="sc-layout__container">
        <div class="ui-container-full">
            <main>
                <div class="sc-stage sc-stage--variant-2 sc-stage--">
                    <div class="ui-image ui-image--i-1">
                        <picture class="ui-image__picture">
                            <source srcset="<%=path%>/static/css/travelcomfortzones/other/comfort-zones-chs-xl.ashx" media="(min-width: 1440px)">
                            <source srcset="<%=path%>/static/css/travelcomfortzones/other/comfort-zones-chs-l.ashx" media="(min-width: 992px)">
                            <source srcset="<%=path%>/static/css/travelcomfortzones/other/comfort-zones-chs-m.ashx" media="(min-width: 576px)">
                            <img class="ui-image__img" src="<%=path%>/static/css/travelcomfortzones/img/comfort-zones-chs-s.jpg" alt="your travel comfort zone"
                                 data-g-js-binding-image="">
                        </picture>
                    </div>
                    <div class="sc-stage__gradient-top"></div>
                    <div class="sc-stage__content">
                        <div class="ui-container-grid sc-stage__full-height">
                            <div class="ui-row sc-stage__full-height">
                                <div
                                        class="ui-col-12 ui-col-md-10 ui-offset-md-1 ui-col-lg-12 ui-offset-lg-0 ui-col-xxl-10 ui-offset-xxl-1 sc-stage__content-column">
                                    <div class="sc-stage__content-block">
                                        <!-- <p class="sc-stage__sub-title">T R A N S C E N D</p> -->
                                        <h1 class="ui-headline ui-headline--h1 ui-headline--t-9">
                                            超越自我的旅行
                                        </h1>
                                    </div>
                                    <div class="sc-stage__content-block">
                                        <div class="sc-stage__sub-link-container">
                                        </div>
                                        <div class="sc-stage__location-container">
                                            <i class="ui-icon ui-icon--ico-4 ui-icon--size-3">
                                            </i>
                                            <a class="ui-link ui-color-c-4 ui-link--caption" href="/hotel/castle-hot-springs">
                                                城堡温泉酒店
                                            </a>
                                        </div>
                                        <a href="#stage-end" class="sc-stage__scroll-down-indicator">
                                            <i class="ui-icon ui-icon--ico-27 ui-icon--size-5"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="stage-end"></div>
                <div class="sc-body-text sc-body-text--centered sc-body-text--no-spacing">
                    <div class="ui-container-grid">
                        <div class="ui-row sc-body-text__row">
                            <div class="ui-col-12 ui-col-lg-7 sc-body-text__column">
                                <h1 style="text-align: center;">当再次与旅行相遇之时</h1>
                                <p style="text-align: left;"><span>2022年，人们对假期自由更加的渴望，我们比以往任何时候都期待更令人难忘的旅行。但是，伴随着逃离日常生活的兴奋感，我们可能还被困在舒适圈内，内心有着恐惧、不确定性和焦虑的感觉。 <span
                                        style="text-align: justify;">根据SLH最近的一项OnePoll调查， <span
                                        style="text-align: justify; color: #0d0e00;">64%的人表示，</span><strong> 旅行是帮助我们走出舒适区的最佳方式</strong>。</span></span><span style="color: windowtext;">受访者表示，旅行让他们感觉充满能量（40%）、焕发活力（37%）和激发灵感（34%）。&nbsp; &nbsp;</span></p>
                                <div class="sc-body-text__link-container">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sc-image-text sc-image-text--none sc-image-text--right sc-image-text--no-spacing">
                    <div class="sc-image-text__frame-solid"></div>
                    <div class="ui-container-grid">
                        <div class="ui-row sc-image-text__row">
                            <div class="sc-image-text__frame-border"></div>
                            <div class="ui-col-12 ui-col-sm-3 sc-image-text__image-container">
                                <div class="ui-image ui-image--i-2">
                                    <picture class="ui-image__picture">
                                        <source srcset="<%=path%>/static/css/travelcomfortzones/img/top1.jpg" media="(min-width: 576px)">
                                        <img class="ui-image__img" src="<%=path%>/static/css/travelcomfortzones/img/top.jpg" alt="comfort-zones-diagram2"
                                             data-g-js-binding-image="">
                                    </picture>
                                </div>
                            </div>
                            <div class="ui-col-12 ui-col-sm-8 ui-col-lg-5 sc-image-text__text-container">
                                <div class="sc-body-text">
                                    <h1>旅行的意义</h1>
                                    <p>拜访从未到达过的目的地、以不同的方式探索最喜爱的老地方、挑战以前不敢尝试的活动或美食、独自旅行用外语与新朋友互动，超越自我的旅行将带来全新的旅行体验，改变自己，改变人生。</p>
                                    <div class="sc-body-text__link-container">
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="sc-image-text sc-image-text--border sc-image-text--no-spacing">
                    <div class="sc-image-text__frame-solid"></div>
                    <div class="ui-container-grid">
                        <div class="ui-row sc-image-text__row">
                            <div class="sc-image-text__frame-border"></div>
                            <div class="ui-col-12 ui-col-sm-4 sc-image-text__image-container">
                                <div class="ui-image ui-image--i-2">
                                    <picture class="ui-image__picture">
                                        <source srcset="<%=path%>/static/css/travelcomfortzones/img/roxie1.jpg" media="(min-width: 576px)">
                                        <img class="ui-image__img" src="<%=path%>/static/css/travelcomfortzones/img/roxie.jpg" alt="roxie" data-g-js-binding-image="">
                                    </picture>
                                </div>
                            </div>
                            <div class="ui-col-12 ui-col-sm-8 ui-col-lg-5 sc-image-text__text-container">
                                <div class="sc-body-text" style="margin-bottom:0;">
                                    <h2>一起来迈出挑战自我的第一步</h2>
                                    <p>为了鼓励人们迈出生活的舒适区的第一步，SLH将与艺术家Beatrice Oh合作创造《娇虎探险之旅》，其故事中的角色“娇虎与小莉”将带领观众在世界各地进行独特的游历，在他们寻找娇虎的家的过程中，与各个目的地的当地人进行沟通，了解更多本地的人文及历史故事。读者也能参加一系列由社交媒体主导的活动，还可以免费下载GIPHY、WhatsApp、LINE和微信手机贴纸，以激发下一次旅行的灵感。</p>
                                    <!-- <p>&nbsp;</p> -->
                                    <br/>
                                    <div class="sc-body-text__link-container" style="display: none;">
                                        <a class="ui-button ui-button--primary"
                                           href="https://soundcloud.com/user-*********/slh-travel-meditation?utm_source=clipboard&amp;utm_medium=text&amp;utm_campaign=social_sharing"
                                           rel="noopener" type="submit">
											<span class="ui-button__content">
												Holiday Headspace
												&nbsp;»
											</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="sc-body-text sc-body-text--centered sc-body-text--no-spacing">
                    <div class="ui-container-grid">
                        <div class="ui-row sc-body-text__row">
                            <div class="ui-col-12 ui-col-lg-7 sc-body-text__column">
                                <h1>如何安排超越自我的旅行</h1>
                                <p>
                                    不同的人意味着不同的挑战，尤其是在旅行方面。去到陌生遥远的地方，用未知的美食挑战你的味蕾，安排丰富的行程特别是那些你过去从不敢尝试的，学习一门外语去独自旅行——超越自我的旅行是个人成长中重要而令人兴奋的一部分，它可以给你信心，以全新的眼光探索人生中的其他方面。</p>
                                <p><span style="background-color: #ffffff; text-align: center; color: #282828;">拨打&nbsp;4001 203276联系&nbsp;</span><a
                                        href="mailto:<EMAIL>?subject=Transcend%20Travel%20Comfort%20Zone%20Experience%20Inquiry"
                                        style="color: #9e693d; background-color: #ffffff; text-align: center;">SLH 预订团队</a><span
                                        style="background-color: #ffffff; text-align: center; color: #282828;">&nbsp;预订中意的酒店体验。或者您可以通过网站预订酒店，同时将旅行体验加入到预订订单中。</span></p>
                                <!-- <br> -->
                                <div class="sc-body-text__link-container">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ui-container-grid">
                    <div class="ui-col-12">
                        <div class="promo">
                            <div class="promo__intro promo--center">
                                <!-- <h3 class="ui-headline ui-headline--h3"></h3> -->
                                <h5 class="ui-headline ui-headline--h5 ui-headline--handwriting">带我去发现与挑战</h5>
                                <p class="sc-three-col-promotion__item-copy">SLH精选旅行体验</p>
                            </div>
                        </div>
                        <div class="sc-image-gallery sc-image-gallery--bottom-link" data-g-name="ImageCardGallery"
                             data-image-amount="0">
                            <div class="glide" data-g-js-binding-image-gallery-images="">
                                <div class="glide__track" data-glide-el="track">
                                    <div class="glide__slides h-ui-mt-s-0">
                                        <div class="glide__slide">
                                            <h3 class="promo__headline promo__content-center">沉浸在海底世界</h3>
                                            <div class="sc-inspiration__item-container">
                                                <div class="ui-image ui-image--i-3">
                                                    <picture class="ui-image__picture">
                                                        <img class="ui-image__img" src="<%=path%>/static/css/travelcomfortzones/img/hotel_1.jpg" alt="马尔代夫芙花芬岛度假酒店"
                                                             width="424" height="480" loading="lazy">
                                                    </picture>
                                                    <a class="link-overlay" href="/hotel/huvafen-fushi-maldives" title="">了解更多</a>
                                                </div>
                                                <span class="gradient-full gradient-full--shade-0"></span>
                                                <span class="sc-inspiration__frame"></span>
                                                <div class="sc-inspiration__image-tile-content">
                                                    <div class="sc-inspiration__cta-container"></div>
                                                </div>
                                            </div>
                                            <div class="promo__content promo__content-center">
                                                <p class="promo__item-copy">马尔代夫芙花芬岛度假酒店, 马尔代夫</p>
                                                <a class="ui-button ui-button--secondary" href="/hotel/huvafen-fushi-maldives" title="">
                                                    <span class="ui-button__content">了解更多&nbsp;»</span>
                                                </a>
                                            </div>
                                        </div>

                                        <div class="glide__slide">
                                            <h3 class="promo__headline promo__content-center">沙漠中的野餐</h3>
                                            <div class="sc-inspiration__item-container">
                                                <div class="ui-image ui-image--i-3">
                                                    <picture class="ui-image__picture">
                                                        <img class="ui-image__img" src="<%=path%>/static/css/travelcomfortzones/img/hotel_2.jpg" alt="敦煌碧玥酒店"
                                                             width="424" height="480" loading="lazy">
                                                    </picture>
                                                    <a class="link-overlay" href="/hotel/jangala-dunhuang" title="">了解更多</a>
                                                </div>
                                                <span class="gradient-full gradient-full--shade-0"></span>
                                                <span class="sc-inspiration__frame"></span>
                                                <div class="sc-inspiration__image-tile-content">
                                                    <div class="sc-inspiration__cta-container"></div>
                                                </div>
                                            </div>
                                            <div class="promo__content promo__content-center">
                                                <p class="promo__item-copy">敦煌碧玥酒店, 中国甘肃</p>
                                                <a class="ui-button ui-button--secondary" href="/hotel/jangala-dunhuang" title="">
                                                    <span class="ui-button__content">了解更多&nbsp;»</span>
                                                </a>
                                            </div>
                                        </div>

                                        <div class="glide__slide">
                                            <h3 class="promo__headline promo__content-center">到私人码头乘坐摇橹船</h3>
                                            <div class="sc-inspiration__item-container">
                                                <div class="ui-image ui-image--i-3">
                                                    <picture class="ui-image__picture">
                                                        <img class="ui-image__img" src="<%=path%>/static/css/travelcomfortzones/img/hotel_3.jpg"
                                                             alt="杭州木守西溪酒店" width="424" height="480" loading="lazy">
                                                    </picture>

                                                    <a class="link-overlay" href="/hotel/muh-shoou-xixi-hotel" title="">了解更多</a>
                                                </div>
                                                <span class="gradient-full gradient-full--shade-0"></span>
                                                <span class="sc-inspiration__frame"></span>
                                                <div class="sc-inspiration__image-tile-content">
                                                    <div class="sc-inspiration__cta-container"></div>
                                                </div>
                                            </div>
                                            <div class="promo__content promo__content-center">
                                                <p class="promo__item-copy">杭州木守西溪酒店, 中国浙江</p>

                                                <a class="ui-button ui-button--secondary" href="/hotel/muh-shoou-xixi-hotel" title="">
                                                    <span class="ui-button__content">了解更多&nbsp;»</span>
                                                </a>

                                            </div>
                                        </div>

                                        <div class="glide__slide">
                                            <h3 class="promo__headline promo__content-center">一生只有一次机会成为火车站长</h3>
                                            <div class="sc-inspiration__item-container">
                                                <div class="ui-image ui-image--i-3">
                                                    <picture class="ui-image__picture">
                                                        <img class="ui-image__img" src="<%=path%>/static/css/travelcomfortzones/img/hotel_4.jpg"
                                                             alt="东京站大饭店" width="424" height="480" loading="lazy">
                                                    </picture>
                                                    <a class="link-overlay" href="/hotel/the-tokyo-station-hotel" title="">了解更多</a>
                                                </div>
                                                <span class="gradient-full gradient-full--shade-0"></span>
                                                <span class="sc-inspiration__frame"></span>
                                                <div class="sc-inspiration__image-tile-content">
                                                    <div class="sc-inspiration__cta-container"></div>
                                                </div>
                                            </div>
                                            <div class="promo__content promo__content-center">
                                                <p class="promo__item-copy">东京站大饭店, 日本东京</p>
                                                <a class="ui-button ui-button--secondary" href="/hotel/the-tokyo-station-hotel" title="">
                                                    <span class="ui-button__content">了解更多&nbsp;»</span>
                                                </a>
                                            </div>
                                        </div>

                                        <div class="glide__slide">
                                            <h3 class="promo__headline promo__content-center">带你做一次地道的泰国人</h3>
                                            <div class="sc-inspiration__item-container">
                                                <div class="ui-image ui-image--i-3">
                                                    <picture class="ui-image__picture">
                                                        <img class="ui-image__img" src="<%=path%>/static/css/travelcomfortzones/img/hotel_5.jpg"
                                                             alt="凯璞酷度酒店" width="424" height="480" loading="lazy">
                                                    </picture>

                                                    <a class="link-overlay" href="/hotel/cape-kudu-hotel" title="">了解更多</a>
                                                </div>

                                                <span class="gradient-full gradient-full--shade-0"></span>

                                                <span class="sc-inspiration__frame"></span>
                                                <div class="sc-inspiration__image-tile-content">
                                                    <div class="sc-inspiration__cta-container"></div>


                                                </div>
                                            </div>
                                            <div class="promo__content promo__content-center">
                                                <p class="promo__item-copy">凯璞酷度酒店, 泰国</p>

                                                <a class="ui-button ui-button--secondary" href="/hotel/cape-kudu-hotel" title="">
                                                    <span class="ui-button__content">了解更多&nbsp;»</span>
                                                </a>

                                            </div>
                                        </div>

                                        <div class="glide__slide">
                                            <h3 class="promo__headline promo__content-center">徒步喜马拉雅</h3>
                                            <div class="sc-inspiration__item-container">
                                                <div class="ui-image ui-image--i-3">
                                                    <picture class="ui-image__picture">

                                                        <img class="ui-image__img" src="<%=path%>/static/css/travelcomfortzones/img/hotel_6.jpg"
                                                             alt="岗提山谷隐世小屋"
                                                             width="424" height="480" loading="lazy">
                                                    </picture>
                                                    <a class="link-overlay" href="/hotel/gangtey-lodge" title="">了解更多</a>
                                                </div>
                                                <span class="gradient-full gradient-full--shade-0"></span>
                                                <span class="sc-inspiration__frame"></span>
                                                <div class="sc-inspiration__image-tile-content">
                                                    <div class="sc-inspiration__cta-container"></div>
                                                </div>
                                            </div>
                                            <div class="promo__content promo__content-center">
                                                <p class="promo__item-copy">岗提山谷隐世小屋, 不丹</p>
                                                <a class="ui-button ui-button--secondary" href="/hotel/gangtey-lodge" title="">
                                                    <span class="ui-button__content">了解更多&nbsp;»</span>
                                                </a>
                                            </div>
                                        </div>

                                        <div class="glide__slide">
                                            <h3 class="promo__headline promo__content-center">绿色生活</h3>
                                            <div class="sc-inspiration__item-container">
                                                <div class="ui-image ui-image--i-3">
                                                    <picture class="ui-image__picture">

                                                        <img class="ui-image__img" src="<%=path%>/static/css/travelcomfortzones/img/hotel_7.jpg"
                                                             alt="德萨海酒店"
                                                             width="424" height="480" loading="lazy">
                                                    </picture>
                                                    <a class="link-overlay" href="/hotel/desa-hay" title="">了解更多</a>
                                                </div>
                                                <span class="gradient-full gradient-full--shade-0"></span>
                                                <span class="sc-inspiration__frame"></span>
                                                <div class="sc-inspiration__image-tile-content">
                                                    <div class="sc-inspiration__cta-container"></div>
                                                </div>
                                            </div>
                                            <div class="promo__content promo__content-center">
                                                <p class="promo__item-copy">德萨海酒店, 印度尼西亚巴厘岛</p>
                                                <a class="ui-button ui-button--secondary" href="/hotel/desa-hay" title="">
                                                    <span class="ui-button__content">了解更多&nbsp;»</span>
                                                </a>
                                            </div>
                                        </div>

                                        <div class="glide__slide">
                                            <h3 class="promo__headline promo__content-center">永恒的建筑和文化瑰宝</h3>
                                            <div class="sc-inspiration__item-container">
                                                <div class="ui-image ui-image--i-3">
                                                    <picture class="ui-image__picture">
                                                        <img class="ui-image__img" src="<%=path%>/static/css/travelcomfortzones/img/hotel_8.jpg"
                                                             alt="百瑞营圣淘沙酒店" width="424" height="480" loading="lazy">
                                                    </picture>

                                                    <a class="link-overlay" href="/hotel/the-barracks-hotel-sentosa" title="">了解更多</a>
                                                </div>

                                                <span class="gradient-full gradient-full--shade-0"></span>

                                                <span class="sc-inspiration__frame"></span>
                                                <div class="sc-inspiration__image-tile-content">
                                                    <div class="sc-inspiration__cta-container"></div>


                                                </div>
                                            </div>
                                            <div class="promo__content promo__content-center">
                                                <p class="promo__item-copy">百瑞营圣淘沙酒店,新加坡</p>

                                                <a class="ui-button ui-button--secondary" href="/hotel/the-barracks-hotel-sentosa" title="">
                                                    <span class="ui-button__content">了解更多&nbsp;»</span>
                                                </a>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="glide__arrows glide__arrows--std" data-glide-el="controls">
                                    <button class="glide__arrow glide__arrow--left" data-glide-dir="<"><span class="sr-only">prev</span><i
                                            class="ui-icon ui-icon--ico-30 ui-icon--size-3"></i></button>
                                    <button class="glide__arrow glide__arrow--right" data-glide-dir=">"><span
                                            class="sr-only">next</span><i class="ui-icon ui-icon--ico-29 ui-icon--size-3"></i></button>
                                </div>
                                <div class="glide__bullets glide__bullets--std" data-glide-el="controls[nav]">
                                    <button class="glide__bullet" data-glide-dir="=0"></button>
                                    <button class="glide__bullet" data-glide-dir="=1"></button>
                                    <button class="glide__bullet" data-glide-dir="=2"></button>
                                    <button class="glide__bullet" data-glide-dir="=3"></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- https://www.youtube-nocookie.com/embed/C6erZpW8xtI?rel=0&amp;showinfo=0&amp;autoplay=0 -->
                <div class="sc-video sc-video--v-1">
                    <video ref="video" class="sc-video__iframe"  controls poster="<%=path%>/static/css/travelcomfortzones/img/poster.jpg">
                        <source src="https://1252139118.vod2.myqcloud.com/1fda134bvodtranscq1252139118/6267b5d6387702304070969592/v.f100040.mp4" type="video/mp4">
                    </video>
                </div>

            </main>
        </div>
    </div>
    <a class="ui-back-to-top" data-g-name="BackToTop" style="cursor: pointer;">
        <svg class="ui-back-to-top__media" width="71" height="71" viewBox="0 0 71 71" fill="none"
             xmlns="http://www.w3.org/2000/svg">
            <g filter="url(#filter0_d)">
                <path
                        d="M63 35.5C63 50.6878 50.6878 63 35.5 63C20.3122 63 8 50.6878 8 35.5C8 20.3122 20.3122 8 35.5 8C50.6878 8 63 20.3122 63 35.5Z"
                        fill="#282828"></path>
            </g>
            <path d="M26 35L27.5862 36.5863L33.875 30.3088V44H36.125V30.3088L42.4025 36.5975L44 35L35 26L26 35Z" fill="#fff">
            </path>
            <defs>
                <filter id="filter0_d" x="0" y="0" width="71" height="71" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0">
                    </feColorMatrix>
                    <feOffset></feOffset>
                    <feGaussianBlur stdDeviation="4"></feGaussianBlur>
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"></feColorMatrix>
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"></feBlend>
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"></feBlend>
                </filter>
            </defs>
        </svg>
    </a>

    <!-- <div class="sc-cookie-banner" data-g-name="CookieBanner">
        <div class="ui-container-grid sc-cookie-banner__container">
            <div class="sc-cookie-banner__text-containter">
                <div class="sc-cookie-banner__line">
                    <span>您接受使用cookie进行数据统计以继续访问网站。</span>
                    <a class="ui-link" href="/about-slh/cookie-policy">
                        更多
                    </a>

                </div>
            </div>

            <div class="sc-cookie-banner__accept-button">
                <button class="ui-button ui-button--outlined" type="submit">
                    <span class="ui-button__content">
                        我明白了
                    </span>
                </button>

            </div>
        </div>
    </div> -->
</div>
    <script type="text/javascript">
        function docReady (fn) {
            if (document.readyState === "complete" || document.readyState === "interactive") {
                setTimeout(fn, 1);
            } else {
                document.addEventListener("DOMContentLoaded", fn);
            }
        }
        document.addEventListener("DOMContentLoaded", function () {
            const body = document.getElementsByTagName("body")[0];
            // const cookieBanner = document.getElementsByClassName("sc-cookie-banner")[0];
            function setMarginTop () {
                // setTimeout(() => { body.style.marginTop = `${cookieBanner.scrollHeight}px` }, 0);
            }
            setMarginTop();
            // cookieBanner.addEventListener("click", setMarginTop);
            window.addEventListener("resize", setMarginTop);
        })
    </script>

    <script type="text/javascript">
        $(window).scroll(function(){
            let scTop=$(document).scrollTop()
            if(scTop>660){
                $('.ui-back-to-top').css({display:'block'})
            }else{
                $('.ui-back-to-top').css({display:'none'})
            }
        })
        $(document).ready(function () {
            $('.sc-cookie-banner').addClass("sc-cookie-banner-visible")
            $('.sc-cookie-banner .ui-button__content').click(function(){
                $('.sc-cookie-banner').removeClass("sc-cookie-banner-visible")
            })
            $('[data-sc-field-name=CountryOfResidence]').change(function () {
                $('[data-sc-field-name=Currency]').val($(this).find(':selected').data('currency'));
            });
            const glide=new Glide('.glide',{
                perView:4,
                bound:true,
                breakpoints:{
                    992:{
                        perView:3,
                    },
                    768:{
                        perView:1.5,
                    }
                }
            })
            glide.mount()
            $('.glide__arrows').css({top:"43%"})
            let prArr =$(".promo__headline.promo__content-center")
            let pr=30
            prArr.each(function(){
                if($(this).height()>pr){
                    pr=$(this).height()
                }
            })
            prArr.each(function(){
                $(this).height(pr)
            })
            $('.ui-back-to-top').click(function(){
                $('body,html').scrollTop(0)
            })
        });
    </script>

<!-- pc footer start -->
<%@ include file="/WEB-INF/view/include/footer_pc.jsp"%>
<!-- pc footer end -->

<!-- mobile footer start -->
<%@ include file="/WEB-INF/view/include/footer_m.jsp"%>
<!-- mobile footer end -->

<!-- right sidebar start -->
<%@ include file="/WEB-INF/view/include/right-sidebar.jsp"%>
<!-- right sidebar start -->
<!-- 	<div class="notice_pop">
		<div class="notice_box">
			<a href="javascript:void(0);" class="noticeCloseBtn"></a>
			<div class="con">
				<span class="imgBg"></span>
				<p>由于预订系统维护</br>
					<i>2019年2月9日19:00 - 2月10日0:00</i>网上预订暂停服务</br>
					如需预订酒店,请拨打</br>
					贵宾服务热线：<a href="tel:4001-203276">4001-203276</a></p>
			</div>
		</div>
	</div> -->
<script>
    var locale = "${locale}";
    var path = '<%=path%>';
    var templatePath = '<%=path%>/template';
</script>

<!-- 页面公用方法 -->
<script src="<%=path%>/static/js/new_common.js?v=20180427"></script>

<script src="<%=path%>/static/js/handlebars-v4.0.5.js"></script>
<script src="<%=path%>/static/js/nodetpl.js"></script>
<script src="<%=path%>/static/js/fetch-jsonp.js"></script>
<script src='<%=path%>/static/slh/common.js?v=20180426'></script>
<script src='<%=path%>/static/slh/config.js?v=20180426'></script>
<script src="<%=path%>/static/slh/handlebars-helper.js?v=20180328"></script>
<script src='<%=path%>/static/slh/destination.js?v=20170904'></script>

<script>
    // var secs = 10;
    // for(var i=1;i<=secs;i++) {
    //     window.setTimeout("update(" + i + ")", i * 1000);
    // }
    // function update(num) {
    //     if(num == secs) {
    <%--         window.location="<%=path%>/"; --%>
    // 			} else {
    // 				var printnr = secs - num;
    // 				$("#timeout").html(printnr + "秒");
    // 				console.log($("#timeout").html());
    // 			}
    // 		}
</script>
</body>
</html>
