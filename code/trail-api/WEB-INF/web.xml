<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://java.sun.com/xml/ns/javaee"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
	id="WebApp_ID" version="3.0">
	<display-name>trail-api</display-name>

	<context-param>
		<param-name>webAppRootKey</param-name>
		<param-value>trail-api</param-value>
	</context-param>
	
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			classpath:spring/applicationContext.xml,
			classpath:spring/applicationContext-datasource.xml,
			classpath:spring/applicationContext-mongo.xml,
			classpath:spring/applicationContext-task.xml
		</param-value>
	</context-param>
	
	<context-param>
		<param-name>spring.profiles.active</param-name> 
		<param-value>dev</param-value> 
	</context-param> 
	<context-param> 
		<param-name>spring.profiles.default</param-name> 
		<param-value>dev</param-value> 
	</context-param>
	<context-param> 
		<param-name>spring.liveBeansView.mbeanDomain</param-name> 
		<param-value>dev</param-value> 
	</context-param>
	
	<listener>
		<listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>
	</listener>
	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<servlet>
		<servlet-name>springmvc</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath:spring/spring-mvc.xml</param-value>
		</init-param>
		<load-on-startup>2</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>springmvc</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>

	<!-- <welcome-file-list> <welcome-file>index.html</welcome-file> <welcome-file>index.htm</welcome-file> 
		<welcome-file>index.jsp</welcome-file> <welcome-file>default.html</welcome-file> 
		<welcome-file>default.htm</welcome-file> <welcome-file>default.jsp</welcome-file> 
		</welcome-file-list> -->

	<!-- <servlet>
		<servlet-name>DruidStatView</servlet-name>
		<servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
		<init-param>
			<param-name>resetEnable</param-name>
			<param-value>true</param-value>
		</init-param>
	</servlet>
	<servlet-mapping>
		<servlet-name>DruidStatView</servlet-name>
		<url-pattern>/druid/*</url-pattern>
	</servlet-mapping>

	<filter>
		<filter-name>DruidWebStatFilter</filter-name>
		<filter-class>com.alibaba.druid.support.http.WebStatFilter</filter-class>
		<init-param>
			<param-name>exclusions</param-name>
			<param-value>*.js,*.gif,*.jpg,*.png,*.css,*.ico,/trail-api/*</param-value>
		</init-param>
		<init-param>
			<param-name>sessionStatMaxCount</param-name>
			<param-value>1000</param-value>
		</init-param>
		<init-param>
			<param-name>profileEnable</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>DruidWebStatFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping> -->

	<!-- <filter>
		<filter-name>javamelody</filter-name>
		<filter-class>net.bull.javamelody.MonitoringFilter</filter-class>
		<async-supported>true</async-supported>
	</filter>
	<filter-mapping>
		<filter-name>javamelody</filter-name>
		<url-pattern>/*</url-pattern>
		<dispatcher>REQUEST</dispatcher>
		<dispatcher>ASYNC</dispatcher>
	</filter-mapping>
	<listener>
		<listener-class>net.bull.javamelody.SessionListener</listener-class>
	</listener> -->

	<!-- 设置session失效，单位分钟 -->
	<session-config>
		<session-timeout>120</session-timeout>
	</session-config>

	<error-page>
		<error-code>404</error-code>
		<location>/WEB-INF/view/redirectError.jsp</location>
	</error-page>
	<error-page>
		<error-code>500</error-code>
		<location>/WEB-INF/view/redirectError.jsp</location>
	</error-page>

</web-app>