/*
 * Translation to pt-EU by: <PERSON> <http://jorge.simoes.com>
 */
(function ($) {
    $.mobiscroll.i18n['pt-PT'] = $.extend($.mobiscroll.i18n['pt-PT'], {
        // Core
        setText: 'Se<PERSON><PERSON>onar',
        cancelText: 'Cancelar',
        clearText: 'Clar<PERSON>',
        selectedText: '<PERSON><PERSON><PERSON><PERSON>',
        // Datetime component
        dateFormat: 'dd-mm-yy',
        dateOrder: 'ddMMyy',
        dayNames: ['Domingo','Segunda-feira','<PERSON>r<PERSON>-feira','Quarta-feira','Quinta-feira','<PERSON>ta-feira','S&aacute;bado'],
        dayNamesShort: ['Dom','Seg','Ter','Qua','Qui','Sex','S&aacute;b'],
        dayText: 'Dia',
        hourText: 'Horas',
        minuteText: 'Minutos',
        monthNames: ['Janeiro','<PERSON><PERSON>','<PERSON>&cced<PERSON>;o','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','Novem<PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
        monthNamesShort: ['Jan','Fev','Mar','Abr','Mai','Jun','Jul','Ago','Set','Out','Nov','Dez'],
        monthText: 'M&ecirc;s',
        secText: 'Segundo',
        timeFormat: 'HH:ii:ss',
        timeWheels: 'HHiiss',
        yearText: 'Ano',
        nowText: 'Actualizar',
        pmText: 'da tarde',
        amText: 'da manhã',
        // Calendar component
        dateText: 'Data',
        timeText: 'Tempo',
        calendarText: 'Calend&aacute;rio',
        closeText: 'Fechar',
        // Daterange component
        fromText: 'In&iacute;cio',
        toText: 'Fim',
        // Measurement components
        wholeText: 'Inteiro',
        fractionText: 'Frac&ccedil;&atilde;o',
        unitText: 'Unidade',
        // Time / Timespan component
        labels: ['Anos', 'Meses', 'Dias', 'Horas', 'Minutos', 'Segundos', ''],
        labelsShort: ['Ano', 'M&ecirc;s', 'Dia', 'Hora', 'Min', 'Seg', ''],
        // Timer component
        startText: 'Come&ccedil;ar',
        stopText: 'Parar',
        resetText: 'Reinicializar',
        lapText: 'Lap',
        hideText: 'Esconder'
    });
})(jQuery);
