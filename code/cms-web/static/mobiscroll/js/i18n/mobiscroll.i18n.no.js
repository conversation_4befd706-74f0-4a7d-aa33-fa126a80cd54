(function ($) {
    $.mobiscroll.i18n.no = $.extend($.mobiscroll.i18n.no, {
        // Core
        setText: 'OK',
        cancelText: 'Avbryt',
        clearText: 'Tømme',
        selectedText: 'Valgt',
        // Datetime component
        dateFormat: 'dd.mm.yy',
        dateOrder: 'ddmmyy',
        dayNames: ['Søndag', 'Mandag', 'Tirsdag', 'Onsdag', 'Torsdag', 'Fredag', '<PERSON><PERSON>rdag'],
        dayNamesShort: ['Sø', 'Ma', 'Ti', 'On', 'To', 'Fr', 'Lø'],
        dayText: 'Dag',
        hourText: 'Time',
        minuteText: 'Minutt',
        monthNames: ['Januar', 'Februar', 'Mars', 'April', 'Mai', 'Juni', 'Juli', 'August', 'September', 'Oktober', 'November', 'Desember'],
        monthNamesShort: ['Jan', 'Feb', '<PERSON>', 'Apr', '<PERSON>', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', '<PERSON>'],
        monthText: 'Må<PERSON>',
        secText: 'Sekund',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: 'År',
        nowText: 'Nå',
        pmText: 'pm',
        amText: 'am',
        // Calendar component
        dateText: 'Dato',
        timeText: 'Tid',
        calendarText: 'Kalender',
        closeText: 'Lukk',
        // Daterange component
        fromText: 'Start',
        toText: 'End',
        // Measurement components
        wholeText: 'Hele',
        fractionText: 'Fraksjon',
        unitText: 'Enhet',
        // Time / Timespan component
        labels: ['År', 'Måneder', 'Dager', 'Timer', 'Minutter', 'Sekunder', ''],
        labelsShort: ['Yrs', 'Mths', 'Days', 'Hrs', 'Mins', 'Secs', ''],
        // Timer component
        startText: 'Start',
        stopText: 'Stopp',
        resetText: 'Tilbakestille',
        lapText: 'Runde',
        hideText: 'Skjul'
    });
})(jQuery);
