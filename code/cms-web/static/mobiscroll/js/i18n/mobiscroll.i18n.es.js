(function ($) {
    $.mobiscroll.i18n.es = $.extend($.mobiscroll.i18n.es, {
        // Core
        setText: 'Aceptar',
        cancelText: 'Cancelar',
        clearText: '<PERSON>lar<PERSON>',
        selectedText: 'Sele<PERSON>ona<PERSON>',
        // Datetime component
        dateFormat: 'dd/mm/yy',
        dateOrder: 'ddmmyy',
        dayNames: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','Mi&#xE9;rcoles','<PERSON><PERSON>','Viernes','S&#xE1;bado'],
        dayNamesShort: ['Do','Lu','Ma','Mi','Ju','Vi','S&#xE1;'],
        dayText: 'D&#237;a',
        hourText: 'Horas',
        minuteText: 'Minutos',
        monthNames: ['Enero','Febrero','Marzo','Abril','Mayo','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>','Septiembre','Octubre','<PERSON><PERSON><PERSON>','Dicie<PERSON>'],
        monthNamesShort: ['En<PERSON>','Feb','<PERSON>','Abr','May','Jun','Jul','A<PERSON>','Sep','Oct','Nov','Dic'],
        monthText: 'Mes',
        secText: 'Segundos',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: 'A&ntilde;o',
        nowText: 'Ahora',
        pmText: 'pm',
        amText: 'am',
        // Calendar component
        dateText: 'Fecha',
        timeText: 'Tiempo',
        calendarText: 'Calendario',
        closeText: 'Cerrar',
        // Daterange component
        fromText: 'Iniciar',
        toText: 'Final',
        // Measurement components
        wholeText: 'Entero',
        fractionText: 'Fracción',
        unitText: 'Unidad',
        // Time / Timespan component
        labels: ['Años', 'Meses', 'Días', 'Horas', 'Minutos', 'Segundos', ''],
        labelsShort: ['Yrs', 'Mths', 'Days', 'Hrs', 'Mins', 'Secs', ''],
        // Timer component
        startText: 'Iniciar',
        stopText: 'Deténgase',
        resetText: 'Reinicializar',
        lapText: 'Lap',
        hideText: 'Esconder'
    });
})(jQuery);
