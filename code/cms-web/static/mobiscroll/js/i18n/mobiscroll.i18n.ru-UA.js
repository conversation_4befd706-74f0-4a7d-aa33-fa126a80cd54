(function ($) {
    $.mobiscroll.i18n['ru-UA'] = $.extend($.mobiscroll.i18n['ru-UA'], {
        // Core
        setText: 'Установить',
        cancelText: 'Отменить',
        clearText: 'Очиститьr',
        selectedText: 'Вібрать',
        // Datetime component
        dateFormat: 'dd.mm.yy',
        dateOrder: 'ddmmy',
        dayNames: ['воскресенье', 'понедельник', 'вторник', 'среда', 'четверг', 'пятница', 'суббота'],
        dayNamesShort: ['вс', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],
        dayText: 'День',
        hourText: 'Часы',
        minuteText: 'Минуты',
        monthNames: ['Январь', 'Февраль', 'Март', 'Апрель', 'Май', 'Июнь', 'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь'],
        monthNamesShort: ['Янв.', 'Февр.', 'Март', 'Апр.', 'Май', 'Июнь', 'Июль', 'Авг.', 'Сент.', 'Окт.', 'Нояб.', 'Дек.'],
        monthText: 'Месяцы',
        secText: 'Сикунды',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: 'Год',
        nowText: 'Сейчас',
        amText: 'До полудня',
        pmText: 'После полудня',
        // Calendar component
        dateText: 'Дата',
        timeText: 'Время',
        calendarText: 'Календарь',
        closeText: 'Закрыть',
        // Daterange component
        fromText: 'Начало',
        toText: 'Конец',
        // Measurement components
        wholeText: 'Весь',
        fractionText: 'Часть',
        unitText: 'Единица',
        // Time / Timespan component
        labels: ['Годы', ' Месяцы ', ' Дни ', ' Часы ', ' Минуты ', ' Секунды', ''],
        labelsShort: ['Год', 'Мес.', 'Дн.', 'Ч.', 'Мин.', 'Сек.', ''],
        // Timer component
        startText: 'Старт',
        stopText: 'Стоп',
        resetText: ' Сброс ',
        lapText: ' Этап ',
        hideText: ' Скрыть '
    });
})(jQuery);
