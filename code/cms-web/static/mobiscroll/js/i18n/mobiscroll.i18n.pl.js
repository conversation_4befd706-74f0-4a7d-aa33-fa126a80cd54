(function ($) {
    $.mobiscroll.i18n.pl = $.extend($.mobiscroll.i18n.pl, {
      // Core
        setText: '<PERSON>estaw',
        cancelText: 'Anuluj',
        clearText: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        selectedText: 'Wyb<PERSON>r',
        // Datetime component
        dateFormat: 'yy-mm-dd',
        dateOrder: 'ddmmy',
        dayNames: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Piątek', '<PERSON><PERSON>a'],
        dayNamesShort: ['Niedz.', 'Pon.', 'Wt.', 'Śr.', 'Czw.', 'Pt.', 'Sob.'],
        dayText: '<PERSON><PERSON><PERSON>',
        hourText: 'Godziny',
        minuteText: 'Minuty',
        monthNames: ['<PERSON>ycze<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>op<PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
        monthNamesShort: ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>ze', 'Lip', 'Sie', 'Wrz', 'Paź', 'Lis', 'Gru'],
        monthText: 'Miesiąc',
        secText: 'Sekundy',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: 'Rok',
        nowText: 'Teraz',
        amText: 'rano',
        pmText: 'po południu',
        // Calendar component
        dateText: 'Data',
        timeText: 'Czas',
        calendarText: 'Kalendarz',
        closeText: 'Zakończenie',
        // Daterange component
        fromText: 'Rozpoczęcie',
        toText: 'Koniec',
        // Measurement components
        wholeText: 'Cały',
        fractionText: 'Ułamek',
        unitText: 'Jednostka',
        // Time / Timespan component
        labels: ['Lata', 'Miesiąc', 'Dni', 'Godziny', 'Minuty', 'Sekundy', ''],
        labelsShort: ['R', 'M', 'Dz', 'Godz', 'Min', 'Sek', ''],
        // Timer component
        startText: 'Rozpoczęcie',
        stopText: 'Zatrzymać',
        resetText: 'Zresetować',
        lapText: 'Zakładka',
        hideText: 'Ukryć'
    });
})(jQuery);
