(function ($) {
    $.mobiscroll.i18n.it = $.extend($.mobiscroll.i18n.it, {
        // Core
        setText: 'OK',
        cancelText: '<PERSON><PERSON><PERSON>',
        clearText: '<PERSON><PERSON><PERSON>',
        selectedText: 'Selezionato',
        // Datetime component
        dateFormat: 'dd-mm-yyyy',
        dateOrder: 'ddmmyy',
        dayNames: ['Domenica', '<PERSON>ned&Igrave;', '<PERSON><PERSON>&Igrave;', '<PERSON><PERSON>ole<PERSON>&Igrave;', '<PERSON><PERSON><PERSON>&Igrave;', '<PERSON><PERSON><PERSON>&Igrave;', '<PERSON><PERSON><PERSON>'],
        dayNamesShort: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
        dayText: '<PERSON>ior<PERSON>',
        hourText: 'Ore',
        minuteText: 'Minuti',
        monthNames: ['Gennaio', 'Febbraio', 'Mar<PERSON>', 'Aprile', 'Ma<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>em<PERSON>', 'Di<PERSON><PERSON>'],
        monthNamesShort: ['Gen', 'Feb', 'Mar', 'Apr', 'Mag', 'Giu', 'Lug', 'A<PERSON>', '<PERSON>', 'O<PERSON>', 'Nov', 'Dic'],
        monthText: 'Mese',
        secText: 'Secondi',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: 'Anno',
        nowText: 'Ora',
        pmText: 'pm',
        amText: 'am',
        // Calendar component
        dateText: 'Data',
        timeText: 'Volta',
        calendarText: 'Calendario',
        closeText: 'Chiudere',
        // Daterange component
        fromText: 'Inizio',
        toText: 'Fine',
        // Measurement components
        wholeText: 'Intero',
        fractionText: 'Frazione',
        unitText: 'Unità',
        // Time / Timespan component
        labels: ['Anni', 'Mesi', 'Giorni', 'Ore', 'Minuti', 'Secondi', ''],
        labelsShort: ['Yrs', 'Mths', 'Days', 'Hrs', 'Mins', 'Secs', ''],
        // Timer component
        startText: 'Inizio',
        stopText: 'Arresto',
        resetText: 'Ripristina',
        lapText: 'Lap',
        hideText: 'Nascondi'
    });
})(jQuery);
