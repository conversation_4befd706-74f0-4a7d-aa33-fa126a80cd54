(function ($) {
    $.mobiscroll.i18n.ro = $.extend($.mobiscroll.i18n.ro, {
        // Core
        setText: 'Setare',
        cancelText: 'Anular<PERSON>',
        clearText: '<PERSON><PERSON><PERSON><PERSON>',
        selectedText: 'Selectat',
        // Datetime component
        dateFormat: 'dd.mm.yy',
        dateOrder: 'ddmmy',
        dayNames: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'],
        dayNamesShort: ['Du', '<PERSON>', 'Ma', 'Mi', 'Jo', 'Vi', 'S<PERSON>'],
        dayText: ' Ziua',
        hourText: ' Ore ',
        minuteText: 'Minute',
        monthNames: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>e', '<PERSON><PERSON>', 'Aprilie', 'Mai', 'Iunie', 'Iulie', 'August', 'Septembrie', 'Octombrie', '<PERSON><PERSON><PERSON><PERSON>', 'Decembrie'],
        monthNamesShort: ['Ian.', 'Feb.', 'Mar.', 'Apr.', 'Mai', 'Iun.', 'Iul.', 'Aug.', 'Sept.', 'Oct.', 'Nov.', 'Dec.'],
        monthText: 'Luna',
        secText: 'Secunde',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: 'Anul',
        nowText: 'Acum',
        amText: 'am',
        pmText: 'pm',
        // Calendar component
        dateText: 'Data',
        timeText: 'Ora',
        calendarText: 'Calendar',
        closeText: 'Închidere',
        // Daterange component
        fromText: 'Start',
        toText: 'Final',
        // Measurement components
        wholeText: 'Complet',
        fractionText: 'Parţial',
        unitText: 'Unitate',
        // Time / Timespan component
        labels: ['Ani', 'Luni', 'Zile', 'Ore', 'Minute', 'Secunde', ''],
        labelsShort: ['Ani', 'Luni', 'Zile', 'Ore', 'Min.', 'Sec.', ''],
        // Timer component
        startText: 'Start',
        stopText: 'Stop',
        resetText: 'Resetare',
        lapText: 'Tură',
        hideText: 'Ascundere'
    });
})(jQuery);
