(function ($) {
    $.mobiscroll.i18n.sv = $.extend($.mobiscroll.i18n.sv, {
        // Core
        setText: 'OK',
        cancelText: 'Avbryt',
        clearText: '<PERSON><PERSON><PERSON>',
        selectedText: 'Vald',
        // Datetime component
        dateFormat: 'yy-mm-dd',
        dateOrder: 'yymmdd',
        dayNames: ['<PERSON>öndag', 'Måndag', 'Tisdag', 'Onsdag', 'Torsdag', 'Fred<PERSON>', '<PERSON><PERSON>rdag'],
        dayNamesShort: ['Sö', 'Må', 'Ti', 'On', 'To', 'Fr', 'Lö'],
        dayText: 'Dag',
        hourText: 'Tim<PERSON>',
        minuteText: 'Minut',
        monthNames: ['<PERSON><PERSON>ri', 'Febru<PERSON>', '<PERSON>', 'April', 'Maj', 'Juni', 'Juli', 'Augusti', 'September', 'Okto<PERSON>', 'November', 'December'],
        monthNamesShort: ['Jan', 'Feb', '<PERSON>', 'Apr', 'Maj', 'Jun', 'Jul', 'Aug', '<PERSON>', 'Okt', 'Nov', 'Dec'],
        monthText: '<PERSON><PERSON>na<PERSON>',
        secText: 'Sekund',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: 'År',
        nowText: 'Nu',
        pmText: 'pm',
        amText: 'am',
        // Calendar component
        dateText: 'Datum',
        timeText: 'Tid',
        calendarText: 'Kalender',
        closeText: 'Stäng',
        // Daterange component
        fromText: 'Start',
        toText: 'Slut',
        // Measurement components
        wholeText: 'Hela',
        fractionText: 'Bråk',
        unitText: 'Enhet',
        // Time / Timespan component
        labels: ['År', 'Månader', 'Dagar', 'Timmar', 'Minuter', 'Sekunder', ''],
        labelsShort: ['År', 'Mån', 'Dag', 'Tim', 'Min', 'Sek', ''],
        // Timer component
        startText: 'Start',
        stopText: 'Stopp',
        resetText: 'Återställ',
        lapText: 'Varv',
        hideText: 'Dölj'
    });
})(jQuery);