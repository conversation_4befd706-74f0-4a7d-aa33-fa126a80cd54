(function ($) {
    $.mobiscroll.i18n.ja = $.extend($.mobiscroll.i18n.ja, {
        // Core
        setText: 'セット',
        cancelText: 'キャンセル',
        clearText: 'クリア',
        selectedText: '選択',
        // Datetime component
        dateFormat: 'yy年mm月dd日',
        dateOrder: 'yymmdd',
        dayNames: ['日','月','火','水','木','金','土'],
        dayNamesShort: ['日','月','火','水','木','金','土'],
        dayText: '日',
        hourText: '時',
        minuteText: '分',
        monthNames: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
        monthNamesShort: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
        monthText: '月',
        secText: '秒',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: '年',
        nowText: '今',
        pmText: '午後',
        amText: '午前',
        yearSuffix: '年',
        monthSuffix: '月',
        daySuffix: '日',
        // Calendar component
        dateText: '日付',
        timeText: '時間',
        calendarText: 'カレンダー',
        closeText: 'クローズ',
        // Daterange component
        fromText: '開始',
        toText: '終わり',
        // Measurement components
        wholeText: '全数',
        fractionText: '分数',
        unitText: '単位',
        // Time / Timespan component
        labels: ['年間', '月間', '日間', '時間', '分', '秒', ''],
        labelsShort: ['年間', '月間', '日間', '時間', '分', '秒', ''],
        // Timer component
        startText: '開始',
        stopText: '停止',
        resetText: 'リセット',
        lapText: 'ラップ',
        hideText: '隠す'
    });
})(jQuery);
