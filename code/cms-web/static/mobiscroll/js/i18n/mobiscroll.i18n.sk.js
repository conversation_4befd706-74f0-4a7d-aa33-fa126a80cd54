(function ($) {
    $.mobiscroll.i18n.sk = $.extend($.mobiscroll.i18n.sk, {
        // Core
        setText: '<PERSON>adaj',
        cancelText: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        clearText: 'Vymaz<PERSON><PERSON>',
        selectedText: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        // Datetime component
        dateFormat: 'dd/mm/yy',
        dateOrder: 'ddmmy',
        dayNames: ['Nedeľa', 'Pondelok', 'U<PERSON><PERSON>', '<PERSON><PERSON>a', 'Štvrtok', '<PERSON>ato<PERSON>', 'Sobota'],
        dayNamesShort: ['Ne', 'Po', 'Ut', 'St', 'Št', 'Pi', 'So'],
        dayText: 'Ďeň',
        hourText: 'Hodiny',
        minuteText: '<PERSON><PERSON><PERSON>',
        monthNames: ['Janu<PERSON>r', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Apríl', 'Máj', '<PERSON><PERSON>', '<PERSON><PERSON>', 'August', 'September', 'Okt<PERSON><PERSON>', 'November', 'December'],
        monthNamesShort: ['<PERSON>', 'Feb', 'Mar', 'Apr', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Aug', '<PERSON>', 'Okt', 'Nov', 'Dec'],
        monthText: 'Mesiac',
        secText: 'Sekundy',
        timeFormat: 'hh:ii A',
        timeWheels: 'hhiiA',
        yearText: 'Rok',
        nowText: 'Teraz',
        amText: 'am',
        pmText: 'pm',
        // Calendar component
        dateText: 'Datum',
        timeText: 'Čas',
        calendarText: 'Kalendár',
        closeText: 'Zavrieť',
        // Daterange component
        fromText: 'Začiatok',
        toText: 'Koniec',
        // Measurement components
        wholeText: 'Celý',
        fractionText: 'Časť',
        unitText: 'Jednotka',
        // Time / Timespan component
        labels: ['Roky', 'Mesiace', 'Dni', 'Hodiny', 'Minúty', 'Sekundy', ''],
        labelsShort: ['Rok', 'Mes', 'Dni', 'Hod', 'Min', 'Sec', ''],
        // Timer component
        startText: 'Start',
        stopText: 'Stop',
        resetText: 'Resetovať',
        lapText: 'Etapa',
        hideText: 'Schovať'
    });
})(jQuery);
