/*
 * Translation by: <PERSON> <<EMAIL>>
 */
(function ($) {
    $.mobiscroll.i18n['pt-BR'] = $.extend($.mobiscroll.i18n['pt-BR'], {
        // Core
        setText: 'Selecionar',
        cancelText: 'Cancelar',
        clearText: '<PERSON><PERSON><PERSON>',
        selectedText: '<PERSON><PERSON><PERSON><PERSON>',
        // Datetime component
        dateFormat: 'dd/mm/yy',
        dateOrder: 'ddMMyy',
        dayNames: ['Domingo','Segunda-feira','<PERSON>r<PERSON>-feira','Quarta-feira','Quinta-feira','<PERSON>ta-feira','Sábad<PERSON>'],
        dayNamesShort: ['Dom','Seg','Ter','Qua','Qui','Sex','Sáb'],
        dayText: 'Dia',
        hourText: 'Hora',
        minuteText: 'Minutos',
        monthNames: ['Janeiro','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','A<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
        monthNamesShort: ['<PERSON>','Fev','Mar','Abr','Mai','Jun','Jul','Ago','Set','Out','Nov','Dez'],
        monthText: 'Mês',
        secText: 'Segundo',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: 'Ano',
        nowText: 'Agora',
        pmText: 'da tarde',
        amText: 'da manhã',
        // Calendar component
        dateText: 'Data',
        timeText: 'Tempo',
        calendarText: 'Calendário',
        closeText: 'Fechar',
        // Daterange component
        fromText: 'In&iacute;cio',
        toText: 'Fim',
        // Measurement components
        wholeText: 'Inteiro',
        fractionText: 'Fração',
        unitText: 'Unidade',
        // Time / Timespan component
        labels: ['Anos', 'Meses', 'Dias', 'Horas', 'Minutos', 'Segundos', ''],
        labelsShort: ['Yrs', 'Mths', 'Days', 'Hrs', 'Mins', 'Secs', ''],
        // Timer component
        startText: 'Começar',
        stopText: 'Pare',
        resetText: 'Reinicializar',
        lapText: 'Lap',
        hideText: 'Esconder'
    });
})(jQuery);
