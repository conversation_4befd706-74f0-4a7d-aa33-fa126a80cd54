(function ($) {
    $.mobiscroll.i18n.fr = $.extend($.mobiscroll.i18n.fr, {
        // Core
        setText: 'Termin<PERSON>',
        cancelText: 'Annuler',
        clearText: 'Effacer',
        selectedText: 'Sélectionné',
        // Datetime component
        dateFormat: 'dd/mm/yy',
        dateOrder: 'ddmmyy',
        dayNames: ['&#68;imanche', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],
        dayNamesShort: ['&#68;im.', 'Lun.', 'Mar.', 'Mer.', 'Jeu.', 'Ven.', 'Sam.'],
        dayText: 'Jour',
        monthText: 'Mois',
        monthNames: ['Jan<PERSON>', '<PERSON>évrier', '<PERSON>', 'Avril', '<PERSON>', 'Juin', '<PERSON><PERSON><PERSON>', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'],
        monthNamesShort: ['Janv.', 'Févr.', '<PERSON>', 'Avril', '<PERSON>', 'Juin', 'Juil.', 'Août', 'Sept.', 'Oct.', 'Nov.', 'Déc.'],
        hourText: 'Heures',
        minuteText: 'Minutes',
        secText: 'Secondes',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: 'Année',
        nowText: 'Maintenant',
        pmText: 'après-midi',
        amText: 'avant-midi',
        // Calendar component
        dateText: 'Date',
        timeText: 'Heure',
        calendarText: 'Calendrier',
        closeText: 'Fermer',
        // Daterange component
        fromText: 'Démarrer',
        toText: 'Fin',
        // Measurement components
        wholeText: 'Entier',
        fractionText: 'Fraction',
        unitText: 'Unité',
        // Time / Timespan component
        labels: ['Ans', 'Mois', 'Jours', 'Heures', 'Minutes', 'Secondes', ''],
        labelsShort: ['Yrs', 'Mths', 'Days', 'Hrs', 'Mins', 'Secs', ''],
        // Timer component
        startText: 'Démarrer',
        stopText: 'Arrêter',
        resetText: 'Réinitialiser',
        lapText: 'Lap',
        hideText: 'Cachez'
    });
})(jQuery);
