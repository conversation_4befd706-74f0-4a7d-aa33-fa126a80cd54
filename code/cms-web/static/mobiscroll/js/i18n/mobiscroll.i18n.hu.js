(function ($) {
    $.mobiscroll.i18n.hu = $.extend($.mobiscroll.i18n.hu, {
        // Core
        setText: 'OK',
        cancelText: '<PERSON>é<PERSON><PERSON>',
        clearText: 'Törl<PERSON>',
        selectedText: 'Ki<PERSON><PERSON>lasztva',
        // Datetime component
        dateFormat: 'yy.mm.dd.',
        dateOrder: 'yymmdd',
        dayNames: ['Vas<PERSON>rna<PERSON>', '<PERSON><PERSON>tf<PERSON>', 'Kedd', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>rt<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
        dayNamesShort: ['Va', 'Hé', 'Ke', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'P<PERSON>', '<PERSON><PERSON>'],
        dayText: 'Nap',
        hourText: '<PERSON><PERSON>',
        minuteText: 'Perc',
        monthNames: ['Janu<PERSON>r', 'Február', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>t<PERSON><PERSON>', 'November', 'December'],
        monthNamesShort: ['<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Aug', 'Szep', 'Okt', 'Nov', 'Dec'],
        monthText: 'Hónap',
        secText: 'Másodperc',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: 'Év',
        nowText: 'Most',
        pmText: 'de',
        amText: 'du',
        // Calendar component
        firstDay: 1,
        dateText: 'Dátum',
        timeText: 'Idő',
        calendarText: 'Naptár',
        closeText: 'Bezár',
        // Daterange component
        fromText: 'Eleje',
        toText: 'Vége',
        // Measurement components
        wholeText: 'Egész',
        fractionText: 'Tört',
        unitText: 'Egység',
        // Time / Timespan component
        labels: ['Év', 'Hónap', 'Nap', 'Óra', 'Perc', 'Másodperc', ''],
        labelsShort: ['Év', 'Hó.', 'Nap', 'Óra', 'Perc', 'Mp.', ''],
        // Timer component
        startText: 'Indít',
        stopText: 'Megállít',
        resetText: 'Visszaállít',
        lapText: 'Lap',
        hideText: 'Elrejt'
    });
})(jQuery);
