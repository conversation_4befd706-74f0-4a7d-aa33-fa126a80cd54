(function ($) {
    $.mobiscroll.i18n.cs = $.extend($.mobiscroll.i18n.cs, {
        // Core
        setText: '<PERSON>ade<PERSON>',
        cancelText: '<PERSON>orno',
        clearText: 'Vymazat',
        selectedText: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        // Datetime component
        dateFormat: 'dd/mm/yy',
        dateOrder: 'ddmmy',
        dayNames: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>tv<PERSON><PERSON>', 'P<PERSON>tek', '<PERSON>bot<PERSON>'],
        dayNamesShort: ['Ne', 'Po', 'Út', 'St', 'Čt', 'P<PERSON>', 'So'],
        dayText: 'Den',
        hourText: 'Hodiny',
        minuteText: 'Minuty',
        monthNames: ['<PERSON>en', '<PERSON>nor', 'B<PERSON>ezen', '<PERSON>en', 'Květen', 'Červen', 'Červenec', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Říjen', 'Listopad', 'Prosinec'],
        monthNamesShort: ['Led', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>vc', '<PERSON>p<PERSON>', '<PERSON><PERSON><PERSON>', 'Říj', '<PERSON>s', 'Pro'],
        monthText: 'Měsíc',
        secText: 'Sekundy',
        timeFormat: 'hh:ii A',
        timeWheels: 'hhiiA',
        yearText: 'Rok',
        nowText: 'Teď',
        amText: 'am',
        pmText: 'pm',
        // Calendar component
        dateText: 'Datum',
        timeText: 'Čas',
        calendarText: 'Kalendář',
        closeText: 'Zavřít',
        // Daterange component
        fromText: 'Začátek',
        toText: 'Konec',
        // Measurement components
        wholeText: 'Celý',
        fractionText: 'Část',
        unitText: 'Jednotka',
        // Time / Timespan component
        labels: ['Roky', 'Měsíce', 'Dny', 'Hodiny', 'Minuty', 'Sekundy', ''],
        labelsShort: ['Rok', 'Měs', 'Dny', 'Hod', 'Min', 'Sec', ''],
        // Timer component
        startText: 'Start',
        stopText: 'Stop',
        resetText: 'Resetovat',
        lapText: 'Etapa',
        hideText: 'Schovat'
    });
})(jQuery);
