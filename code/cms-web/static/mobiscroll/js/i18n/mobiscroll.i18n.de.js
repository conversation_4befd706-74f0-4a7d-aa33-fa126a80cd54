(function ($) {
    $.mobiscroll.i18n.de = $.extend($.mobiscroll.i18n.de, {
        // Core
        setText: 'OK',
        cancelText: 'Abbrechen',
        clearText: '<PERSON><PERSON><PERSON>',
        selectedText: 'Ausgewählt',
        // Datetime component
        dateFormat: 'dd.mm.yy',
        dateOrder: 'ddmmyy',
        dayNames: ['Sonnta<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Mitt<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Freitag', 'Samstag'],
        dayNamesShort: ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],
        dayText: 'Tag',
        hourText: 'Stunde',
        minuteText: 'Minuten',
        monthNames: ['Januar', 'Februar', '<PERSON><PERSON><PERSON>', 'April', 'Mai', 'Juni', 'Juli', 'August', 'September', 'Okto<PERSON>', 'November', 'Dezember'],
        monthNamesShort: ['Jan', 'Feb', '<PERSON><PERSON><PERSON>', 'Apr', '<PERSON>', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dez'],
        monthText: 'Monat',
        secText: 'Sekunden',
        timeFormat: 'HH:ii',
        timeWheels: 'HHii',
        yearText: 'Jahr',
        nowText: 'Jetzt',
        pmText: 'nachm.',
        amText: 'vorm.',
        // Calendar component
        dateText: 'Datum',
        timeText: 'Zeit',
        calendarText: 'Kalender',
        closeText: 'Schließen',
        // Daterange component
        fromText: 'Von',
        toText: 'Um',
        // Measurement components
        wholeText: 'Ganze Zahl',
        fractionText: 'Bruchzahl',
        unitText: 'Maßeinheit',
        // Time / Timespan component
        labels: ['Jahre', 'Monate', 'Tage', 'Stunden', 'Minuten', 'Sekunden', ''],
        labelsShort: ['Yrs', 'Mths', 'Days', 'Hrs', 'Mins', 'Secs', ''],
        // Timer component
        startText: 'Starten',
        stopText: 'Stoppen',
        resetText: 'Zurücksetzen',
        lapText: 'Lap',
        hideText: 'Ausblenden'
    });
})(jQuery);
