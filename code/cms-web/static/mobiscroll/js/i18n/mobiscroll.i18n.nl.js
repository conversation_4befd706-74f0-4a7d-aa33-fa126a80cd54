(function ($) {
    $.mobiscroll.i18n.nl = $.extend($.mobiscroll.i18n.nl, {
        // Core
        setText: 'Instellen',
        cancelText: 'Annuleren',
        clearText: '<PERSON><PERSON>li<PERSON>',
        selectedText: 'Gekozen',
        // Datetime component
        dateFormat: 'dd/mm/yy',
        dateOrder: 'ddmmyy',
        dayNames: ['zondag', 'maandag', 'Dinsdag', 'Woensdag', 'Donderdag', 'Vrijdag', 'Zaterdag'],
        dayNamesShort: ['zo', 'ma', 'di', 'wo', 'do', 'vr', 'za'],
        dayText: 'Dag',
        hourText: 'Uur',
        minuteText: 'Minuten',
        monthNames: ['januari', 'februari', 'maart', 'april', 'mei', 'juni', 'juli', 'augustus', 'september', 'oktober', 'november', 'december'],
        monthNamesShort: ['jan', 'feb', 'mrt', 'apr', 'mei', 'jun', 'jul', 'aug', 'sep', 'okt', 'nov', 'dec'],
        monthText: 'Maand',
        secText: 'Seconden',
        timeFormat: 'HH:ii',
        timeWheels: 'hhii',
        yearText: 'Jaar',
        nowText: 'Nu',
        pmText: 'pm',
        amText: 'am',
        // Calendar component
        dateText: 'Datum',
        timeText: 'Tijd',
        calendarText: 'Kalender',
        closeText: 'Sluiten',
        // Daterange component
        fromText: 'Start',
        toText: 'Einde',
        // Measurement components
        wholeText: 'geheel',
        fractionText: 'fractie',
        unitText: 'eenheid',
        // Time / Timespan component
        labels: ['Jaren', 'Maanden', 'Dagen', 'Uren', 'Minuten', 'Seconden', ''],
        labelsShort: ['j', 'm', 'd', 'u', 'min', 'sec', ''],
        // Timer component
        startText: 'Start',
        stopText: 'Stop',
        resetText: 'Reset',
        lapText: 'Ronde',
        hideText: 'Verbergen'
    });
})(jQuery);
