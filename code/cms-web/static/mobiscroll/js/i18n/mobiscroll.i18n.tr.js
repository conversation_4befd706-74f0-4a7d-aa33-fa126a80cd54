(function ($) {
    $.mobiscroll.i18n.tr = $.extend($.mobiscroll.i18n.tr, {
        // Core
        setText: 'Se<PERSON>',
        cancelText: '<PERSON>ptal',
        clearText: '<PERSON><PERSON><PERSON>yin',
        selectedText: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        // Datetime component
        dateFormat: 'dd.mm.yy',
        dateOrder: 'ddmmyy',
        dayNames: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
        dayNamesShort: ['<PERSON>', 'Pzt', '<PERSON>', '<PERSON>ar', 'Per', 'Cum', 'Cmt'],
        dayText: 'Gün',
        hourText: 'Saat',
        minuteText: '<PERSON><PERSON><PERSON>',
        monthNames: ['<PERSON>cak', '<PERSON>ubat', '<PERSON>', '<PERSON>san', 'Mayıs', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>uz', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
        monthNamesShort: ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', 'May', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Kas', 'Ara'],
        monthText: 'Ay',
        secText: 'Saniye',
        timeFormat: 'hh:ii A',
        timeWheels: 'hhiiA',
        yearText: 'Yıl',
        nowText: 'Şimdi',
        pmText: 'akşam',
        amText: 'sabah',
        // Calendar component
        dateText: 'Tarih',
        timeText: 'Zaman',
        calendarText: 'Takvim',
        closeText: 'Kapatmak',
        // Daterange component
        fromText: 'Başla',
        toText: 'Son',
        // Measurement components
        wholeText: 'Tam',
        fractionText: 'Kesir',
        unitText: 'Birim',
        // Time / Timespan component
        labels: ['Yıl', 'Ay', 'Gün', 'Saat', 'Dakika', 'Saniye', ''],
        labelsShort: ['Yıl', 'Ay', 'Gün', 'Sa', 'Dak', 'Sn', ''],
        // Timer component
        startText: 'Başla',
        stopText: 'Durdur',
        resetText: 'Sıfırla',
        lapText: 'Tur',
        hideText: 'Gizle'
    });
})(jQuery);
