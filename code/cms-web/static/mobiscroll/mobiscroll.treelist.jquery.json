{"name": "mobiscroll.treelist", "title": "Mobiscroll Treelist <PERSON><PERSON><PERSON>", "description": "Renders a scrollable hierarchical list of dynamic depth for easy data picking on touch devices. It comes with theming capability, optional jQuery Mobile integration and multiple display modes: Inline, Modal, Bubble, Docked Top & Bottom.", "keywords": ["ui", "input", "form", "html5", "mobile", "list", "hierarchy", "scroller", "clickpick"], "version": "2.11.0", "author": {"name": "Acid Media", "url": "http://theacidmedia.net"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/acidb"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/dioslaska"}, {"name": "<PERSON><PERSON><PERSON>", "email": "a<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/andrasz"}], "licenses": [{"type": "MIT", "url": "https://github.com/acidb/mobiscroll/blob/master/MITLicense.txt"}], "bugs": "https://github.com/acidb/mobiscroll/issues", "homepage": "http://mobiscroll.com/component/list", "docs": "http://docs.mobiscroll.com/list", "demo": "http://demo.mobiscroll.com/list", "download": "http://mobiscroll.com/component/list", "dependencies": {"jquery": ">=1.7"}}