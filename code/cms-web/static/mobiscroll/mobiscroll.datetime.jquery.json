{"name": "mobiscroll.datetime", "title": "Mobiscroll Date & Time Scroller", "description": "Renders a scrollable Date, Time or Date & Time Picker for easy date entry on touch devices. It comes with theming capability, optional jQuery Mobile integration and multiple display modes: Inline, Modal, Bubble, Docked Top & Bottom.", "keywords": ["ui", "input", "form", "html5", "datepicker", "timepicker", "touchscreen", "mobile", "scroller", "clickpick"], "version": "2.11.0", "author": {"name": "Acid Media", "url": "http://theacidmedia.net"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/acidb"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/dioslaska"}], "licenses": [{"type": "MIT", "url": "https://github.com/acidb/mobiscroll/blob/master/MITLicense.txt"}], "bugs": "https://github.com/acidb/mobiscroll/issues", "homepage": "http://mobiscroll.com/component/datetime", "docs": "http://docs.mobiscroll.com/datetime", "demo": "http://demo.mobiscroll.com/datetime", "download": "http://mobiscroll.com/component/datetime", "dependencies": {"jquery": ">=1.7"}}