/* Android Skin */
.android .dw {
    border: 2px solid #555;
}
.android .dwv {
    padding: 10px;
    margin: 0;
    background: none;
    border-bottom: 1px solid #333;
    -webkit-border-radius: 0;
    border-radius: 0;
}
.android .dwwr {
    padding: 0;
    color: #fff;
    background: #000;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
}
.android .dwc {
    padding: 32px 2px 6px 2px;
    display: inline-block;
}
.android .dwhl {
    padding-top: 6px;
}
.android .dw .dwpm .dww,
.android .dw .dwpm .dwwl {
    border: 0;
    background: #fff;
}
.android .dwwl {
    margin: 0 2px;
}
.android .dw .dwpm .dww {
    margin: 0;
}
.android .dwpm .dww .dw-li {
    color: #000;
    text-shadow: none;
}
.android .dwbc {
    padding: 4px 2px;
    background: #9c9c9c;
}
.android .dw .dwwr .dwb {
    background: #ccc;
    background: -webkit-gradient(linear,left bottom,left top,from(#ccc),to(#eee));
    background: -webkit-linear-gradient(#eee,#ccc);
    background: -moz-linear-gradient(#eee,#ccc);
    background: linear-gradient(#eee,#ccc);
    color: #000;
    font-weight: normal;
    text-shadow: none;
    -webkit-border-radius: 0;
    border-radius: 0;
}
.android .dw .dwwr .dwb-a, .android .dw .dwwl .dwb-a {
    background: #ffb25a;
    background: -webkit-gradient(linear,left bottom,left top,from(#ef6100),to(#ffb25a));
    background: -webkit-linear-gradient(#ffb25a,#ef6100);
    background: -moz-linear-gradient(#ffb25a,#ef6100);
    background: linear-gradient(#ffb25a,#ef6100);
}
/* Bubble positioning */
.android .dw-bubble-bottom .dw-arr {
    border-color: transparent transparent #000 transparent;
}
.android .dw-bubble-top .dw-arr {
    border-color: #9c9c9c transparent transparent transparent;
}
/* Docked */
.android.dw-top .dw,
.android.dw-bottom .dw {
    border-right: 0;
    border-left: 0;
}
.android.dw-top .dw {
    border-top: 0;
}
.android.dw-bottom .dw {
    border-bottom: 0;
}
