/* Sense UI Skin */
.sense-ui .dwwr {
    padding: 0;
    color: #fff;
    background: #333;
    border: 2px solid #fff;
}
.sense-ui .dwv {
    margin: 0;
    padding: 5px 10px;
    border-bottom: 1px solid #666;
    text-align: left;
    background: none;
    -webkit-border-radius: 0;
    border-radius: 0;
}
.sense-ui .dwc {
    padding: 26px 2px 0 2px;
}
.sense-ui .dwhl {
    padding-top: 0;
}
.sense-ui .dwfl {
    background: none;
}
.sense-ui .dw .dwpm .dwwl,
.sense-ui .dw .dwpm .dww {
    background: #fff;
    border: 0;
    padding: 0;
}
.sense-ui .dw .dwpm .dwwl,
.sense-ui .dw .dwpm .dwwo {
    -webkit-border-radius: 3px;
    border-radius: 3px;
}
.sense-ui .dwbc {
    padding: 4px 2px;
    background: #000;
    background: -webkit-gradient(linear,left bottom,left top,color-stop(0.5, #000),color-stop(0.5, #666));
    background: -webkit-linear-gradient(#666 50%,#000 50%);
    background: -moz-linear-gradient(#666 50%,#000 50%);
    background: linear-gradient(#666 50%,#000 50%);
}
.sense-ui .dw .dwwr .dwb {
    border: 1px solid #333;
    background: #222;
    background: -webkit-gradient(linear,left bottom,left top,from(#000),to(#444));
    background: -webkit-linear-gradient(#444,#000);
    background: -moz-linear-gradient(#444,#000);
    background: linear-gradient(#444,#000);
    box-shadow: inset 0 0 5px #000;
}
.sense-ui .dw .dwwr .dwb-a {
    background: #3c7500;
    background: -webkit-gradient(linear,left bottom,left top,from(#3c7500),to(#94c840));
    background: -webkit-linear-gradient(#94c840,#3c7500);
    background: -moz-linear-gradient(#94c840,#3c7500);
    background: linear-gradient(#94c840,#3c7500);
}
/* Docked */
.sense-ui.dw-top .dwwr {
    border: 0;
}
.sense-ui.dw-bottom .dwwr {
    border: 0;
    border-top: 1px solid #666;
}
