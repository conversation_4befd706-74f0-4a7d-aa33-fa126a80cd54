/* iOS Skin */
.ios .dw {
    min-width: 134px;
    border: 1px solid #2d3034;
}
.ios .dwo {
    background: none;
}
.ios .dwwr {
    position: relative;
    padding: 0 6px;
    margin-top: 40px;
    background: -webkit-gradient(linear,left top,left bottom,from(#9f9fa6),color-stop(0.5, #484a55),color-stop(0.5, #272836),to(#282a39));
    background: -webkit-linear-gradient(#9f9fa6,#484a55 50%,#272836 50%,#282a39);
    background: -moz-linear-gradient(#9f9fa6,#484a55 50%,#272836 50%,#282a39);
    background: linear-gradient(#9f9fa6,#484a55 50%,#272836 50%,#282a39);
    background-color: #9f9fa6;
    background-repeat: no-repeat;
    color: #fff;
    overflow: visible;
    -webkit-border-radius: 0;
    border-radius: 0;
}
.ios.dw-nobtn .dwwr,
.ios.dw-inline .dwwr {
    margin-top: 0;
}
.ios .dwv {
    width: 0;
    height: 0;
    margin: 0;
    padding: 0;
    border: 0;
    overflow: hidden;
}
.ios .dwc {
    padding: 30px 2px 10px 2px;
}
.ios .dwhl {
    padding: 10px 2px;
}
.ios .dwfl {
    padding: 0;
    margin: 0;
    background: none;
}
.ios .dw .dwwl {
    margin: 0;
    border-left: 1px solid #000;
    border-right: 1px solid #000;
    background: #cbcce0;
    background: -webkit-gradient(linear,left bottom,left top,from(#2c2c38),color-stop(0.35, #cbcce0),color-stop(0.65, #cbcce0),to(#2c2c38));
    background: -webkit-linear-gradient(#2c2c38,#cbcce0 35%, #cbcce0 65%,#2c2c38);
    background: -moz-linear-gradient(#2c2c38,#cbcce0 35%, #cbcce0 65%,#2c2c38);
    background: linear-gradient(#2c2c38,#cbcce0 35%, #cbcce0 65%,#2c2c38);
    -webkit-box-shadow: 0 1px 1px rgba(255,255,255,0.3);
    box-shadow: 0 1px 1px rgba(255,255,255,0.3);
}
.ios .dw-ltr .dwsc .dwfl:first-child .dwwl,
.ios .dw-rtl .dwsc .dwfl:last-child .dwwl {
    -webkit-border-radius: 3px 0 0 3px;
    border-radius: 3px 0 0 3px;
}
.ios .dw-ltr .dwsc .dwfl:last-child .dwwl,
.ios .dw-rtl .dwsc .dwfl:first-child .dwwl {
    -webkit-border-radius: 0 3px 3px 0;
    border-radius: 0 3px 3px 0;
}
.ios .dwpm .dwwl {
    -webkit-border-radius: 3px;
    border-radius: 3px;
}
.ios .dwsc .dww {
    background: #fff;
    background: -webkit-gradient(linear,left bottom,left top,from(#333),color-stop(0.10, #999),color-stop(0.30, #fff),color-stop(0.70, #fff),color-stop(0.90, #999),to(#333));
    background: -webkit-linear-gradient(#333,#999 10%,#fff 30%,#fff 70%,#999 90%,#333);
    background: -moz-linear-gradient(#333,#999 10%,#fff 30%,#fff 70%,#999 90%,#333);
    background: linear-gradient(#333,#999 10%,#fff 30%,#fff 70%,#999 90%,#333);
    -webkit-border-radius: 0;
    border-radius: 0;
}
.ios .dw .dwpm .dww {
    background: -webkit-gradient(linear,left bottom,left top,from(#333),color-stop(0.48, #fff),color-stop(0.52, #fff),to(#333));
    background: -webkit-linear-gradient(#333,#fff 48%,#fff 52%,#333);
    background: -moz-linear-gradient(#333,#fff 48%,#fff 52%,#333);
    background: linear-gradient(#333,#fff 48%,#fff 52%,#333);
}
.ios .dw .dwpm .dwwl {
    margin: 4px 2px;
    border: 1px solid #000;
}
.ios .dw .dwpm .dww {
    margin: 0;
    border: 0;
}
.ios .dww .dw-li {
    color: #000;
    font-size: 20px;
    font-weight: bold;
    text-align: right;
    text-shadow: none;
}
.ios .dww .dw-li.dw-hl {
    background: -webkit-gradient(linear,left bottom,left top,from(#0288f3),to(#005de6));
    background: -webkit-linear-gradient(#0288f3,#005de6);
    background: -moz-linear-gradient(#0288f3,#005de6);
    background: linear-gradient(#0288f3,#005de6);
    color: #fff;
}
.ios .dwwo {
    background: -webkit-gradient(linear,left bottom,left top,from(#333),color-stop(0.1, rgba(153,153,153,0)),color-stop(0.9, rgba(153,153,153,0)),to(#333));
    background: -webkit-linear-gradient(#333,rgba(153,153,153,0) 10%, rgba(153,153,153,0) 90%, #333);
    background: -moz-linear-gradient(#333,rgba(153,153,153,0) 10%, rgba(153,153,153,0) 90%, #333);
    background: linear-gradient(#333,rgba(153,153,153,0) 10%, rgba(153,153,153,0) 90%, #333);
}
.ios .dwwol {
    height: 28px;
    padding: 1px;
    margin-top: -16px;
    border-color: #7b8699;
    background: #6f75b0;
    background: -webkit-gradient(linear,left bottom,left top,color-stop(0.5, rgba(111, 117, 176, 0.5)),color-stop(0.5, rgba(151, 157, 197, 0.5)));
    background: -webkit-linear-gradient(rgba(151, 157, 197, 0.5) 50%,rgba(111, 117, 176, 0.5) 50%);
    background: -moz-linear-gradient(rgba(151, 157, 197, 0.5) 50%,rgba(111, 117, 176, 0.5) 50%);
    background: linear-gradient(rgba(151, 157, 197, 0.5) 50%,rgba(111, 117, 176, 0.5) 50%);
    z-index: 10;
    left: -1px;
    -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.5);
    box-shadow: 0 1px 3px rgba(0,0,0,0.5);
    filter: alpha(opacity=50);
}
.ios .dww .dwwol {
    display: none;
}
.ios .dw .dwbc {
    display: block;
    position: absolute;
    top: -40px;
    left: 0;
    width: 100%;
    height: 28px;
    padding: 5px 0;
    background: #454545;
    background: -webkit-gradient(linear,left top,left bottom,from(rgba(69,69,69,0.7)),color-stop(0.5, rgba(37,37,37,0.7)),color-stop(0.5, rgba(16,16,16,0.7)),to(rgba(0,0,0,0.7)));
    background: -webkit-linear-gradient(rgba(69,69,69,0.7),rgba(37,37,37,0.7) 50%,rgba(16,16,16,0.7) 50%,rgba(0,0,0,0.7));
    background: -moz-linear-gradient(rgba(69,69,69,0.7),rgba(37,37,37,0.7) 50%,rgba(16,16,16,0.7) 50%,rgba(0,0,0,0.7));
    background: linear-gradient(rgba(69,69,69,0.7),rgba(37,37,37,0.7) 50%,rgba(16,16,16,0.7) 50%,rgba(0,0,0,0.7));
    border-bottom: 1px solid #888;
    border-bottom: 1px solid rgba(255,255,255,0.5);
    border-top: 1px solid #888;
    border-top: 1px solid rgba(255,255,255,0.5);
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
}
.ios .dw .dwb {
    margin: 0 5px;
    padding: 0 10px;
    display: inline-block;
    font-size: 12px;
    height: 26px;
    line-height: 26px;
    border: 1px solid #1f1f1f;
    background: #1a1a1a;
    background: -webkit-gradient(linear,left top,left bottom,from(#7b7b7b),color-stop(0.5, #1a1a1a),color-stop(0.5, #000));
    background: -webkit-linear-gradient(#7b7b7b,#1a1a1a 50%,#000 50%);
    background: -moz-linear-gradient(#7b7b7b,#1a1a1a 50%,#000 50%);
    background: linear-gradient(#7b7b7b,#1a1a1a 50%,#000 50%);
    -webkit-box-shadow: 0 1px 0 rgba(255,255,255,0.3);
    box-shadow: 0 1px 0 rgba(255,255,255,0.3);
}
.ios .dwb-s .dwb {
    border: 1px solid #194aab;
    background: #194aab;
    background: -webkit-gradient(linear,left top,left bottom,from(#82aaff),color-stop(0.5,#3162c4),color-stop(0.5,#194aab));
    background: -webkit-linear-gradient(#82aaff,#3162c4 50%,#194aab 50%);
    background: -moz-linear-gradient(#82aaff,#3162c4 50%,#194aab 50%);
    background: linear-gradient(#82aaff,#3162c4 50%,#194aab 50%);
}
.ios .dwb-a {
    opacity: .8;
    filter: alpha(opacity=80);
}
.ios .dw .dwwb {
    color: #fff;
    border: 0;
    background: #3f4e68;
    background: -webkit-gradient(linear,left bottom,left top,from(#3f4e68),color-stop(0.5, #75859f),color-stop(0.5, #808ea6),to(#c7d1e2));
    background: -webkit-linear-gradient(#c7d1e2,#808ea6 50%,#75859f 50%,#3f4e68);
    background: -moz-linear-gradient(#c7d1e2,#808ea6 50%,#75859f 50%,#3f4e68);
    background: linear-gradient(#c7d1e2,#808ea6 50%,#75859f 50%,#3f4e68);
}
.ios .dw .dwwl .dwb-a {
    background: #252c36;
    background: -webkit-gradient(linear,left bottom,left top,from(#252c36),color-stop(0.5, #171e28),color-stop(0.5, #272e38),to(#6b6e75));
    background: -webkit-linear-gradient(#6b6e75,#272e38 50%,#171e28 50%,#252c36);
    background: -moz-linear-gradient(#6b6e75,#272e38 50%,#171e28 50%,#252c36);
    background: linear-gradient(#6b6e75,#272e38 50%,#171e28 50%,#252c36);
}
.ios .dwbw {
    display: block;
    float: right;
}
.ios .dwb-c {
    float: left;
}
/* Bubble positioning */
.ios.dw-bubble .dw {
    padding: 6px;
    background: #afafaf;
    background: -webkit-gradient(linear,left top,left bottom,from(#afafaf),color-stop(0.3, #1b2530));
    background: -webkit-linear-gradient(#afafaf, #1b2530 30%);
    background: -moz-linear-gradient(#afafaf, #1b2530 30%);
    background: linear-gradient(#afafaf, #1b2530 30%);
    box-shadow: 0 0 25px rgba(0,0,0,0.7);
    -webkit-border-radius: 5px;
    -webkit-box-shadow: 0 0 25px rgba(0,0,0,0.7);
    border-radius: 5px;
}
.ios .dw-bubble-bottom .dw-arr {
    border-color: transparent transparent #2d3034 transparent;
}
.ios .dw-bubble-bottom .dw-arr:after {
    content: '';
    position: absolute;
    top: -16px;
    left: -17px;
    border: 17px solid #afafaf;
    border-color: transparent transparent #afafaf transparent;
}
.ios .dw-bubble-top .dw-arr {
    border-color: #1b2530 transparent transparent transparent;
}
/* Multiple select */
.ios .dwwms .dwwol {
    display: none;
}
.ios .dwwms .dw-li {
    padding: 0 5px 0 30px;
    text-align: left;
}
.ios .dw-msel:after {
    width: 30px;
}
.ios .dww .dw-msel {
    color: #215085;
}
