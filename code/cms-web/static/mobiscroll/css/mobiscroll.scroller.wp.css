.wp .dw .dwwr {
    padding: 10px;
    background: #1f1f1f;
    -webkit-border-radius: 0;
    border-radius: 0;
}
.wp .dwv {
    background: none;
    margin: 0;
    padding: 10px;
    padding-top: 0;
    color: #fff;
}
.wp .dwc {
    padding: 30px 0 0 0;
}
.wp .dwhl {
    padding: 0;
}
.wp .dwwc {
    padding: 3px;
}
.wp .dwfl {
    padding: 0;
    margin: 0;
    background: none;
}
.wp .dw .dwwl {
    margin: 0;
    padding: 0 2px;
    background: none;
}
.wp .dwl {
    color: #fff;
}
.wp .dw .dwwl .dww {
    background: none;
    border: 0;
    color: #fff;
}
.wp .dw-li {
    position: relative;
    padding: 0;
    font-size: 26px;
    letter-spacing: -1px;
    text-align: left;
    text-shadow: none;
    opacity: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    filter: Alpha(Opacity=0);
}
.wp .dwwr .dw-i {
    position: relative;
    top: 4%;
    height: 92%;
    padding: 0 5px;
    border: 1px solid #4c4c4c;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    filter: inherit;
}
.wp .dw-li .dw-i {
    width: 99.99%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}
.wp .wpa .dw-li, 
.wp .dwa .dw-li {
    opacity: .3;
    filter: Alpha(Opacity=30);
}
.wp .wpa .dw-v, 
.wp .dwa .dw-v { 
    opacity: 1;
    filter: Alpha(Opacity=100);
}
.wp .dw-day, .wp .dw-mon {
    display: block;
    color: #a9a9a9;
    line-height: 18px;
    font-size: 11px;
    letter-spacing: normal;
    position: absolute;
    bottom: 0;
    filter: inherit;
}
.wp .dw-sel {
    opacity: 1;
    filter: Alpha(Opacity=100);
}
.wp .dw .dwwr .dw-sel .dw-i {
    color: #fff;
    background: #4c4c4c;
}
.wp .dw-sel .dw-day, .wp .dw-sel .dw-mon {
    color: #fff;
}
.wp .dw-hl {
    background: none;
}
.wp .dw-hl .dw-i {
    background: #4c4c4c;
}
.wp .dwa .dww .dw-sel .dw-i {
    border: 1px solid #4c4c4c;
    background-color: transparent;
}
.wp .dwa .dw-day, .wp .dwa .dw-month {
    color: #a9a9a9;
}
.wp .dwwo {
    display: none;
}
.wp .dwwol {
    display: none;
}
.wp .dwbc {
    padding: 0;
    display: block;
}
.wp .dwbw {
    display: inline-block;
}
.wp .dw .dwwr .dwb {
    position: relative;
    top: 0;
    min-width: 32px;
    display: inline-block;
    height: 20px;
    padding: 29px 5px 0 5px;
    background: none;
    line-height: 20px;
    font-size: 11px;
    font-weight: normal;
    text-transform: lowercase;
    text-shadow: none;
    -webkit-backface-visibility: hidden;
    -webkit-box-shadow: none;
    -webkit-transition: all .1s linear;
    -moz-transition: all .1s linear;
    transition: all .1s linear;
    box-shadow: none;
}
.wp .dwb:before {
    position: absolute;
    top: 3px;
    left: 50%;
    width: 22px;
    height: 22px;
    margin: 0 -13px;
    border: 2px solid #fff;
    border-radius: 13px;
    line-height: 22px;
    font-size: 10px;
}
.wp .dw .dwwr .dwb-a {
    top: -3px;
}
/* +/- buttons */
.wp .dw .dwwl .dwb-a {
    background: #1f1f1f;
}
.wp .dw .dwpm .dwwl {
    border: 0;
}
.wp .dw .dwwb {
    background: #1f1f1f;
    -webkit-box-shadow: none;
    -webkit-border-radius: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-shadow: none;
    border-radius: 0;
    box-sizing: border-box;
}
.wp .dwwb:before {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 22px;
    height: 22px;
    margin: -13px 0 0 -13px;
    color: #fff;
    line-height: 22px;
    font-size: 10px;
    text-shadow: none;
    border: 2px solid #fff;
    -webkit-border-radius: 13px;
    border-radius: 13px;
}
.wp .dw .dwwr .dwwb.dwb-a {
    top: auto;
}
.wp .dwwb span {
    display: none;
}
.wp .dwb-a:before {
    background: #fff;
    color: #1f1f1f;
}

/* Bubble positioning */
.wp .dw-bubble-bottom .dw-arr {
    border-color: transparent transparent #1f1f1f transparent;
}
.wp .dw-bubble-top .dw-arr {
    border-color: #1f1f1f transparent transparent transparent;
}
/* Multiple select */
.wp .dwwms .dw-li {
    opacity: .3;
    filter: Alpha(Opacity=30);
    padding: 0 0 0 30px;
}
.wp .dwwms .dw-v {
    opacity: 1;
    filter: Alpha(Opacity=100);
}
.wp .dwc .dwwms .dw-i,
.wp .dwc .dwwms .dw-sel .dw-i,
.wp .dwc .dwwms .dw-hl .dw-i,
.wp .dwc .dwwms.dwa .dw-sel .dw-i {
    border: 0;
    background: none;
}
.wp .dwwms .dw-li:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 5px;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    font-size: 18px;
    line-height: 20px;
    background: #4c4c4c;
    color: #fff;
}
.wp .dwwms .dw-msel:after {
    content: '\2714';
}
/* Light version */
.wp.light .dwwr {
    background: #dedede;
}
.wp.light .dwv,
.wp.light .dwl,
.wp.light .dwb {
    color: #000;
}
.wp.light .dw-li {
    color: #6b6b6b;
}
.wp.light .dw-i {
    border-color: #b5b5b5;
}
.wp.light .dw .dw-sel .dw-i {
    background: #b5b5b5;
}
.wp.light .dw-hl .dw-i {
    color: #fff;
    background: #b5b5b5;
}
.wp.light .dw-day, .wp.light .dw-mon {
    color: #6b6b6b;
}
.wp.light .dw-sel .dw-day, .wp.light .dw-sel .dw-mon {
    color: #fff;
}
.wp.light .dw .dwa .dw-sel .dw-i {
    color: #6b6b6b;
    border: 1px solid #b5b5b5;
    background-color: transparent;
}
.wp.light .dw .dwc .dwa .dw-day, .wp.light .dw .dwc .dwa .dw-mon {
    color: #6b6b6b;
}
.wp.light .dwb:before,
.wp.light .dwwb:before {
    border: 2px solid #000;
    color: #000;
}
.wp.light .dwb-a:before {
    background: #000;
    color: #dedede;
}
.wp.light .dw .dwwb {
    background: #dedede;
}
/* Bubble positioning */
.wp.light .dw-bubble-bottom .dw-arr {
    border-color: transparent transparent #dedede transparent;
}
.wp.light .dw-bubble-top .dw-arr {
    border-color: #dedede transparent transparent transparent;
}
/* Multiple select */
.wp.light .dwc .dwwms .dw-i,
.wp.light .dwwms.dwa .dw-sel .dw-i {
    border: 0;
    background: none;
    color: #000;
}
.wp.light .dwwms .dw-li:after {
    background: #b5b5b5;
}
.wp.light .wp-none .dwwms .dw-li:after {
    color: #000;
}
/* Accents */
.wp.light .dw.wp-none .dw-sel .dw-i, .wp.light .dw.wp-none .dw-hl .dw-i {
    color: #000;
}
.wp.light .dw.wp-none .dw-day, .wp.light .dw.wp-none .dw-mon {
    color: #6b6b6b;
}
.wp .dw.wp-lime .dw-sel .dw-i, .wp .dw.wp-lime .dw-hl .dw-i, .wp .dw.wp-lime .dwwms .dw-li:after {
    background: #a4c400;
    border-color: #a4c400;
}
.wp .dw.wp-green .dw-sel .dw-i, .wp .dw.wp-green .dw-hl .dw-i, .wp .dw.wp-green .dwwms .dw-li:after {
    background: #60a917;
    border-color: #60a917;
}
.wp .dw.wp-emerald .dw-sel .dw-i, .wp .dw.wp-emerald .dw-hl .dw-i, .wp .dw.wp-emerald .dwwms .dw-li:after {
    background: #008a00;
    border-color: #008a00;
}
.wp .dw.wp-teal .dw-sel .dw-i, .wp .dw.wp-teal .dw-hl .dw-i, .wp .dw.wp-teal .dwwms .dw-li:after {
    background: #00aba9;
    border-color: #00aba9;
}
.wp .dw.wp-cyan .dw-sel .dw-i, .wp .dw.wp-cyan .dw-hl .dw-i, .wp .dw.wp-cyan .dwwms .dw-li:after {
    background: #1ba1e2;
    border-color: #1ba1e2;
}
.wp .dw.wp-cobalt .dw-sel .dw-i, .wp .dw.wp-cobalt .dw-hl .dw-i, .wp .dw.wp-cobalt .dwwms .dw-li:after {
    background: #0050ef;
    border-color: #0050ef;
}
.wp .dw.wp-indigo .dw-sel .dw-i, .wp .dw.wp-indigo .dw-hl .dw-i, .wp .dw.wp-indigo .dwwms .dw-li:after {
    background: #6a00ff;
    border-color: #6a00ff;
}
.wp .dw.wp-violet .dw-sel .dw-i, .wp .dw.wp-violet .dw-hl .dw-i, .wp .dw.wp-violet .dwwms .dw-li:after {
    background: #aa00ff;
    border-color: #aa00ff;
}
.wp .dw.wp-pink .dw-sel .dw-i, .wp .dw.wp-pink .dw-hl .dw-i, .wp .dw.wp-pink .dwwms .dw-li:after {
    background: #f472d0;
    border-color: #f472d0;
}
.wp .dw.wp-magenta .dw-sel .dw-i, .wp .dw.wp-magenta .dw-hl .dw-i, .wp .dw.wp-magenta .dwwms .dw-li:after {
    background: #d80073;
    border-color: #d80073;
}
.wp .dw.wp-crimson .dw-sel .dw-i, .wp .dw.wp-crimson .dw-hl .dw-i, .wp .dw.wp-crimson .dwwms .dw-li:after {
    background: #a20025;
    border-color: #a20025;
}
.wp .dw.wp-red .dw-sel .dw-i, .wp .dw.wp-red .dw-hl .dw-i, .wp .dw.wp-red .dwwms .dw-li:after {
    background: #e51400;
    border-color: #e51400;
}
.wp .dw.wp-orange .dw-sel .dw-i, .wp .dw.wp-orange .dw-hl .dw-i, .wp .dw.wp-orange .dwwms .dw-li:after {
    background: #fa6800;
    border-color: #fa6800;
}
.wp .dw.wp-amber .dw-sel .dw-i, .wp .dw.wp-amber .dw-hl .dw-i, .wp .dw.wp-amber .dwwms .dw-li:after {
    background: #f0a30a;
    border-color: #f0a30a;
}
.wp .dw.wp-yellow .dw-sel .dw-i, .wp .dw.wp-yellow .dw-hl .dw-i, .wp .dw.wp-yellow .dwwms .dw-li:after {
    background: #d8c100;
    border-color: #d8c100;
}
.wp .dw.wp-brown .dw-sel .dw-i, .wp .dw.wp-brown .dw-hl .dw-i, .wp .dw.wp-brown .dwwms .dw-li:after {
    background: #825a2c;
    border-color: #825a2c;
}
.wp .dw.wp-olive .dw-sel .dw-i, .wp .dw.wp-olive .dw-hl .dw-i, .wp .dw.wp-olive .dwwms .dw-li:after {
    background: #6d8764;
    border-color: #6d8764;
}
.wp .dw.wp-steel .dw-sel .dw-i, .wp .dw.wp-steel .dw-hl .dw-i, .wp .dw.wp-steel .dwwms .dw-li:after {
    background: #647687;
    border-color: #647687;
}
.wp .dw.wp-mauve .dw-sel .dw-i, .wp .dw.wp-mauve .dw-hl .dw-i, .wp .dw.wp-mauve .dwwms .dw-li:after {
    background: #76608a;
    border-color: #76608a;
}
.wp .dw.wp-sienna .dw-sel .dw-i, .wp .dw.wp-sienna .dw-hl .dw-i, .wp .dw.wp-sienna .dwwms .dw-li:after {
    background: #7a3b3f;
    border-color: #7a3b3f;
}
