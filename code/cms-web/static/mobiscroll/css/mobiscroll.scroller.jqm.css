/* jQuery Mobile Theme */
.jqm .dwo {
    background: none;
}
.jqm .dwv {
    position: static;
    width: auto;
    border: 0;
    margin: 0 -2px;
    padding: 5px 0;
    -webkit-border-radius: 0;
    border-radius: 0;
}
.jqm .dw-hidden {
    min-height: 0;
}
.jqm .dwwr {
    border: 0;
}
.jqm .dwpm .dwwo,
.jqm .dwpm .dwfl {
    background: none;
}
.jqm .dwwb {
    margin: 0;
    border: 0;
    padding: 0;
}
.jqm .dwwb span {
    padding: 0;
}
.jqm .dwwbp .ui-btn-inner {
    font-size: 40px;
    -webkit-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
}
.jqm .dwwbm .ui-btn-inner {
    font-size: 32px;
    -webkit-border-radius: 0 0 3px 3px;
    border-radius: 0 0 3px 3px;
}
.jqm .dwwbp span {
    font-weight: normal;
}
.jqm .dwbc .ui-btn {
    margin: 0 2px;
}
.jqm .dwbc .ui-btn-inner {
    font-size: 12px;
}
.jqm .dwpm .dwl {
    border: 0;
    background: none;
}
/* Bubble positioning */
.jqm .dw-bubble-bottom .dw-arr {
    border-color: transparent transparent #222 transparent;
}
.jqm .dw-bubble-top .dw-arr {
    border-color: #222 transparent transparent transparent;
}
.jqm.dw-bubble .dw {
    background: #222;
    border: 0;
    padding: 6px;
}
.jqm.dw-bubble .dwwr {
    background-color: #fff;
}
/* Docked */
.jqm.dw-bottom .dw, .jqm.dw-top .dw {
    padding: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
}
.jqm.dw-top .dw {
    border-right: 0;
    border-top: 0;
    border-left: 0;
}
.jqm.dw-bottom .dw {
    border-bottom: 0;
    border-right: 0;
    border-left: 0;
}
