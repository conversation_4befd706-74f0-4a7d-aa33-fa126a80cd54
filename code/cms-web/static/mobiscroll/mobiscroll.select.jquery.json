{"name": "mobiscroll.select", "title": "Mobiscroll Select Scroller", "description": "Renders a scrollable list from HTML selects for easy data picking on touch devices. It comes with theming capability, optional jQuery Mobile integration and multiple display modes: Inline, Modal, Bubble, Docked Top & Bottom. It works with optgroups as well.", "keywords": ["ui", "input", "form", "html5", "select", "touchscreen", "dropdown", "mobile", "scroller", "clickpick"], "version": "2.11.0", "author": {"name": "Acid Media", "url": "http://theacidmedia.net"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/acidb"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/dioslaska"}], "licenses": [{"type": "MIT", "url": "https://github.com/acidb/mobiscroll/blob/master/MITLicense.txt"}], "bugs": "https://github.com/acidb/mobiscroll/issues", "homepage": "http://mobiscroll.com/component/select", "docs": "http://docs.mobiscroll.com/select", "demo": "http://demo.mobiscroll.com/select", "download": "http://mobiscroll.com/component/select", "dependencies": {"jquery": ">=1.7"}}