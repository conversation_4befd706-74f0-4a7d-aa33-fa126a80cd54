<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!-- 指明xml版本，编码格式为UTF-8 -->
<!-- basedir故名思意就是工作的根目录 .代表当前目录。default代表默认要做的事情。 -->
<project basedir="." default="war" name="ad-gx-admin">
    <!--
property类似定义程序中的变量，name是变量名，value是变量值，在下文中可以通过${src}和${webroot}取得变
量的值 -->
    <property name="src" value="src" />
    <property name="webroot" value="WebContent" />
    <property name="lib" value="${webroot}/WEB-INF/lib" />
    <property name="classes" value="build/classes" />
    <property name="warDest" value="build/warDest" />
    <property name="Bulid_Number" value="warDest" />
    <!-- target,即一个任务，它有一个名字，depends是它所依赖的target，
在执行这个targe，例如这里的init之前ant会先检查clean是否曾经被执行过，
如果执行过则直接直接执行init，如果没有则会先执行它依赖的target -->
    <target name="init" depends="clean">
        <!-- 创建编译后文件存放目录、war包存放目录 -->
        <mkdir dir="${classes}" />
        <mkdir dir="${warDest}" />
        <!-- 指定jar包的目录，下面直接通过${classpath}可以取得jar路径 -->
        <path id="classpath">
            <fileset dir="${lib}">
                <include name="**/*.jar" />
            </fileset>
        </path>
        <!-- 该任务主要用来对文件和目录的复制功能,即将./${src}和./${webroot}目录下
include指定的文件拷贝到./${classes}目录下。额外说明：exclude表示排除的文件 -->
        <copy todir="./${classes}">
            <fileset dir="./${src}">
                <include name="**/*.properties" />
                <include name="**/*.xml" />
                <include name="**/*.dtd" />
                <include name="**/*.json" />
                <include name="**/*.vm" />
            </fileset>
            <fileset dir="./${webroot}">
                <include name="resource/" />
            </fileset>
        </copy>
    </target>
    <target name="clean">
        <!-- 该任务的作用是根据日志或监控器的级别输出信息。它包括message、file、append和level四个属性 -
->
<echo >${warDest}</echo>
<!== 对文件或目录进行删除,这里是删除${warDest}和${classes}目录 -->
        <delete dir="${warDest}" />
        <delete dir="${classes}" />
    </target>
    <target name="complie" depends="init">
        <!-- 编译:将srcdir目录的源码，编译后放到destdir目录下,refid表示关联的jar包 -->
        <javac srcdir="${src}" destdir="${classes}" debug="true" debuglevel="lines,vars,source" encoding="UTF-8">
            <classpath refid="classpath" />
        </javac>
    </target>
    <target name="war" depends="complie">
        <war warfile="${warDest}/ad-gx-admin.war" webxml="${webroot}/WEB-INF/web.xml">
            <lib dir="${lib}" />
            <classes dir="${classes}" />
            <fileset dir="./${webroot}">
                <include name="**/**" />
                <exclude name="META-INF/**" />
                <exclude name="WEB-INF/classes/**" />
                <exclude name="WEB-INF/lib/**" />
            </fileset>
        </war>
    </target>
</project>