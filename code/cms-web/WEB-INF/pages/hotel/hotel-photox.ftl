<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>图片管理</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="images-photo-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>图片管理</h2>
						</header>
						<div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal">
									<div class="form-group">
										<div class="col-sm-12" style="text-align: right;">
											<input type="button" id="savtBtn" class="btn btn-primary" value="保存" />
										</div>
									</div>
								</form>
								<div id="hotelImageLayer" class="superbox col-sm-12" style="margin-top: 12px; margin-bottom: 28px;">
									<#list hotelImages as data>
										<div class="superbox-list" data-id="${data.id}" data-hide="${data.hidden?if_exists}">
											<img src="${imageUrl}/${data.surl?if_exists}" title="${data.type}" data-img="${imageUrl}/${data.url?if_exists}" title="${data.name?if_exists}" class="superbox-img viewBanner">
											<div style="margin-top: 4px;">
												<input type="checkbox" value="${data.id?if_exists}">
												<span>${data.id?if_exists}</span>
											</div>
										</div>
									</#list>
									<div class="superbox-float"></div>
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	$('#savtBtn').on('click', function() {
		var ids = '';
		$('input[type=checkbox]:checked').each(function() {
			var $obj = $(this);
			var val = $obj.val();
			ids += val + ',';
		});
		var params = {};
		params.ids = ids;
		$('#savtBtn').prop('disabled', true);
		$.post('${basePath}/hotel/photox', params, function(data) {
			alert(data.desc);
			$('#savtBtn').prop('disabled', false);
		}, 'json');
	});
});
</script>
</body>
</html>