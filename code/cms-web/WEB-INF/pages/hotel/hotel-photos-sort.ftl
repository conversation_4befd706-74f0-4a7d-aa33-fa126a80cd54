<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>图片排序</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="images-photo-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>图片排序</h2>
						</header>
						<div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal">
									<div class="form-group">
										<div class="col-sm-12" style="text-align: right;">
											<!-- <input type="button" class="btn btn-primary" value="添加图片" style="margin-right: 12px;" onclick="window.location.href='${basePath}/hotel/image-edit?hotelCode=${hotelCode}'" /> -->
											<input type="button" class="btn btn-info saveOrderBtn" value="保存排序状态" style="margin-right: 12px;" />
											<a class="btn btn-primary" href="${basePath}/hotel/photos?hotelCode=${hotelCode}">返回</a>
										</div>
									</div>
								</form>
								<div class="col-sm-12 alert alert-block alert-warning">
									<h4 class="alert-heading" style="margin-bottom: 4px;">酒店图片</h4>
									<span>提示：拖动以下图片，点“保存排序及隐藏状态”按钮，可对图片进行排序。</span>
								</div>
								<div id="hotelImageLayer" class="superbox col-sm-12" style="margin-top: 12px; margin-bottom: 28px;">
									<#list page.datas as data>
										<div class="superbox-list" data-id="${data.id}" data-hide="${data.hidden?if_exists?string("true","false")}">
											<!--
											<img src="${imageUrl}/${data.url?if_exists?substring(0,data.url?last_index_of("."))}_1024_510${data.url?substring(data.url?last_index_of("."),data.url?length)}" data-img="${imageUrl}/${data.url?if_exists?substring(0,data.url?last_index_of("."))}_1024_510${data.url?substring(data.url?last_index_of("."),data.url?length)}" title="${data.name?if_exists}" class="superbox-img viewBanner">
											-->
											<img src="${data.sourceUrl?if_exists!imageUrl + '/' + data.url}" data-img="${data.sourceUrl?if_exists!imageUrl + '/' + data.url}" title="${data.name?if_exists}" class="superbox-img viewBanner" />
											<div style="margin-top: 4px;line-height: 24px; font-weight: bolder;">${data.name?if_exists}</div>
											<div class="imgif" style="margin-top: 4px;line-height: 24px;"><!-- 1600px × 558px 1600px × 550px --></div>
											<div style="margin-top: 4px;">
												<!-- <a style="float: left;" href="${basePath}/hotel/image-edit?hotelCode=${hotelCode}&id=${data.id?if_exists}">修改</a>
												<a style="float: right;" class="hidectl" href="javascript:void(0);" data-id="${data.id?if_exists}" data-status="${data.hidden?if_exists?string("true","false")}"><#if data.hidden?if_exists?string("true","false")=="false">隐藏<#else>已隐藏</#if></a> -->
											</div>
										</div>
									</#list>
									<div class="superbox-float"></div>
								</div>
								<div style="text-align: right;">
									<!-- <input type="button" class="btn btn-info showAllImgBtn" value="显示所有图片" style="margin-right: 12px;" /> -->
									<input type="button" class="btn btn-info saveOrderBtn" value="保存排序状态" style="margin-right: 12px;" />
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>

<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {

	var $hids = $('#hids');
	var $hrows = $('input[type=checkbox][name=hid]');
	$hids.on('change', function() {
		if($hids.prop('checked')) {
			$hrows.prop('checked', true);
		} else {
			$hrows.prop('checked', false);
		}
	});
	
	/*$('img.viewBanner').each(function() {
		var $obj = $(this);
		var url = $obj.attr('data-img');
		var params = {};
		params.url = url;
		$.get('${basePath}/image/infox.json', params, function(data) {
			var width = data.width;
			var height = data.height;
			var size = data.size;
			$obj.parent().find('.imgif').text('大小：'+size+' 分辨率：'+width+'px × '+height+'px');
		}, 'json');
	});*/
	$('.superbox').sortable();
	$('.superbox-list .hidectl').on('click', function() {
		var $obj = $(this);
		var id = $obj.attr('data-id');
		var cstatus = $obj.attr('data-status');
		if(cstatus == '0') {
			$obj.attr('data-status', '1');
			$obj.text('已隐藏');
			/* var url = "/hotel/hiddenHotelImage/"+id+"/true";
			$.get(url,  function(data) {
				alert(data.desc);
				if(data.code == '1') {
					$obj.attr('data-status', '1');
					$obj.text('已隐藏');
				} else{
					alert("修改失败");
				}
			}); */
		} else {
			$obj.attr('data-status', '0');
			$obj.text('隐藏');
			/* var url = "/hotel/hiddenHotelImage/"+id+"/false";
			$.get(url,  function(data) {
				alert(data.desc);
				if(data.code == '1') {
					$obj.attr('data-status', '0');
					$obj.text('隐藏');
				} else{
					alert("修改失败");
				}
			}); */
		}
	});
	
	$('.showAllImgBtn').on('click', function() {
		if(confirm('确定要显示所有图片吗？')) {
			var url = '${basePath}/hotel/image/showall';
			var params = {};
			params.hotelCode = '${hotelCode}';
			$.post(url, params, function(data) {
				alert(data.desc);
				window.location.href = '${basePath}/hotel/photos?hotelCode=${hotelCode}';
			}, 'json');
		}
	});
	
	$('.saveOrderBtn').on('click', saveOrder);
	
	var $hids = $('#hids');
	var $hrows = $('input[type=checkbox][name=hid]');
	$hids.on('change', function() {
		if($hids.prop('checked')) {
			$hrows.prop('checked', true);
		} else {
			$hrows.prop('checked', false);
		}
	});
	
	$('#batchOnBtn').on('click', function() {
		var codes = getCheckHotelCode();
		if(codes == '') {
			alert('请选择酒店');
			return;
		}
		if(confirm('确定要上线吗？')) {
			var url = '${basePath}/hotel/online';
			var params = {};
			params.ids = codes;
			$.post(url, params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					toPage('1');
				}
			}, 'json');
		}
	});
	
	$('#batchOffBtn').on('click', function() {
		var codes = getCheckHotelCode();
		if(codes == '') {
			alert('请选择酒店');
			return;
		}
		if(confirm('确定要下线吗？')) {
			var url = '${basePath}/hotel/offline';
			var params = {};
			params.ids = codes;
			$.post(url, params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					toPage('1');
				}
			}, 'json');
		}
	});
	
	function getCheckHotelCode() {
		var codes = '';
		$('input[type=checkbox][name=hid]:checked').each(function() {
			var $obj = $(this);
			var code = $.trim($obj.val());
			if(code != '') {
				codes += code + ',';
			}
		});
		return codes;
	}
	function showView() {
		$('#hotelImageLayer .superbox-list[data-hide=0]:lt(3)').css({'border': '3px solid #16A085', 'border-radius': '3px'});
	}
	showView();
});

function saveOrder() {
	var datas = '';
	$('#hotelImageLayer .superbox-list').each(function(index) {
		var $obj = $(this);
		var hpid = $obj.attr('data-id');
		var ishide = $.trim($obj.find('.hidectl').attr('data-status'));
		if(ishide != '1') {
			ishide = '0';
		}
		datas += hpid+','+(index+1)+','+ishide+';';
	});
	$('#roomImageLayer .superbox-list').each(function(index) {
		var $obj = $(this);
		var hpid = $obj.attr('data-id');
		var ishide = $.trim($obj.find('.hidectl').attr('data-status'));
		if(ishide != '1') {
			ishide = '0';
		}
		datas += hpid+','+(index+1)+','+ishide+';';
	});
	if(datas == '') {
		alert('没有要排序的图片');
		return;
	}
	var url = '${basePath}/hotel/image/order/save';
	var params = {};
	params.datas = datas;
	$.post(url, params, function(data) {
		alert(data.desc);
		window.location.href = '${basePath}/hotel/toSortHotelPhotos?chainCode=${chainCode}&hotelCode=${hotelCode}';
	}, 'json');
}

function saveHidden(hidden){
	var rows = $('input[type=checkbox][name=hid]');
	var ids = "";
	for(var i=0;i<rows.length;i++){
		if(rows[i].checked){
			ids += ","+rows[i].value;
		}
	}
	console.log('ids: '+ids);
	var url = '${basePath}/hotel/image/updateHidden';
	var params = {};
	params.ids = ids;
	params.hidden = hidden;
	$.post(url, params, function(data) {
		alert(data.desc);
		$("#queryForm").submit();
	}, 'json');
	
}

function toPage(pageNo) {
	$('#start').val(pageNo);
	var temp = $('#queryForm');
	$('#queryForm').submit();
}
</script>
</body>
</html>