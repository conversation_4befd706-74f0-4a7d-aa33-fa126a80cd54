<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}"> </@head.head>
<body>
	<#include "/common/header.ftl" /> <#include "/common/menu.ftl" />
	<div id="main" role="main">
		<div id="ribbon">
			<ol class="breadcrumb">
				<li>新增酒店</li>
			</ol>
		</div>
		<div id="content">
			<section id="widget-grid" class="">
				<div class="row">
					<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
						<div class="jarviswidget jarviswidget-color-darken"
							id="hotel-list-1" data-widget-editbutton="false"
							data-widget-sortable="false" data-widget-colorbutton="false"
							data-widget-fullscreenbutton="false"
							data-widget-deletebutton="false">
							<header>
								<span class="widget-icon"> <i class="fa fa-table"></i>
								</span>
								<h2>新增酒店</h2>
							</header>
							<div>
								<div class="widget-body no-padding">
									<div class="widget-body-toolbar">
<!-- 										<form id="queryForm" class="form-inline"> -->
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<input type="text" class="form-control" name="hcode", id="hcode"
													value="${hcode?if_exists}" placeholder="酒店Trust code" />
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<input type="text" class="form-control" name="scode", id="scode"
													value="${scode?if_exists}" placeholder="酒店Synxis code" />
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<input type="text" class="form-control" name="hotelEnName", id="hotelEnName"
													value="${hotelEnName?if_exists}" placeholder="酒店英文名称" />
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<a class="btn btn-success"
													href="javascript:showUpdateTcsHotelListDivAdd()"
													style="margin-right: 8px;">添加酒店</a>
												<a class="btn btn-success"
													href="javascript:showUploadDiv()"
													style="margin-right: 8px;">excel导入酒店</a>
<!-- 													<input type="button" id="uploadExcelBtn" class="btn btn-primary" value="excel导入酒店" onclick="showUploadDiv()" style="margin-right: 12px;" /> -->
											</div>
<!-- 										</form> -->
									</div>
								</div>
							</div>
							
							<div class="widget-body">
								<div class="alert-heading" style="margin-bottom: 4px;">服务器日志：</div>
								<textarea id="serverLog" rows="10" cols="120"></textarea>
							</div>
						</div>
					</article>
				</div>
			</section>
		</div>
	</div>
	
	<!-- 同步trust选项 -->
	<div id="updateTcsHotelListDiv" class="modal fade top1" tabindex="-1" role="dialog"
		aria-labelledby="addDivLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title">同步选项</h4>
				</div>
				<div class="modal-body pre-scrollable">
					<div>
						<input type="checkbox" name="updateOption" value="downloadHotelXml" checked> 下载酒店xml文件
					</div>
					<div>
						<input type="checkbox" name="updateOption" value="importHotelXml" checked> 更新酒店和房型基本信息
					</div>
					<#if chainCode!="PF">
						<div>
							<input type="checkbox" name="updateOption" value="importLandMarkAndTrsansport" checked> 更新交通和地标基本信息
						</div>
					</#if>
					<div>
						<input type="checkbox" name="updateOption" value="syncCountryFromHotel" checked> 更新国家和城市基本信息
					</div>
					<#if chainCode!="PF">
						<div>
							<input type="checkbox" name="updateOption" value="importRoomPicture" checked> 导入房型图片
						</div>
						<div>
							<input type="checkbox" name="updateOption" value="downloadFtpHotelImage" checked> 下载酒店图片ftp文件
						</div>
						<#if chainCode!="LX">
						<div>
							<input type="checkbox" name="updateOption" value="importHotelPicture" checked> 导入酒店图片
						</div>
						</#if>
					</#if>
					<div class="modal-footer">
						<button type="button" class="btn btn-default" data-dismiss="modal">关闭
						</button>
						<button type="button" class="btn btn-primary" onclick="addTcsHotel('${chainCode}')">提交
						</button>
					</div>
				</div><!-- /.modal-content -->
			</div><!-- /.modal -->
		</div>
	</div>
	
	<div class="modal fade" id="uploadExcelModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content" style="width: 840px;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">上传文件</h4>
			</div>
			<div class="modal-body" style="padding-bottom: 8px;">
				<form id="uploadExcelForm" class="form-horizontal" method="post" action="" enctype="multipart/form-data" target="uploadframe">
					<fieldset>
						<div class="form-group">
							<div class="col-md-10">
								<input class="form-control" type="file" id="file" name="file" />
							</div>
							<div class="col-md-2">
								<input type="button" onclick="uploadExcel('${chainCode}')" class="btn btn-success" value="上传" />
							</div>
						</div>
						<div class="form-group">
							<div class="superbox col-sm-12">xls和xlsx文件均可，请从第一个sheet的第一列第一行开始，每行一个酒店code</div>
						</div>
						<div class="form-group modal-header">
							<h4 class="modal-title">同步选项</h4>
						</div>
						<div class="form-group modal-body pre-scrollable">
							<div>
								<input type="checkbox" name="updateOptionUpload" value="downloadHotelXml" checked> 下载酒店xml文件
							</div>
							<div>
								<input type="checkbox" name="updateOptionUpload" value="importHotelXml" checked> 更新酒店和房型基本信息
							</div>
							<#if chainCode!="PF">
								<div>
									<input type="checkbox" name="updateOptionUpload" value="importLandMarkAndTrsansport" checked> 更新交通和地标基本信息
								</div>
							</#if>
							<div>
								<input type="checkbox" name="updateOptionUpload" value="syncCountryFromHotel" checked> 更新国家和城市基本信息
							</div>
							<#if chainCode!="PF">
								<div>
									<input type="checkbox" name="updateOptionUpload" value="importRoomPicture" checked> 导入房型图片
								</div>
								
								<div>
									<input type="checkbox" name="updateOptionUpload" value="downloadFtpHotelImage" checked> 下载酒店图片ftp文件
								</div>
								<#if chainCode!="LX">
								<div>
									<input type="checkbox" name="updateOptionUpload" value="importHotelPicture" checked> 导入酒店图片
								</div>
								</#if>
							</#if>
						</div><!-- /.modal-content -->
					</fieldset>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
			</div>
		</div>
	</div>
</div>
	
	<@foot.foot basePath="${basePath}"></@foot.foot>
	<script src="${basePath}/static/js/sockjs.0.3.4.min.js"></script>
	<script src="${basePath}/static/js/websocket.js"></script>
	<script type="text/javascript">
		$(document).ready(function() {
			/* var $hids = $('#hids');
			var $hrows = $('input[type=checkbox][name=hid]');
			$hids.on('change', function() {
				if ($hids.prop('checked')) {
					$hrows.prop('checked', true);
				} else {
					$hrows.prop('checked', false);
				}
			}); */
			
			websocketConnect();
		});
		
		function afterWebSocketOpen(){
			var message = {event:"updateTcsHotelList",data:{roomId:"updateTcsHotelList"}}
			ws.send(JSON.stringify(message));
		}
		
		function showUpdateTcsHotelListDivAdd(){
			var hotelCode = $('#hcode').val();
			var scode = $('#scode').val();
			var hotelEnName = $('#hotelEnName').val();
			if(hotelCode == null || hotelCode == ""){  
				alert('请输入酒店代码！');
			}else if(scode == null || scode == ""){  
				alert('请输入酒店Synxis code！');
			}else if(hotelEnName == null || hotelEnName == ""){  
				alert('请输入酒店英文名称！');
			}else{
				$('#updateTcsHotelListDiv').modal('show');
			}
		}
		
		function showUpdateTcsHotelListDivSyn(){
			$('#updateTcsHotelListDiv').modal('show');
		}
		
		function updateTcsHotelList(chainCode) {
			var crsApi = '';
			if(chainCode=='LX'){
				crsApi = 'sabre';
			}else{
				crsApi = 'trust';
			}
			var updateOptions = '';
			$('input[name="updateOption"]:checked').each(function(){
				updateOptions += ','+($(this).val());
				});
			$.ajax({
				async : true,
				url : '${basePath}/hotel/updateTcsHotelList?updateOptions='+updateOptions,
				type : 'GET',
				data : {
					chainCode : chainCode,
					crsApi :  crsApi,
				},
				dataType : "text",
				success : function(data) {
					console.log("updateTcsHotelList: " + data);
					alert("请求已提交！");$('#updateTcsHotelListDiv').modal('hide');
				},
				error : function(data) {
					console.log("updateTcsHotelList: " + data);
				}
			});
		}
		
		function addTcsHotel(chainCode) {
			var crsApi = '';
			if(chainCode=='LX'){
				crsApi = 'sabre';
				var updateOptions = '';
				$('input[name="updateOption"]:checked').each(function(){
					updateOptions += ','+($(this).val());
					});
				var hotelCode = $('#hcode').val();
				var scode = $('#scode').val();
				var hotelEnName = $('#hotelEnName').val();
				$.ajax({
					async : true,
					url : '${basePath}/hotel/addTcsHotel?updateOptions='+updateOptions,
					type : 'GET',
					data : {
						chainCode : chainCode,
						sabreCode : scode,
						hotelCode : hotelCode,
						hotelEnName : hotelEnName,
						crsApi :  crsApi,
					},
					dataType : "text",
					success : function(data) {
						console.log("addTcsHotel: " + data);
						$('#updateTcsHotelListDiv').modal('hide');
// 						var rs = "导入结果：\n";
// 						if (data.code == "success") {
// 							rs += data.desc;
// 						}
						alert("导入成功");
// 						log("请求已提交！");
					},
					error : function(data) {
						console.log("addTcsHotel: " + data);
					}
				});
			}
		}
		
		function showUploadDiv(){
			$("#uploadExcelModal").modal();
		}

		function uploadExcel(chainCode){
			if(chainCode=='LX'){
				var chainCode = 'LX';
				var crsApi = 'sabre';
				var formData = new FormData($("#uploadExcelForm")[0]);
				var updateOptions = '';
				$('input[name="updateOptionUpload"]:checked').each(function(){
					updateOptions += ','+($(this).val());
					});
				var url = '${basePath}/hotel/addTcsHotelExcel?updateOptions='+updateOptions+'&crsApi='+crsApi+'&chainCode='+chainCode;
			    $.ajax({  
			         url: url,
			         type: 'POST',  
			         data: formData,  
			         async: false,  
			         cache: false,//上传文件不需要缓存。
			         contentType: false,//因为是由<form>表单构造的FormData对象，且已经声明了属性enctype="multipart/form-data"，所以这里设置为false
			         processData: false,//因为data值是FormData对象，不需要对数据做处理
					 dataType:"json",
			         success: function (data) {  
			             alert(data.desc);
			             console.log(JSON.stringify(data));
			             if(data.code=="ok"){
			            	$("#uploadExcelModal").modal('hide');
			            	$("#uploadExcelForm file").val("");
			            	// location.reload(true);
			             }
			         },  
			         error: function (data) {
			             console.log(JSON.stringify(data));
			        	 console.log(data.status+" "+data.statusText);
			        	 console.log(data.responseText); 
			             alert(data.status+" "+data.statusText+" "+data.responseText);  
			         }  
			    }); 
			}
		}
	</script>
</body>
</html>