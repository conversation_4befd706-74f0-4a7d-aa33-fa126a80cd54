<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}"></@head.head>
<body>
<div id="main" role="main" style="margin: 16px 0px 0px 8px;">
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row" style="margin: -12px -38px -72px -20px">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<div class="widget-body no-padding" style="border-top: 1px solid #ccc;">
							<div class="widget-body-toolbar" style="display: none;">
								<form id="queryForm" class="form-inline" style="text-align: right;" method="post" action="${basePath}/hotel/room/select">
									<input type="hidden" id="start" name="start" value="${page.currentPage}" />
									<input type="hidden" id="limit" name="limit" value="${limit}" />
									<input type="hidden" id="hotelCode" name="hotelCode" value="${hotelCode}" />
								</form>
							</div>
							<table class="table table-striped table-bordered table-hover">
								<thead>
									<tr>
										<th>房型Code</th>
										<th>房型名</th>
									    <th>房型英文名</th>
									    <th>中文描述</th>
									    <th>英文描述</th>
									    <th width="100">操作</th>
									</tr>
								</thead>
								<tbody>
									<#list page.datas as data>
									<tr class="small">
										<td>${data.roomCode?if_exists}</td>
	                                    <td>${data.roomName?if_exists}</td>
	                                    <td>${data.roomNameEn?if_exists}</td>
	                                    <td>${data.roomDesc?if_exists}</td>
	                                    <td>${data.roomDescEn?if_exists}</td>
	                                    <td style="text-align: center;">
											<a class="btn btn-info selectBtn" href="javascript:void(0)" 
								              	data-code="${data.roomCode?if_exists}" data-name="${data.roomName?if_exists}">选择</a>
	                                    </td>
									</tr>
									</#list>
								</tbody>
							</table>
							<#include "/common/page.ftl" />
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	$('table tr.small a.selectBtn').on('click', function() {
		var $obj = $(this);
		selectRoom($obj);
	});
	$('table tr.small').on('dblclick', function() {
		var $obj = $(this).find('a.selectBtn');
		selectRoom($obj);
	});
	function selectRoom($obj) {
		var code = $obj.attr('data-code');
		var name = $obj.attr('data-name');
        parent.selectRoomInfo(code, name);
	}
});
function toPage(pageNo) {
	$('#start').val(pageNo);
	$('#queryForm').submit();
}
</script>