<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}"> </@head.head>
<body>
	<#include "/common/header.ftl" />
	<#include "/common/menu.ftl" />
	<div id="main" role="main">
		<div id="ribbon">
			<ol class="breadcrumb">
				<li>${itemName}</li>
			</ol>
		</div>
		<div id="content">
			<input type="hidden" id="itemCode" name="itemCode"
				value="${itemCode}" />
			<section id="widget-grid" class="">
				<div class="row">
					<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
						<div class="jarviswidget jarviswidget-color-darken"
							id="hotel-edit-1" data-widget-editbutton="false"
							data-widget-sortable="false" data-widget-colorbutton="false"
							data-widget-fullscreenbutton="false"
							data-widget-deletebutton="false">
							<header>
								<span class="widget-icon"> <i class="fa fa-table"></i>
								</span>
								<h2>修改酒店信息</h2>
							</header>
							<div>
								<div class="jarviswidget-editbox"></div>
								<div class="widget-body" style="margin-left: -30px;">
									<form id="editForm" class="form-horizontal" method="post"
										action="${basePath}/hotel/update">
										<fieldset>
											<input class="form-control" type="text" id="chainCode"
												name="chainCode" value="${hotel.chainCode?if_exists}"
												style='display: none' /> <input class="form-control"
												type="text" id="hotelCode" name="hotelCode"
												value="${hotel.hotelCode?if_exists}"
												style='display: none' />
											<div class="form-group">
												<label class="col-md-2 control-label" for="name"
													style="font-size: 16px;" check-type="required"
													required-message="名称不能为空。">酒店中文名</label>
												<div class="col-md-10">
													<input class="form-control" type="text" id="name"
														value="${hotel.hotelName?if_exists}" />(如果需要酒店名称换行显示请插入&lt;br&gt;)
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="nameEn"
													style="font-size: 16px;">酒店英文名</label>
												<div class="col-md-10">
													<input class="form-control" type="text" id="nameEn"
														value="${hotel.hotelNameEn?if_exists}" />
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="hotelAliasEn"
													style="font-size: 16px;">酒店英文别名</label>
												<div class="col-md-10">
													<input class="form-control" type="text" id="hotelAliasEn"
														value="${hotel.hotelAliasEn?if_exists}" />
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="hotelNamePinyin"
													style="font-size: 16px;">酒店名拼音</label>
												<div class="col-md-10">
													<input class="form-control" type="text"
														id="hotelNamePinyin"
														value="${hotel.hotelNamePinyin?if_exists}" />
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="hotelLink"
													style="font-size: 16px;">hotelLink</label>
												<div class="col-md-10">
													<input class="form-control" type="text"
														id="hotelLink"
														value="${hotel.hotelLink?if_exists}" />
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="hotelLinkOld"
													style="font-size: 16px;">hotelLinkOld</label>
												<div class="col-md-10">
													<input class="form-control" type="text"
														id="hotelLinkOld"
														value="${hotel.hotelLinkOld?if_exists}" />(多个用英文逗号,隔开)
												</div>
											</div>
											<!-- 
										<div class="form-group">
											<label class="col-md-2 control-label" for="banner" style="font-size: 16px;">酒店默认图片</label>
											<input type="hidden" id="coverUrl" name="roomCoverUrl" value="${hotel.coverUrl?if_exists}"/>
											<div class="col-md-10">
												<input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${hotel.coverUrl?if_exists}" value="查看" />
											</div>
										</div>
										 -->
										 <div class="form-group">
												<label class="col-md-2 control-label"
													style="font-size: 16px;">国家代码</label>
												<div class="col-md-10">
													${hotel.countryCode?if_exists}
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label"
													style="font-size: 16px;">城市代码</label>
												<div class="col-md-10">
													${hotel.cityCode?if_exists}
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="countryCity"
													style="font-size: 16px;">国家&城市</label>
												<div class="col-md-10">
													<select class="form-control" id="countryCity"
														name="countryCity">
														<option value="">请选择国家和城市</option>
														<#list countries as datax> 
															<#list datax.cities as datar>
															<option data-code="${datax.countryCode}"
																data-name="${datax.countryName}"
																data-city="${datar.cityName}" value="${datar.cityCode}"
																<#if datar.cityCode==hotel.cityCode?if_exists>
																selected="selected"</#if>>${datax.countryName} -
																${datar.cityName}
															</option>
															</#list>
														</#list>
													</select>
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="cover"
													style="font-size: 16px;">状态</label>
												<div class="col-md-10">
													<input type="radio" id="is_online_1" name="onlineStatus"
														value="online"
													<#if hotel.onlineStatus=="online">checked="true"</#if>
													/><span style="font-size: 16px;">上线</span> <input
														type="radio" id="is_online_0" name="onlineStatus"
														value="offline"
													<#if hotel.onlineStatus=="offline">checked="true"</#if>
													/><span style="font-size: 16px;">下线</span> <input
														type="radio" id="is_online_2" name="onlineStatus"
														value="new"
													<#if hotel.onlineStatus=="new">checked="true"</#if>
													/><span style="font-size: 16px;">新增</span>
												</div>
											</div>
											<div id="conditionLayer" class="form-group"
												<#if hotel.onlineStatus!="online"> style="display:
												none;"</#if>
												> <label class="col-md-2 control-label" for="cover"
													style="font-size: 16px;">情况</label>
												<div class="col-md-10">
													<input type="radio" id="condition0" name="condition"
														value="0"
													<#if "0"==hotel.conditionCode?if_exists>checked="true"</#if>
													/><span style="font-size: 16px;">营业中</span> <input
														type="radio" id="condition1" name="condition" value="1"
													<#if "1"==hotel.conditionCode?if_exists>checked="true"</#if>
													/><span style="font-size: 16px;">装修中</span> <input
														type="radio" id="condition2" name="condition" value="2"
													<#if "2"==hotel.conditionCode?if_exists>checked="true"</#if>
													/><span style="font-size: 16px;">尚未开业</span>
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="cover"
													style="font-size: 16px;">接收银联</label>
												<div class="col-md-10">
													<input type="radio" id="is_up_pay_1" name="is_up_pay"
														value="1"
													<#if hotel.isUpPay?if_exists==1>checked="true"</#if>
													/><span style="font-size: 16px;">接收</span> <input
														type="radio" id="is_up_pay_0" name="is_up_pay" value="0"
													<#if hotel.isUpPay?if_exists==0>checked="true"</#if>
													/><span style="font-size: 16px;">不接收</span>
												</div>
											</div>
											<div id="remarkLayer" class="form-group"
												<#if hotel.onlineStatus!="online"> style="display:
												none;"</#if>
												> <label class="col-md-2 control-label" for="remark"
													style="font-size: 16px;">说明</label>
												<div class="col-md-10">
													<input class="form-control" type="text" id="remark"
														value="${hotel.remark?if_exists}" />
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="address"
													style="font-size: 16px;">地址</label>
												<div class="col-md-10">
													<input class="form-control" type="text" id="address"
														value="${hotel.address?if_exists}" />
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="addressEn"
													style="font-size: 16px;">英文地址</label>
												<div class="col-md-10">
													<input class="form-control" type="text" id="addressEn"
														value="${hotel.addressEn?if_exists}" />
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="postal_code"
													style="font-size: 16px;">邮编</label>
												<div class="col-md-10">
													<input class="form-control" type="text" id="postal_code"
														value="${hotel.postalCode?if_exists}" />
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="phone"
													style="font-size: 16px;">电话</label>
												<div class="col-md-10">
													<input class="form-control" type="text" id="phone"
														value="${hotel.phone?if_exists}" />
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="fax"
													style="font-size: 16px;">传真</label>
												<div class="col-md-10">
													<input class="form-control" type="text" id="fax"
														value="${hotel.fax?if_exists}" />
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="email"
													style="font-size: 16px;">邮件地址</label>
												<div class="col-md-10">
													<input class="form-control" type="text" id="email"
														value="${hotel.email?if_exists}" />
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="hotel_desc"
													style="font-size: 16px;">酒店描述</label>
												<div class="col-md-10">
													<textarea class="form-control" id="hotel_desc"
														name="hotel_desc" style="height: 180px;">${hotel.hotelDesc?if_exists}</textarea>
												</div>
											</div>
											<#if chainCode!="LW">
											<div class="form-group">
												<label class="col-md-2 control-label" for="hasPools"
													style="font-size: 16px;">泳池</label>
												<div class="col-md-10">
													<input id="hasPools" name="hasPools" type="checkbox"
													<#if hotel.hasPools?string('true','false')=='true'>checked="checked"</#if>
													></input>
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label"
													for="michelinStarredRestaurants" style="font-size: 16px;">米其林餐厅</label>
												<div class="col-md-10">
													<input id="michelinStarredRestaurants"
														name="michelinStarredRestaurants" type="checkbox"
													<#if hotel.michelinStarredRestaurants?string('true','false')=='true'>checked</#if>></input>
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" 
													style="font-size: 16px;">briefEn</label>
												<div class="col-md-10 ">
													${hotel.briefEn?if_exists}
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="brief"
													style="font-size: 16px;">brief</label>
												<div class="col-md-10">
													<textarea class="form-control" id="brief" name="brief"
														style="height: 60px;">${hotel.brief?if_exists}</textarea>
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" 
													style="font-size: 16px;">uniqueQuelitiesEn</label>
												<div class="col-md-10 ">
													${hotel.uniqueQuelitiesEn?if_exists}
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="uniqueQuelities"
													style="font-size: 16px;">uniqueQuelities</label>
												<div class="col-md-10">
													<textarea class="form-control" id="uniqueQuelities"
														name="uniqueQuelities" style="height: 120px;">${hotel.uniqueQuelities?if_exists}</textarea>
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" 
													style="font-size: 16px;">detailEn</label>
												<div class="col-md-10 ">
													${hotel.detailEn?if_exists}
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="detail"
													style="font-size: 16px;">detail</label>
												<div class="col-md-10">
													<textarea class="form-control" id="detail" name="detail"
														style="height: 180px;">${hotel.detail?if_exists}</textarea>
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="detail"
													style="font-size: 16px;">英文站酒店详情页Url</label>
												<div class="col-md-10">
													<input type="text" class="form-control" id="websiteUrl"
														name="websiteUrl" value="${hotel.websiteUrl?if_exists}"></input>
												</div>
											</div>
											</#if>
											<div class="form-group">
												<label class="col-md-2 control-label" for="virusPolicy"
													   style="font-size: 16px;">抗疫政策</label>
												<div class="col-md-10">
													<textarea class="form-control" id="virusPolicy" name="virusPolicy"
															  style="height: 180px;">${hotel.virusPolicy?if_exists}</textarea>
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-2 control-label" for="emergencyNotice"
													   style="font-size: 16px;">紧急通知</label>
												<div class="col-md-10">
													<textarea class="form-control" id="emergencyNotice" name="emergencyNotice"
															  style="height: 100px;">${hotel.emergencyNotice?if_exists}</textarea>
												</div>
											</div>
										</fieldset>
										<div class="form-actions">
											<div class="row">
												<div class="col-md-12">
													<input type="button" class="btn btn-primary"
														value="从官网下载酒店信息" id="downloadHotelInfo"
														style="margin-left: 12px; float: left;" /> <input
														type="button" class="btn btn-primary" value="从ftp下载酒店图片"
														onclick="javascript:downloadFtpHotelImage()"
														style="margin-left: 12px; float: left;" /><#if chainCode=="LX"> <input
														type="button" class="btn btn-primary" value="下载酒店xml、更新酒店、房型、地标、交通信息"
														onclick="javascript:importTcsContentXmlSLH()"
														style="margin-left: 12px; float: left;" /><#else> <input
														type="button" class="btn btn-primary" value="下载酒店xml、更新酒店、房型、地标、交通信息"
														onclick="javascript:importTcsContentXml()"
														style="margin-left: 12px; float: left;" /></#if> <input
														type="button" class="btn btn-primary" value="保存"
														id="itemAdd" style="margin-right: 12px;" /> <input
														type="button" class="btn btn-default" value="返回"
														onclick="javascript:toHotelList()" />
												</div>
											</div>
										</div>
									</form>
								</div>
							</div>
							<#if chainCode!="LW">
							<div class="row">
								<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
									<div class="jarviswidget jarviswidget-color-darken"
										id="travel-item-2" data-widget-editbutton="false"
										data-widget-sortable="false" data-widget-colorbutton="false"
										data-widget-fullscreenbutton="false"
										data-widget-deletebutton="false">
										<header>
											<span class="widget-icon"> <i class="fa fa-table"></i>
											</span>
											<h2>酒店landmark维护</h2>
										</header>
										<div>
											<div class="jarviswidget-editbox"></div>
											<div class="widget-body">
												<table id="dt_basic"
													class="table table-striped table-bordered table-hover overseaGrid">
													<thead>
														<tr>
															<th>ID</th>
															<th>英文名</th>
															<th>中文名</th>
															<th>操作</th>
														</tr>
													</thead>
													<tbody>
														<#list hotel.landmarks as landmark>
														<tr class="small">
															<td>${landmark.id?if_exists}</td>
															<td>${landmark.nameEn?if_exists}</td>
															<td>${landmark.name?if_exists}</td>
															<td width="240" style="text-align: center;"><a
																class="btn btn-warning"
																href="${basePath}/hotel/toLandmarkInfo?id=${landmark.id}"
																style="margin: 0px 12px;">修改</a></td>
														</tr>
														</#list>
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</article>
							</div>

							<div class="row">
								<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
									<div class="jarviswidget jarviswidget-color-darken"
										id="travel-item-2" data-widget-editbutton="false"
										data-widget-sortable="false" data-widget-colorbutton="false"
										data-widget-fullscreenbutton="false"
										data-widget-deletebutton="false">
										<header>
											<span class="widget-icon"> <i class="fa fa-table"></i>
											</span>
											<h2>酒店transport维护</h2>
										</header>
										<div>
											<div class="jarviswidget-editbox"></div>
											<div class="widget-body">
												<table id="dt_basic"
													class="table table-striped table-bordered table-hover overseaGrid">
													<thead>
														<tr>
															<th>ID</th>
															<th>英文名</th>
															<th>中文名</th>
															<th>操作</th>
														</tr>
													</thead>
													<tbody>
														<#list hotel.transports as transport>
														<tr class="small">
															<td>${transport.id?if_exists}</td>
															<td>${transport.nameEn?if_exists}</td>
															<td>${transport.name?if_exists}</td>
															<td width="240" style="text-align: center;"><a
																class="btn btn-warning"
																href="${basePath}/hotel/toTransportInfo?id=${transport.id}"
																style="margin: 0px 12px;">修改</a></td>
														</tr>
														</#list>
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</article>
							</div>
							</#if>

							<div class="widget-body">
								<div class="alert-heading" style="margin-bottom: 4px;">服务器日志：</div>
								<textarea id="serverLog" rows="10" cols="120"></textarea>
							</div>
						</div>
					</article>
				</div>
			</section>
		</div>
	</div>
	<#include "/common/utils.ftl" />
	<@foot.foot basePath="${basePath}"></@foot.foot>
	<script src="${basePath}/static/js/sockjs.0.3.4.min.js"></script>
	<script src="${basePath}/static/js/websocket.js"></script>
	<script type="text/javascript">
		var type = 'hotel-cover';
		$(document).ready(
			function() {
				$("#itemAdd").click(function() {
					updateHotel();
				});

				$("#downloadHotelInfo").click(function() {
					downloadHotelInfo();
				});
				var $selectBannerModal = $('#selectBannerModal');
				var $itemBanner = $('#coverUrl');
				var selectBanner = function() {
					var params = {};
					params.relateType = type;
					$.post('${basePath}/image/list.json', params, function(
							datas) {
						$selectBannerModal.find('.superbox').html(
								convertImageHtml(datas));
					}, 'json');
					$selectBannerModal.find('input[name=relateType]').val(
							type);
					$selectBannerModal.find('input[name=relateCode]').val(
							$('#itemCode').val());
					$selectBannerModal.find('input[name=format]').val(
							'269*168c.jpg');
					$selectBannerModal.find('#imgsize').text('269 x 168');
					$selectBannerModal.find('.modal-title').text('添加封面');
					$selectBannerModal.modal();
				};

				$('#selectBannerBtn').on('click', selectBanner);
				$itemBanner.on('click', selectBanner);

				$("input:radio[name='is_online']").on('click', function() {
					var $obj = $(this);
					if ($obj.val() == '1') {
						$('#conditionLayer').show();
						$('#remarkLayer').show();
						/**
						if($("input:radio[name='condition']:checked").val() == '0') {
							$('#remarkLayer').hide();
						} else {
							$('#remarkLayer').show();
						}
						 **/
					} else {
						$('#conditionLayer').hide();
						$('#remarkLayer').hide();
					}
				});
				/**
				$("input:radio[name='condition']").on('click', function() {
					var $obj = $(this);
					if($obj.val() == '0') {
						$('#remarkLayer').hide();
					} else {
						$('#remarkLayer').show();
					}
				});
				 **/
	
				websocketConnect();
			});

		function afterWebSocketOpen(){
			var message = {event:"downloadHotelInfo",data:{roomId:"downloadHotelInfo_"+$("#hotelCode").val()}}
			ws.send(JSON.stringify(message));
			
			message = {event:"downloadFtpHotelImage",data:{roomId:"downloadFtpHotelImage_"+$("#hotelCode").val()}}
			ws.send(JSON.stringify(message));
		}
		
		function selectImage(url) {
			$('#coverUrl').val(url);
			$('.viewBanner').attr('data-img', '${imageUrl}/' + url);
			$('#selectBannerModal').modal('hide');
		};
		
		function updateHotel() {
			var $countryCity = $('#countryCity option:selected');
			$.ajax({
				async : false,
				type : "POST",
				dataType : "json",
				data : {
					chainCode : $("#chainCode").val(),
					hotelCode : $("#hotelCode").val(),
					hotelName : $("#name").val(),
					hotelNameEn : $("#nameEn").val(),
					hotelAliasEn : $("#hotelAliasEn").val(),
					hotelNamePinyin : $("#hotelNamePinyin").val(),
					hotelLink : $("#hotelLink").val(),
					hotelLinkOld : $("#hotelLinkOld").val(),
					countryCode : $countryCity.attr('data-code'),
					countryName : $countryCity.attr('data-name'),
					cityCode : $countryCity.val(),
					cityName : $countryCity.attr('data-city'),
					addressEn : $("#addressEn").val(),
					onlineStatus : $(
							"input:radio[name='onlineStatus']:checked")
							.val(),
					isUpPay : $("input:radio[name='is_up_pay']:checked")
							.val(),
					conditionCode : $(
							"input:radio[name='condition']:checked")
							.val(),
					remark : $("#remark").val(),
					address : $("#address").val(),
					phone : $("#phone").val(),
					fax : $("#fax").val(),
					postalCode : $("#postal_code").val(),
					email : $("#email").val(),
					hotelDesc : $("#hotel_desc").val(),
					coverUrl : $("#coverUrl").val(),
					brief : $("#brief").val(),
					uniqueQuelities : $("#uniqueQuelities").val(),
					detail : $("#detail").val(),
					websiteUrl : $("#websiteUrl").val(),
					hasPools : $("#hasPools").is(':checked'),
					michelinStarredRestaurants : $(
							"#michelinStarredRestaurants").is(
							':checked'),
					virusPolicy : $("#virusPolicy").val(),
					emergencyNotice : $("#emergencyNotice").val(),
				},
				url : "${basePath}/hotel/update",
				error : function(data) {
					return "执行失败";
				},
				success : function(data) {
					if (data.code == 1) {
						alert(data.desc);
						window.location = "${basePath}/hotel/list";
						return false;
					} else {
						alert(data.desc);
						return false;
					}
				}
			});
		}
		
		function downloadHotelInfo() {
			var $countryCity = $('#countryCity option:selected');
			$.ajax({
				async : true,
				type : "POST",
				dataType : "json",
				data : {
					chainCode : $("#chainCode").val(),
					hotelCode : $("#hotelCode").val(),
				},
				url : "${basePath}/hotel/downloadHotelInfo",
				error : function(data) {
					return "执行失败";
				},
				success : function(data) {
					if (data.code == 1) {
						alert(data.desc);
						window.location.reload();
						return false;
					} else {
						alert(data.desc);
						return false;
					}
				}
			});
		}

		function downloadFtpHotelImage() {
			$.ajax({
				async : true,
				type : "POST",
				dataType : "json",
				data : {
					hotelCode : $("#hotelCode").val(),
				},
				url : "${basePath}/hotel/downloadFtpHotelImage",
				error : function(data) {
					return "执行失败";
				},
				success : function(data) {
					if (data.code == 1) {
						alert(data.desc);
						return false;
					} else {
						alert(data.desc);
						return false;
					}
				}
			});
		}

		function importTcsContentXml() {
			$.ajax({
				async : true,
				type : "POST",
				dataType : "json",
				data : {
					hotelCode : $("#hotelCode").val(),
				},
				url : "${basePath}/hotel/importTcsContentXml",
				error : function(data) {
					return "执行失败";
				},
				success : function(data) {
					if (data.code == 1) {
						alert(data.desc);
						return false;
					} else {
						alert(data.desc);
						return false;
					}
				}
			});
		}
		
		function importTcsContentXmlSLH() {
			$.ajax({
				async : true,
				type : "POST",
				dataType : "json",
				data : {
					hotelCode : $("#hotelCode").val(),
					chainCode : $("#chainCode").val(),
				},
				url : "${basePath}/hotel/importTcsContentXmlSLH",
				success : function(data) {
					console.log("updateTcsHotelList: " + data);
					if (data.code == 'success') {
						alert(data.desc);
					}
				},
				error : function(data) {
					console.log("updateTcsHotelList: " + data);
					alert("导入中，请稍后查看");
				}
			});
		}

		function toHotelList() {
			window.location = "${basePath}/hotel/list";
		}
	</script>
</body>
</html>