<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>图片管理</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="images-photo-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>图片管理</h2>
						</header>
						<div>
							<div class="widget-body">
								<!-- <div class="col-sm-12 alert alert-block alert-warning">
									<h4 class="alert-heading" style="margin-bottom: 4px;">酒店图片</h4>
									<#if chainCode=="LW">
									<span>提示：拖动以下图片，点“保存排序及隐藏状态”按钮，可对图片进行排序，还可以控制图片的已隐藏及隐藏状态。前3张酒店图片在酒店详情页展示为大幅滚屏。</span>
									</#if>
								</div> -->
								<form id="editForm" class="form-horizontal">
								</form>
								<div class="widget-body no-padding">
									<div class="widget-body-toolbar">
										<form id="queryForm" class="form-inline" method="post" action="${basePath}/hotel/images/search">
											<input type="hidden" id="start" name="start" value="${page.currentPage}" />
											<input type="hidden" id="limit" name="limit" value="${limit}" />
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
									        	<input type="text" class="form-control" id="hotelCode" name="hotelCode" value="${hotelCode?if_exists}" placeholder="酒店代码" />
									       	</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
							                	<input type="text" class="form-control" name="name" value="${name?if_exists}" placeholder="酒店中文名" />
						                	</div><div class="form-group" style="margin: 0px 15px 8px 0px;">
						                	<input type="text" class="form-control" name="enname" value="${enname?if_exists}" placeholder="酒店英文名" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<select class="form-control" name="city">
					                			<option value="">请选择国家和城市</option>
					                			<#list countries as datax>
					                				<#list datax.cities as data>
					                					<option value="${data.cityCode?if_exists}"<#if data.cityCode==city> selected="selected"</#if>>${datax.countryName?if_exists} - ${data.cityName?if_exists}</option>
					                				</#list>
					                			</#list>
					                		</select>
					                	</div>
										<!-- <div class="form-group" style="width: 600px;margin: 0px 0px 8px 0px;">
											<label for="maxAspectRatio" class="col-sm-6 control-label">包括无宽高值的图片(处理失败的图片):</label>
											<div class="col-sm-4">
												<label class="checkbox-inline">
													<input type="radio" name="noWidth" id="noWidth" value="true" <#if noWidth?if_exists>checked</#if>> 是
												</label>
												<label class="checkbox-inline">
													<input type="radio" name="noWidth" id="noWidth" value="false" <#if !noWidth?if_exists>checked</#if>> 否
												</label>
											</div>
										</div> -->
										<br>
										<div class="form-group" style="width: 400px;margin: 0px 0px 8px 0px;">
											<label for="exportType" class="col-sm-4 control-label">查询限制:</label>
											<div class="col-sm-8">
												<select class="form-control" id="exportType"name="exportType" >
													<option value="include" <#if exportType=="include">selected</#if>>符合以下条件</option>
													<option value="exclude" <#if exportType=="exclude">selected</#if>>不符合以下条件</option>
												</select>
											</div>
										</div>
										<br/>
										<div class="form-group" style="width: 400px;margin: 0px 0px 8px 0px;">
											<label for="minWidth" class="col-sm-4 control-label">最小宽度(像素):</label>
											<div class="col-sm-8">
												<input type="text" class="form-control" id="minWidth"
													name="minWidth" value="${minWidth?if_exists}" placeholder="请输入整数"/>
											</div>
										</div>
										<div class="form-group" style="width: 400px;margin: 0px 0px 8px 0px;">
											<label for="minHeight" class="col-sm-4 control-label">最小高度(像素):</label>
											<div class="col-sm-8">
												<input type="text" class="form-control" id="minHeight"
													name="minHeight" value="${minHeight?if_exists }" placeholder="请输入整数"/>
											</div>
										</div>
										<div class="form-group" style="width: 400px;margin: 0px 0px 8px 0px;">
											<label for="maxWidth" class="col-sm-4 control-label">最大宽度(像素):</label>
											<div class="col-sm-8">
												<input type="text" class="form-control" id="maxWidth"
													name="maxWidth" value="${maxWidth?if_exists }" placeholder="请输入整数"/>
											</div>
										</div>
										<div class="form-group" style="width: 400px;margin: 0px 0px 8px 0px;">
											<label for="maxHeight" class="col-sm-4 control-label">最大高度(像素):</label>
											<div class="col-sm-8">
												<input type="text" class="form-control" id="maxHeight"
													name="maxHeight" value="${maxHeight?if_exists }" placeholder="请输入整数"/>
											</div>
										</div>
										<div class="form-group" style="width: 400px;margin: 0px 0px 8px 0px;">
											<label for="minAspectRatio" class="col-sm-4 control-label">最小宽高比:</label>
											<div class="col-sm-8">
												<input type="text" class="form-control" id="minAspectRatio"
													name="minAspectRatio" value="${minAspectRatio?if_exists }" placeholder="请输入数字"/>
											</div>
										</div>
										<div class="form-group" style="width: 400px;margin: 0px 0px 8px 0px;">
											<label for="maxAspectRatio" class="col-sm-4 control-label">最大宽高比:</label>
											<div class="col-sm-8">
												<input type="text" class="form-control" id="maxAspectRatio"
													name="maxAspectRatio" value="${maxAspectRatio?if_exists}" placeholder="请输入数字"/>
											</div>
										</div>
					                	<div class="form-group" style="width: 400px;margin: 0px 0px 8px 0px;">
											<label for="firstname" class="col-sm-4 control-label">图片类型:</label>
											<div class="col-sm-8">
												<select class="form-control" id="imageType"name="imageType" >
													<!-- <option value="" <#if imageType=="">selected</#if>>所有图片</option> -->
													<option value="1" <#if imageType=="1">selected</#if>>酒店图片</option>
													<!-- <option value="2" <#if imageType=="2">selected</#if>>房型图片</option> -->
													<option value="3" <#if imageType=="3">selected</#if>>国家图片</option>
												</select>
											</div>
										</div>
										<br/>
						                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
						                		<select class="form-control" id="hidden" name="hidden">
						                			<option value="">全部</option>
						                			<option value="0" "<#if hidden?string('true','false')=='false'> selected="selected"</#if>>在线</option>
						                			<option value="1" "<#if hidden?string('true','false')=='true'> selected="selected"</#if>>隐藏</option>
						                		</select>
						                	</div>
									<div class="form-group">
										<div class="col-sm-12" style="text-align: right;">
										</div>
									</div>
						                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
						                		<input type="submit" class="btn btn-primary" value="查询" style="margin-right: 8px;" />
						                		<input type="button" class="btn btn-info" value="批量隐藏" style="margin-right: 12px;" onclick="javascript:saveHidden(true);"/>
						                		<input type="button" class="btn btn-info" value="批量显示" style="margin-right: 12px;" onclick="javascript:saveHidden(false);"/>
												<input type="button" class="btn btn-primary" value="图片导出" onclick="exportImages()" style="margin-right: 8px;" />
						                	</div>
										</form>
									</div>
									<table id="dt_basic" class="table table-striped table-bordered table-hover">
										<thead>
											<tr>
												<th width="20"><input type="checkbox" id="hids" /></th>
												<th width="140">原图</th>
												<th width="140">1024*510缩略图</th>
												<th width="140">330*230缩略图</th>
												<th width="20">宽</th>
												<th width="20">高</th>
												<th width="100">操作</th>
											</tr>
										</thead>
										<tbody>
											<#list page.datas as data>
											<tr class="" style="height:140px;">
												<td>
													<input type="checkbox" name="hid" value="${data.id?if_exists}" />
												</td>
												<td>
													<div  style="float:left;"  data-id="${data.id}" data-hide="${data.hidden?string('true','false')}">
														<img src="${imageUrl}/${data.url?if_exists}" style="width:240px;" data-img="${imageUrl}/${data.url?if_exists}" title="${data.name?if_exists}" class="superbox-img viewBanner">
														<div style="margin-top: 4px;line-height: 24px; font-weight: bolder;">${data.name?if_exists}</div>
														<div class="imgif" style="margin-top: 4px;line-height: 24px;"><!-- 1600px × 558px 1600px × 550px --></div>
														<div style="margin-top: 4px;">
															<a style="float: left;" href="${basePath}/hotel/image-edit?hotelCode=${hotelCode}&id=${data.id?if_exists}">修改</a>
															<div style="float: left;margin-left:4px;;" class="hidectl" href="javascript:void(0);" data-id="${data.id?if_exists}" data-status="${data.hidden?if_exists?string("true","false")}"><#if data.hidden?if_exists?string("true","false")=="false"><#else>已隐藏</#if></div>
														</div>
													</div>
												</td>
			                                    <td>
			                                    	<div  style="float:left;" data-id="${data.id}" data-hide="${data.hidden?string('true','false')}">
														<img src="${imageUrl}/${data.url?if_exists?substring(0,data.url?last_index_of("."))}_1024_510${data.url?substring(data.url?last_index_of("."),data.url?length)}"  style="width:240px;" data-img="${imageUrl}/${data.url?if_exists?substring(0,data.url?last_index_of("."))}_1024_510${data.url?substring(data.url?last_index_of("."),data.url?length)}" title="${data.name?if_exists}" class="superbox-img viewBanner">
														<div style="margin-top: 4px;line-height: 24px; font-weight: bolder;">${data.name?if_exists}</div>
														<div class="imgif" style="margin-top: 4px;line-height: 24px;"><!-- 1600px × 558px 1600px × 550px --></div>
														<!-- <div style="margin-top: 4px;">
															<a style="float: left;" href="${basePath}/hotel/image-edit?hotelCode=${hotelCode}&id=${data.id?if_exists}">修改</a>
															<a style="float: right;" class="hidectl" href="javascript:void(0);" data-id="${data.id?if_exists}" data-status="${data.hidden?if_exists?string("true","false")}"><#if data.hidden?if_exists?string("true","false")=="false">隐藏<#else>已隐藏</#if></a>
														</div> -->
													</div>
			                                    </td>
			                                    <td>
			                                    	<div  style="float:left;" data-id="${data.id}" data-hide="${data.hidden?string('true','false')}">
														<img src='${imageUrl}/${data.url?if_exists?substring(0,data.url?last_index_of("."))}_330_230${data.url?substring(data.url?last_index_of("."),data.url?length)}'  style="width:240px;" data-img="${imageUrl}/${data.url?if_exists?substring(0,data.url?last_index_of("."))}_330_230${data.url?substring(data.url?last_index_of("."),data.url?length)}" title="${data.name?if_exists}" class="superbox-img viewBanner">
														<div style="margin-top: 4px;line-height: 24px; font-weight: bolder;">${data.name?if_exists}</div>
														<div class="imgif" style="margin-top: 4px;line-height: 24px;"><!-- 1600px × 558px 1600px × 550px --></div>
														<!-- <div style="margin-top: 4px;">
															<a style="float: left;" href="${basePath}/hotel/image-edit?hotelCode=${hotelCode}&id=${data.id?if_exists}">修改</a>
															<a style="float: right;" class="hidectl" href="javascript:void(0);" data-id="${data.id?if_exists}" data-status="${data.hidden?if_exists?string("true","false")}"><#if data.hidden?string("true","false")=="false">隐藏<#else>已隐藏</#if></a>
														</div> -->
													</div>
			                                    </td>
			                                    <td>
													<label>${data.width?if_exists}</label>
												</td>
												<td>
													<label>${data.height?if_exists}</label>
												</td>
			                                    <td style="text-align: center;">
			                                    	<a style="float: right;" href="${data.sourceUrl?if_exists}" target="_blank">原图链接</a> 
			                                        <input type="button" class="btn btn-info" value="酒店图片" onclick="window.location.href='${basePath}/hotel/photos?hotelCode=${data.hotelCode?if_exists}'" />
			                                    </td>
											</tr>
											</#list>
										</tbody>
									</table>
									<#include "/common/page.ftl" />
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {

	var $hids = $('#hids');
	var $hrows = $('input[type=checkbox][name=hid]');
	$hids.on('change', function() {
		if($hids.prop('checked')) {
			$hrows.prop('checked', true);
		} else {
			$hrows.prop('checked', false);
		}
	});
	
	/*$('img.viewBanner').each(function() {
		var $obj = $(this);
		var url = $obj.attr('data-img');
		var params = {};
		params.url = url;
		$.get('${basePath}/image/infox.json', params, function(data) {
			var width = data.width;
			var height = data.height;
			var size = data.size;
			$obj.parent().find('.imgif').text('大小：'+size+' 分辨率：'+width+'px × '+height+'px');
		}, 'json');
	});*/
	$('.superbox').sortable();
	$('.superbox-list .hidectl').on('click', function() {
		var $obj = $(this);
		var id = $obj.attr('data-id');
		var cstatus = $obj.attr('data-status');
		if(cstatus == '0') {
			$obj.attr('data-status', '1');
			$obj.text('已隐藏');
			/* var url = "/hotel/hiddenHotelImage/"+id+"/true";
			$.get(url,  function(data) {
				alert(data.desc);
				if(data.code == '1') {
					$obj.attr('data-status', '1');
					$obj.text('已隐藏');
				} else{
					alert("修改失败");
				}
			}); */
		} else {
			$obj.attr('data-status', '0');
			$obj.text('隐藏');
			/* var url = "/hotel/hiddenHotelImage/"+id+"/false";
			$.get(url,  function(data) {
				alert(data.desc);
				if(data.code == '1') {
					$obj.attr('data-status', '0');
					$obj.text('隐藏');
				} else{
					alert("修改失败");
				}
			}); */
		}
	});
	$('.showAllImgBtn').on('click', function() {
		if(confirm('确定要显示所有图片吗？')) {
			var url = '${basePath}/hotel/image/showall';
			var params = {};
			params.hotelCode = '${hotelCode}';
			$.post(url, params, function(data) {
				alert(data.desc);
				window.location.href = '${basePath}/hotel/photos?hotelCode=${hotelCode}';
			}, 'json');
		}
	});
	$('.saveOrderBtn').on('click', saveOrder);
	var $hids = $('#hids');
	var $hrows = $('input[type=checkbox][name=hid]');
	$hids.on('change', function() {
		if($hids.prop('checked')) {
			$hrows.prop('checked', true);
		} else {
			$hrows.prop('checked', false);
		}
	});
	$('#batchOnBtn').on('click', function() {
		var codes = getCheckHotelCode();
		if(codes == '') {
			alert('请选择酒店');
			return;
		}
		if(confirm('确定要上线吗？')) {
			var url = '${basePath}/hotel/online';
			var params = {};
			params.ids = codes;
			$.post(url, params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					toPage('1');
				}
			}, 'json');
		}
	});
	$('#batchOffBtn').on('click', function() {
		var codes = getCheckHotelCode();
		if(codes == '') {
			alert('请选择酒店');
			return;
		}
		if(confirm('确定要下线吗？')) {
			var url = '${basePath}/hotel/offline';
			var params = {};
			params.ids = codes;
			$.post(url, params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					toPage('1');
				}
			}, 'json');
		}
	});
	
	function getCheckHotelCode() {
		var codes = '';
		$('input[type=checkbox][name=hid]:checked').each(function() {
			var $obj = $(this);
			var code = $.trim($obj.val());
			if(code != '') {
				codes += code + ',';
			}
		});
		return codes;
	}
	function showView() {
		$('#hotelImageLayer .superbox-list[data-hide=0]:lt(3)').css({'border': '3px solid #16A085', 'border-radius': '3px'});
	}
	showView();
});

function saveOrder() {
	var datas = '';
	$('#hotelImageLayer .superbox-list').each(function(index) {
		var $obj = $(this);
		var hpid = $obj.attr('data-id');
		var ishide = $.trim($obj.find('.hidectl').attr('data-status'));
		if(ishide != '1') {
			ishide = '0';
		}
		datas += hpid+','+(index+1)+','+ishide+';';
	});
	$('#roomImageLayer .superbox-list').each(function(index) {
		var $obj = $(this);
		var hpid = $obj.attr('data-id');
		var ishide = $.trim($obj.find('.hidectl').attr('data-status'));
		if(ishide != '1') {
			ishide = '0';
		}
		datas += hpid+','+(index+1)+','+ishide+';';
	});
	if(datas == '') {
		alert('没有要排序的图片');
		return;
	}
	var url = '${basePath}/hotel/image/order/save';
	var params = {};
	params.datas = datas;
	$.post(url, params, function(data) {
		alert(data.desc);
		window.location.href = '${basePath}/hotel/photos?hotelCode=${hotelCode}';
	}, 'json');
}

function saveHidden(hidden){
	var rows = $('input[type=checkbox][name=hid]');
	var ids = "";
	for(var i=0;i<rows.length;i++){
		if(rows[i].checked){
			ids += ","+rows[i].value;
		}
	}
	console.log('ids: '+ids);
	var url = '${basePath}/hotel/image/updateHidden';
	var params = {};
	params.ids = ids;
	params.hidden = hidden;
	$.post(url, params, function(data) {
		alert(data.desc);
		$("#queryForm").submit();
	}, 'json');
	
}

function toPage(pageNo) {
	$('#start').val(pageNo);
	var temp = $('#queryForm');
	$('#queryForm').submit();
}

function exportImages() {
	//如果页面中没有用于下载iframe，增加iframe到页面中
	if ($('#downloadcsv').length <= 0) {
		$('body')
				.append(
						"<iframe id=\"downloadcsv\" style=\"display:none\"></iframe>");
	}
	var url = "${basePath}/hotel/Images.xls?chainCode="+$("#chainCode").val()+"&hotelCode="+$("#hotelCode").val()
			+"&imageType="+$("#imageType").val()+"&minWidth="+$("#minWidth").val()+"&noWidth="+$("#noWidth").val()
			+"&minHeight="+$("#minHeight").val()+"&maxWidth="+$("#maxWidth").val()
			+"&maxHeight="+$("#maxHeight").val()+"&minAspectRatio="+$("#minAspectRatio").val()
			+"&maxAspectRatio="+$("#maxAspectRatio").val()+"&exportType="+$("#exportType").val()+"&hidden="+$("#hidden").val();
	$('#downloadcsv').attr('src', '');
	$('#downloadcsv').attr('src', url);
}
</script>
</body>
</html>