<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="hotel-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>修改酒店landmark信息</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body" style="margin-left:-30px;">
								<form id="editForm" class="form-horizontal" method="post" action="">
									<fieldset >
										<input class="form-control" type="text" id="id" name="id" value="${landmark.id?if_exists}" style='display:none'/>
										<input class="form-control" type="text" id="chainCode" name="chainCode" value="${landmark.chainCode?if_exists}" style='display:none'/>
										<input class="form-control" type="text" id="hotelCode" name="hotelCode" value="${landmark.hotelCode?if_exists}" style='display:none'/>
										<div class="form-group">
											<label class="col-md-2 control-label" for="name" style="font-size: 16px;" check-type="required" required-message="名称不能为空。" >英文名</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="nameEn" value="${landmark.nameEn?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="nameEn" style="font-size: 16px;">中文名</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="name" value="${landmark.name?if_exists}" />
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="button" class="btn btn-primary" value="保存" id="updateLandmark" style="margin-right: 12px;" />
												<input type="button" class="btn btn-default" value="返回" onclick="window.history.go(-1)" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
var type ='hotel-cover';
$(document).ready(function() {
	$("#updateLandmark").click(function(){
			updateLandmark();
	}); 
});
 
function updateLandmark(){
	$.ajax({
			async : false,
			type : "POST",
			dataType:"json",
			data:{
				id:$("#id").val(),
				chainCode:$("#chainCode").val(),
				hotelCode:$("#hotelCode").val(),
				name:$("#name").val(),
				nameEn:$("#nameEn").val()
			},
			url :  "${basePath}/hotel/updateLandmark",
			error : function(data) {
				return "执行失败";
			},
			success : function(data) {
				if(data.code==1){
					alert(data.desc);
					window.location="${basePath}/hotel/info?hotelCode=${landmark.hotelCode?if_exists}";
					return false;
				}else{
					alert(data.desc);
					return false;
				}
			}
		});
}
</script>
</body>
</html>