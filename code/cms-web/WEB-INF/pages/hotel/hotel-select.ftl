<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}"></@head.head>
<body>
<div id="main" role="main" style="margin: 16px 0px 0px 8px;">
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row" style="margin: -12px -38px -72px -20px">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<div class="widget-body no-padding" style="border-top: 1px solid #ccc;">
							<div class="widget-body-toolbar">
								<form id="queryForm" class="form-inline" method="post" action="${basePath}/hotel/select">
									<input type="hidden" id="start" name="start" value="${page.currentPage}" />
									<input type="hidden" id="limit" name="limit" value="${limit}" />
									<input type="hidden" id="state" name="state" value="${state}" />
									<div class="form-group">
							       		<select style="width:300px;" name="city" id="city">
							       			<option value="">请选择国家和城市</option>
							       			<#list countries as datax>
							       				<#list datax.cities as data>
							       					<option value="${data.cityCode?if_exists}"<#if data.cityCode==city> selected="selected"</#if>>${datax.countryName?if_exists} - ${data.cityName?if_exists}</option>
							       				</#list>
							       			</#list>
							       		</select>
							       	</div>
									<div class="form-group" style="margin: 0px 15px 8px 0px;">
							        	<input type="text" class="form-control" name="hcode" value="${hcode?if_exists}" placeholder="酒店代码" />
							       	</div>
							       	<div class="form-group" style="margin: 0px 15px 8px 0px;">
							        	<input type="text" class="form-control" name="name" value="${name?if_exists}" placeholder="酒店中文名" />
							       	</div>
							       	<div class="form-group" style="margin: 0px 15px 8px 0px;">
							        	<input type="text" class="form-control" name="enname" value="${enname?if_exists}" placeholder="酒店英文名" />
							       	</div>
							       	<div class="form-group" style="margin: 0px 15px 8px 0px;">
							       		<input type="submit" class="btn btn-primary" value="查询" style="margin-right: 8px;" />
							       	</div>
								</form>
							</div>
							<table class="table table-striped table-bordered table-hover">
								<thead>
									<tr>
										<th>酒店名</th>
										<th>酒店英文名</th>
									    <th>城市</th>
									    <th>国家</th>
									    <th>操作</th>
									</tr>
								</thead>
								<tbody>
									<#list page.datas as data>
									<tr class="small">
										<td>${data.hotelName?if_exists}</td>
							            <td>${data.hotelNameEn?if_exists}</td>
							            <td>${data.cityName?if_exists}</td>
							            <td>${data.countryName?if_exists}</td>
							            <td style="text-align: center;">
							              <a class="btn btn-info selectBtn" href="javascript:void(0)" 
							              	data-hotelCode="${data.hotelCode?if_exists}" data-hotelName="${data.hotelName?if_exists}" data-hotelNameEn="${data.hotelNameEn?if_exists}"
							              	data-countryCode="${data.countryCode?if_exists}" data-countryName="${data.countryName?if_exists}" 
							              	data-cityCode="${data.cityCode?if_exists}" data-cityName="${data.cityName?if_exists}">选择</a>
							            </td>
									</tr>
									</#list>
								</tbody>
							</table>
							<#include "/common/page.ftl" />
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	
	//设置下拉框带搜索功能
	$("#city").select2();
	
	$('a.selectBtn').on('click', function() {
		var $obj = $(this);
		selectHotel($obj);
	});
	$('table tr.small').on('dblclick', function() {
		var $obj = $(this).find('a.selectBtn');
		selectHotel($obj);
	});
	function selectHotel($obj) {
		var hotelCode = $obj.attr('data-hotelCode');
		var hotelName = $obj.attr('data-hotelName');
		var hotelNameEn = $obj.attr('data-hotelNameEn');
        var countryCode = $obj.attr('data-countryCode');
        var countryName = $obj.attr('data-countryName');
        var cityCode = $obj.attr('data-cityCode');
        var cityName = $obj.attr('data-cityName');
        parent.selectHotelInfo(hotelCode, hotelName, hotelNameEn, countryCode, countryName, cityCode, cityName);
	}
});
function toPage(pageNo) {
	$('#start').val(pageNo);
	$('#queryForm').submit();
}
</script>