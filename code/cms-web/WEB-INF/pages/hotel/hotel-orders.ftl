<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>酒店排序</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="hotel-list-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>酒店列表</h2>
						</header>
						<div>
							<div class="widget-body no-padding">
								<div class="widget-body-toolbar">
									<form id="queryForm" class="form-inline" method="post" action="${basePath}/hotel/orders">
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<select class="form-control" name="city">
					                			<option value="">请选择国家和城市</option>
					                			<#list countries as datax>
					                				<#list datax.city as data>
					                					<option value="${data.cityCode?if_exists}"<#if data.cityCode==city> selected="selected"</#if>>${datax.countryName?if_exists} - ${data.cityName?if_exists}</option>
					                				</#list>
					                			</#list>
					                		</select>
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<input type="submit" class="btn btn-primary" value="查询" style="margin-right: 8px;" />
					                		<input id="saveOrderBtn" type="button" class="btn btn-info" value="保存排序" />
					                	</div>
									</form>
								</div>
								<table class="table table-striped table-bordered table-hover">
									<thead>
										<tr>
											<th>酒店中文名</th>
											<th>酒店英文名</th>
										    <th>城市中文名</th>
										    <th>城市英文名</th>
										    <th>国家中文名</th>
										    <th>国家英文名</th>
										    <th width="130">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list datas as data>
										<tr class="small">
											<td>${data.hotelName?if_exists}</td>
		                                    <td>${data.hotelNameEn?if_exists}</td>
		                                    <td>${data.cityName?if_exists}</td>
		                                    <td>${data.cityNameEn?if_exists}</td>
		                                    <td>${data.countryName?if_exists}</td>
		                                    <td>${data.countryNameEn?if_exists}</td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" data-id="${data.hotelCode?if_exists}" value="${data.rankCity?if_exists}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
	$('#saveOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			var value = orderIndex;
			items += id+':'+value+';';
			orderIndex ++;
		});
		if(items == '') {
			alert('没有要排序的酒店！');
			return;
		}
		var params = {};
		params.items = items;
		$.post('${basePath}/hotel/orders/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/hotel/orders?city=${city?if_exists}';
			}
		}, 'json');
	});
});
</script>
</body>
</html>