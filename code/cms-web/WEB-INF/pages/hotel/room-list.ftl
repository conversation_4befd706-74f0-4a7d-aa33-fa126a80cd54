<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>酒店房型列表</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="room-list-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>酒店房型列表</h2>
						</header>
						<div>
							<div class="widget-body no-padding">
								<div class="widget-body-toolbar">
									<form id="queryForm" class="form-inline" style="text-align: right;" method="post" action="${basePath}/hotel/roomlist">
										<input type="hidden" id="start" name="start" value="${page.currentPage}" />
										<input type="hidden" id="limit" name="limit" value="${limit}" />
										<input type="hidden" id="chainCode" name="chainCode" value="${chainCode}" />
										<input type="hidden" id="hotelCode" name="hotelCode" value="${hotelCode}" />
										<input type="button" class="btn btn-default" value="返回" onclick="window.history.go(-1)" />
									</form>
								</div>
								<table id="dt_basic" class="table table-striped table-bordered table-hover">
									<thead>
										<tr>
											<th width="70">房型Code</th>
											<th width="200">房型名</th>
											<#if chainCode!="LW">
										    <th width="200">booker_text</th>
											</#if>
										    <th width="200">房型英文名</th>
										    <th width="200">中文描述</th>
										    <th width="200">英文描述</th>
										    <th width="80">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list page.datas as data>
										<tr class="small">
											<td>${data.roomCode?if_exists}</td>
		                                    <td>${data.roomName?if_exists}</td>
		                                    <#if chainCode!="LW">
		                                    <td>${data.bookerText?if_exists}</td>
		                                    </#if>
		                                    <td>${data.roomNameEn?if_exists}</td>
		                                    <td>${data.bookerTextEn?if_exists}</td>
		                                    <td>${data.roomDescEn?if_exists}</td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning" href="${basePath}/hotel/room/edit?hotelCode=${data.hotelCode?if_exists}&roomCode=${data.roomCode?if_exists}&chainCode=${chainCode?if_exists}">修改</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<#include "/common/page.ftl" />
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	
});
function toPage(pageNo) {
	$('#start').val(pageNo);
	$('#queryForm').submit();
}
</script>
</body>
</html>