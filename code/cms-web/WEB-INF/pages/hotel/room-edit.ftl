<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>修改酒店房型</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="room-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>修改酒店房型</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body" style="margin-left:-30px;">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/about/save">
									<input type="hidden" id="chainCode" name="chainCode" value="${data.chainCode?if_exists}" readonly="readonly"/>
									<input type="hidden" id="hotelCode" name="hotelCode" value="${data.hotelCode?if_exists}" readonly="readonly"/>
									<input type="hidden" id="roomCode" name="roomCode" value="${data.roomCode?if_exists}" readonly="readonly"/>
									<fieldset>
										<div class="form-group">
											<label class="col-md-2 control-label" for="title" style="font-size: 16px;" check-type="required" required-message="名称不能为空。" >房型中文名</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="roomName" value="${data.roomName?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="title" style="font-size: 16px;">房型英文名</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="roomNameEn" readonly="readonly" value="${data.roomNameEn?if_exists}" />
											</div>
										</div>
										<!-- 
										<div class="form-group">
											<label class="col-md-2 control-label" for="banner" style="font-size: 16px;">房型默认图片</label>
											<input type="hidden" id="roomCoverUrl" name="roomCoverUrl" value="${data.roomCoverUrl?if_exists}"/>
											<div class="col-md-10">
												<input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${data.roomCoverUrl?if_exists}" value="查看" />
											</div>
										</div>
										-->
										<div class="form-group">
											<label class="col-md-2 control-label" for="cover" style="font-size: 16px;">房型中文描述</label>
											<div class="col-md-10">
												<textarea class="form-control" style="min-height: 120px;" id="bookerTextEn" name="bookerTextEn">${data.bookerTextEn?if_exists}</textarea>
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="cover" style="font-size: 16px;">房型英文描述</label>
											<div class="col-md-10">
												<textarea class="form-control" style="min-height: 120px;" id="roomDescEn" name="roomDescEn">${data.roomDescEn?if_exists}</textarea>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="button" class="btn btn-primary" value="保存" id="itemAdd" style="margin-right: 12px;"/>
												<input type="button" class="btn btn-default" value="返回" onclick="window.history.go(-1)" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>

<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
var type ='hotel-room-cover';
$(document).ready(function() {
	$("#itemAdd").click(function(){
		upHotel();
	});
	
	var $selectBannerModal = $('#selectBannerModal');
	var $itemBanner = $('#roomCoverUrl');
	var selectBanner = function() {
		var params = {};
		params.relateType = type;
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val(type);
		$selectBannerModal.find('input[name=relateCode]').val($('#itemCode').val());
		$selectBannerModal.find('input[name=format]').val('221*168c.jpg');
		$selectBannerModal.find('#imgsize').text('221 x 168');
		$selectBannerModal.find('.modal-title').text('添加封面');
		$selectBannerModal.modal();
	};
	$selectBannerModal.find('.superbox img').on('click', function() {
		var $obj = $(this);
		$itemBanner.val('${imageUrl}/'+$obj.attr('src'));
		$selectBannerModal.modal('hide');
	});
	
	$('#selectBannerBtn').on('click', selectBanner);
	$itemBanner.on('click', selectBanner);
});
function selectImage(url) {
	$('#roomCoverUrl').val(url);
	$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};
function upHotel(){
	$.ajax({
			async : false,
			type : "POST",
			dataType:"json",
			data:{
				roomCode:$("#roomCode").val(),
				chainCode:$("#chainCode").val(),
				hotelCode:$("#hotelCode").val(),
				roomName:$("#roomName").val(),
				roomNameEn:$("#roomNameEn").val(),
				bookerTextEn:$("#bookerTextEn").val(),
				roomDescEn:$("#roomDescEn").val(),
				roomCoverUrl:$("#roomCoverUrl").val()
			},
			url :  "${basePath}/hotel/room/up",
			error : function(data) {
				return "执行失败";
			},
			success : function(data) {
				if(data.code==1){
					alert(data.desc);
					window.location="${basePath}/hotel/roomlist?chainCode=${data.chainCode?if_exists}&hotelCode=${data.hotelCode?if_exists}";
					return false;
				}else{
					alert(data.desc);
					return false;
				}
			}
		});
}
</script>
</body>
</html>