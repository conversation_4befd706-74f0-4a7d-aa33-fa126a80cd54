<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>图片管理</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="images-list-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>图片管理</h2>
						</header>
						<div>
							<div class="widget-body no-padding">
								<div class="widget-body-toolbar">
									<form id="queryForm" class="form-inline" method="post" action="${basePath}/hotel/images">
										<input type="hidden" id="research" name="research" value="true" /> 
										<input type="hidden" id="start" name="start" value="${page.currentPage}" />
										<input type="hidden" id="limit" name="limit" value="${limit}" />
										<div class="form-group" style="margin: 0px 15px 8px 0px;">
								        	<input type="text" class="form-control" name="hcode" value="${hcode?if_exists}" placeholder="酒店代码" />
								       	</div>
										<div class="form-group" style="margin: 0px 15px 8px 0px;">
						                	<input type="text" class="form-control" name="name" value="${name?if_exists}" placeholder="酒店中文名" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
						                	<input type="text" class="form-control" name="enname" value="${enname?if_exists}" placeholder="酒店英文名" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<select class="form-control" name="city">
					                			<option value="">请选择国家和城市</option>
					                			<#list countries as datax>
					                				<#list datax.cities as data>
					                					<option value="${data.cityCode?if_exists}"<#if data.cityCode?if_exists == city> selected="selected"</#if>>${datax.countryName?if_exists} - ${data.cityName?if_exists}</option>
					                				</#list>
					                			</#list>
					                		</select>
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<input type="submit" class="btn btn-primary" value="查询" style="margin-right: 8px;" />
					                	</div>
									</form>
								</div>
								<table id="dt_basic" class="table table-striped table-bordered table-hover">
									<thead>
										<tr>
											<th>酒店中文名</th>
											<th>酒店英文名</th>
										    <th>城市中文名</th>
										    <th>城市英文名</th>
										    <th>国家中文名</th>
										    <#if chainCode=="LW">
										    <th>国家英文名</th>
										    </#if>
										    <th width="200">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list page.datas as data>
										<tr class="small">
											<td>${data.hotelName?if_exists}</td>
		                                    <td>${data.hotelNameEn?if_exists}</td>
		                                    <td>${data.cityName?if_exists}</td>
		                                    <td>${data.cityNameEn?if_exists}</td>
		                                    <td>${data.countryName?if_exists}</td>
		                                    <#if chainCode=="LW">
		                                    <td>${data.countryNameEn?if_exists}</td>
		                                    </#if>
		                                    <td style="text-align: center;">
		                                        <input type="button" class="btn btn-info" value="酒店图片" onclick="window.location.href='${basePath}/hotel/photos?hotelCode=${data.hotelCode?if_exists}'" />
		                                        <#if chainCode!="LW">
		                                        <input type="button" class="btn btn-info" value="房型图片" onclick="window.location.href='${basePath}/hotel/roomPhotos?hotelCode=${data.hotelCode?if_exists}'" />
		                                        </#if>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<#include "/common/page.ftl" />
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>

<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	var $hids = $('#hids');
	var $hrows = $('input[type=checkbox][name=hid]');
	$hids.on('change', function() {
		if($hids.prop('checked')) {
			$hrows.prop('checked', true);
		} else {
			$hrows.prop('checked', false);
		}
	});
	$('#batchOnBtn').on('click', function() {
		var codes = getCheckHotelCode();
		if(codes == '') {
			alert('请选择酒店');
			return;
		}
		if(confirm('确定要上线吗？')) {
			var url = '${basePath}/hotel/online';
			var params = {};
			params.ids = codes;
			$.post(url, params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					toPage('1');
				}
			}, 'json');
		}
	});
	$('#batchOffBtn').on('click', function() {
		var codes = getCheckHotelCode();
		if(codes == '') {
			alert('请选择酒店');
			return;
		}
		if(confirm('确定要下线吗？')) {
			var url = '${basePath}/hotel/offline';
			var params = {};
			params.ids = codes;
			$.post(url, params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					toPage('1');
				}
			}, 'json');
		}
	});
	
	function getCheckHotelCode() {
		var codes = '';
		$('input[type=checkbox][name=hid]:checked').each(function() {
			var $obj = $(this);
			var code = $.trim($obj.val());
			if(code != '') {
				codes += code + ',';
			}
		});
		return codes;
	}
});

function toPage(pageNo) {
	$('#start').val(pageNo);
	$('#queryForm').submit();
}

</script>
</body>
</html>