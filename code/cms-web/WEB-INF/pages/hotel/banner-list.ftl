<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>酒店图片管理</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="image-list-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>图片管理列表</h2>
						</header>
						<div>
							<div class="widget-body no-padding">
								<div class="widget-body-toolbar">
									
									<form id="bannerUploadForm" class="form-horizontal" method="post" action="${basePath}/hotel/upload" enctype="multipart/form-data" target="uploadbaframe" >
										<input type="hidden" name="hotelCode" value="${hotelCode}"/>
										<fieldset>
											<div class="form-group">
												<div class="col-md-5">
													<input class="form-control" type="file" id="file" name="file" />
												</div>
												<div class="col-md-1">
													<input type="submit" class="btn btn-success" value="上传" id="uploadba"/>
												</div>
												<div class="col-md-1">
													<input type="button" class="btn btn-success" value="删除" id="del"/>
												</div>
											</div>
										</fieldset>
									</form>
								</div>
								<form id="queryForm" class="form-inline" method="post" action="${basePath}/hotel/banner/list">
										<input type="hidden" id="start" name="start" value="${page.currentPage}" />
										<input type="hidden" id="limit" name="limit" value="${limit}" />
										<input type="hidden" id="hotelCode" name="hotelCode" value="${hotelCode}" />
								</form>
								<div class="superbox col-sm-12" style="margin-top: 12px; margin-bottom: 12px;">
									<#list page.datas as data>
										<div class="superbox-list" style="margin-left:25px;">
											<input type="checkbox"  name="ids" style="position:absolute;margin-left:-16px;;" value="${data.id}"/>
											<img  src="${imageUrl}/${data.bannerUrl}" data-img="${imageUrl}/${data.bannerUrl}" alt=""  class="superbox-img viewBanner" del="img${data.id}"/>
											<img src="${basePath}/static/images/common/delete.png" class="delimg" style="display:none;position:absolute;border:0;padding:0;margin:-40px 0px 0px 90px;" id="img${data.id}"></img>
										</div>
									</#list>
									<div class="superbox-float"></div>
								</div>
								<#include "/common/page.ftl" />
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<iframe id="uploadbaframe" name="uploadbaframe" style="display: none;"></iframe>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script>
var type = "hotel-banner";
$(document).ready(function(){
	$("#del").click(del);
});
function del(){
	var ids =""; 
	$('input[name="ids"]:checked').each(function(){ 
	ids = ids +$(this).val()+","; });
	if(ids==""){
		alert("请先选中要删除图片");
		return;
	}
	if(!confirm("要删除吗？")){
		return false;
	}
	$.ajax({
			async : false,
			type : "POST",
			dataType:"json",
			data:{
				ids:ids,
				hotelCode:"${hotelCode}"
			},
			url :  "${basePath}/hotel/banner/del",
			error : function(data) {
				return "执行失败";
			},
			success : function(data) {
				if(data.code==1){
					alert(data.desc);
					window.location="${basePath}/hotel/banner/list?hotelCode=${hotelCode?if_exists}";
					return false;
				}else{
					alert(data.desc);
					return false;
				}
			}
		});
}

function toPage(pageNo) {
	$('#start').val(pageNo);
	$('#queryForm').submit();
}
</script>
</body>
</html>