<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}"> </@head.head>
<body>
	<#include "/common/header.ftl" /> <#include "/common/menu.ftl" />
	<div id="main" role="main">
		<div id="ribbon">
			<ol class="breadcrumb">
				<li>酒店管理</li>
			</ol>
		</div>
		<div id="content">
			<section id="widget-grid" class="">
				<div class="row">
					<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
						<div class="jarviswidget jarviswidget-color-darken"
							id="hotel-list-1" data-widget-editbutton="false"
							data-widget-sortable="false" data-widget-colorbutton="false"
							data-widget-fullscreenbutton="false"
							data-widget-deletebutton="false">
							<header>
								<span class="widget-icon"> <i class="fa fa-table"></i>
								</span>
								<h2>酒店管理列表</h2>
							</header>
							<div>
								<div class="widget-body no-padding">
									<div class="widget-body-toolbar">
										<form id="queryForm" class="form-inline" method="post"
											action="${basePath}/hotel/list">
											<input type="hidden" id="research" name="research"
												value="true" /> 
											<input type="hidden"
												id="limit" name="limit" value="${limit}" />
											<input type="hidden" id="start" name="start"
												value="${page.currentPage}" /> 
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<input type="text" class="form-control" name="hcode"
													value="${hcode?if_exists}" placeholder="酒店代码" />
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<input type="text" class="form-control" name="name"
													value="${name?if_exists}" placeholder="酒店中文名" />
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<input type="text" class="form-control" name="enname"
													value="${enname?if_exists}" placeholder="酒店英文名" />
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<select class="form-control" name="cityCode">
													<option value="">请选择国家和城市</option> 
													<#list countries as country> 
													<#list country.cities as city>
													<option value="${city.cityCode?if_exists}"<#if
														city.cityCode==cityCode?if_exists>
														selected="selected"</#if>>${country.countryName?if_exists} -
														${city.cityName?if_exists}</option> 
													</#list> 
													</#list>
												</select>
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<select class="form-control" name="onlineStatus">
													<option value="">请选择状态</option>
													<option value="online""<#if onlineStatus?if_exists=="online">
														selected="selected"</#if>>已上线</option>
													<option value="offline""<#if onlineStatus?if_exists=="offline">
														selected="selected"</#if>>已下线</option>
													<option value="new""<#if onlineStatus?if_exists=="new">
														selected="selected"</#if>>新增</option>
												</select>
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<input type="button" class="btn btn-primary" value="查询"
													style="margin-right: 8px;" onclick="toPage(1)"/>
												<a id="batchOnBtn" class="btn btn-success"
													style="margin-right: 8px;">批量上线</a> 
												<a id="batchOffBtn" class="btn btn-danger"
													style="margin-right: 8px;">批量下线</a> 
												<a class="btn btn-success"
													href="javascript:exportHotelExcel()"
													style="margin-right: 8px;">导出Excel</a>
												<!-- <a class="btn btn-success" href="javascript:exportRoomExcel()" style="margin-right: 8px;">导出房型</a> -->
												<a class="btn btn-success"
													href="javascript:showUpdateTcsHotelListDiv()"
													style="margin-right: 8px;"><#if chainCode=="PF"||chainCode=="LX">同步Sabre酒店<#else>同步Trust酒店</#if></a>
											</div>
										</form>
									</div>
									<table id="dt_basic"
										class="table table-striped table-bordered table-hover">
										<thead>
											<tr>
												<th><input type="checkbox" id="hids" /></th>
												<th>酒店中文名</th>
												<th>酒店英文名</th>
												<th>城市中文名</th>
												<th>城市英文名</th>
												<th>国家中文名</th>
												<th width="140">国家英文名</th>
												<th width="100">接收银联</th>
												<th width="54">状态</th>
												<th width="130">操作</th>
											</tr>
										</thead>
										<tbody>
											<#list page.datas as data>
											<tr class="small">
												<td><input type="checkbox" name="hid"
													value="${data.hotelCode?if_exists}" /></td>
												<td>${data.hotelName?if_exists}</td>
												<td>${data.hotelNameEn?if_exists}</td>
												<td>${data.cityName?if_exists}</td>
												<td>${data.cityNameEn?if_exists}</td>
												<td>${data.countryName?if_exists}</td>
												<td>${data.countryNameEn?if_exists}</td>
												<td><#if data.isUpPay?if_exists==1><span
													style="color: green;">接收</span><#else><span
													style="color: red;">不接收</span></#if>
												</td>
												<td><#if data.onlineStatus?if_exists=="online"><span
													style="color: green;">上线</span></#if><#if
													data.onlineStatus?if_exists=="offline"><span
													style="color: red;">下线</span></#if><#if
													data.onlineStatus?if_exists=="new"><span style="color: red;">新增</span></#if>
												</td>
												<td style="text-align: center;">
													<!-- <input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${data.coverUrl?if_exists}" value="查看封面"> -->
													<a class="btn btn-warning"
													href="${basePath}/hotel/info?hotelCode=${data.hotelCode?if_exists}">修改</a>
													<!-- <a  class="btn btn-success" href="${basePath}/hotel/banner/list?hotelCode=${data.hotelCode?if_exists}" >Banner</a> -->
													<a class="btn btn-primary"
													href="${basePath}/hotel/roomlist?chainCode=${data.chainCode?if_exists}&hotelCode=${data.hotelCode?if_exists}">房型</a>
												</td>
											</tr>
											</#list>
										</tbody>
									</table>
									<#include "/common/page.ftl" />
								</div>
							</div>
							
							<div class="widget-body">
								<div class="alert-heading" style="margin-bottom: 4px;">服务器日志：</div>
								<textarea id="serverLog" rows="10" cols="120"></textarea>
							</div>
						</div>
					</article>
				</div>
			</section>
		</div>
	</div>
	
	<!-- 同步trust选项 -->
	<div id="updateTcsHotelListDiv" class="modal fade top1" tabindex="-1" role="dialog"
		aria-labelledby="addDivLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title">同步选项</h4>
				</div>
				<div class="modal-body pre-scrollable">
					<div>
						<input type="checkbox" name="updateOption" value="downloadHotelXml" checked> 下载酒店xml文件
					</div>
					<div>
						<input type="checkbox" name="updateOption" value="importHotelXml" checked> 更新酒店和房型基本信息
					</div>
					<#if chainCode!="PF">
						<div>
							<input type="checkbox" name="updateOption" value="importLandMarkAndTrsansport" checked> 更新交通和地标基本信息
						</div>
					</#if>
					<div>
						<input type="checkbox" name="updateOption" value="syncCountryFromHotel" checked> 更新国家和城市基本信息
					</div>
					<div>
						<input type="checkbox" name="updateOption" value="importRoomPicture" checked> 导入房型图片
					</div>
					<#if chainCode!="PF">
						<div>
							<input type="checkbox" name="updateOption" value="downloadFtpHotelImage" checked> 下载酒店图片ftp文件
						</div>
						<#if chainCode!="LX">
						<div>
							<input type="checkbox" name="updateOption" value="importHotelPicture" checked> 导入酒店图片
						</div>
						</#if>
					</#if>
					<div class="modal-footer">
						<button type="button" class="btn btn-default" data-dismiss="modal">关闭
						</button>
						<button type="button" class="btn btn-primary" onclick="updateTcsHotelList('${chainCode}')">提交
						</button>
					</div>
				</div><!-- /.modal-content -->
			</div><!-- /.modal -->
		</div>
	</div>
	
	<@foot.foot basePath="${basePath}"></@foot.foot>
	<script src="${basePath}/static/js/sockjs.0.3.4.min.js"></script>
	<script src="${basePath}/static/js/websocket.js"></script>
	<script type="text/javascript">
		$(document).ready(function() {
			var $hids = $('#hids');
			var $hrows = $('input[type=checkbox][name=hid]');
			$hids.on('change', function() {
				if ($hids.prop('checked')) {
					$hrows.prop('checked', true);
				} else {
					$hrows.prop('checked', false);
				}
			});
			$('#batchOnBtn').on('click', function() {
				var codes = getCheckHotelCode();
				if (codes == '') {
					alert('请选择酒店');
					return;
				}
				if (confirm('确定要上线吗？')) {
					var url = '${basePath}/hotel/online';
					var params = {};
					params.ids = codes;
					$.post(url, params, function(data) {
						alert(data.desc);
						if (data.code == '100') {
							window.location.href = '${basePath}/login';
						} else if (data.code == '1') {
							toPage('1');
						}
					}, 'json');
				}
			});
			$('#batchOffBtn').on('click', function() {
				var codes = getCheckHotelCode();
				if (codes == '') {
					alert('请选择酒店');
					return;
				}
				if (confirm('确定要下线吗？')) {
					var url = '${basePath}/hotel/offline';
					var params = {};
					params.ids = codes;
					$.post(url, params, function(data) {
						alert(data.desc);
						if (data.code == '100') {
							window.location.href = '${basePath}/login';
						} else if (data.code == '1') {
							toPage('1');
						}
					}, 'json');
				}
			});

			function getCheckHotelCode() {
				var codes = '';
				$('input[type=checkbox][name=hid]:checked').each(function() {
					var $obj = $(this);
					var code = $.trim($obj.val());
					if (code != '') {
						codes += code + ',';
					}
				});
				return codes;
			}
			
			websocketConnect();
		});
		
		function afterWebSocketOpen(){
			var message = {event:"updateTcsHotelList",data:{roomId:"updateTcsHotelList"}}
			ws.send(JSON.stringify(message));
		}
		
		function toPage(pageNo) {
			$('#start').val(pageNo);
			$('#queryForm').submit();
		}

		function exportHotelExcel() {
			//如果页面中没有用于下载iframe，增加iframe到页面中
			if ($('#downloadcsv').length <= 0) {
				$('body')
						.append(
								"<iframe id=\"downloadcsv\" style=\"display:none\"></iframe>");
			}
			var url = "${basePath}/hotel/Hotel.xls";
			$('#downloadcsv').attr('src', '');
			$('#downloadcsv').attr('src', url);
		}

		function exportRoomExcel() {
			//如果页面中没有用于下载iframe，增加iframe到页面中
			if ($('#downloadcsv').length <= 0) {
				$('body')
						.append(
								"<iframe id=\"downloadcsv\" style=\"display:none\"></iframe>");
			}
			var url = "${basePath}/hotel/Room.xls";
			$('#downloadcsv').attr('src', '');
			$('#downloadcsv').attr('src', url);
		}

		function showUpdateTcsHotelListDiv(){
			$('#updateTcsHotelListDiv').modal('show');
		}
		
		function updateTcsHotelList(chainCode) {
			var crsApi = '';
			if(chainCode=='PF'||chainCode=='LX'){
				crsApi = 'sabre';
			}else{
				crsApi = 'trust';
			}
			var updateOptions = '';
			$('input[name="updateOption"]:checked').each(function(){
				updateOptions += ','+($(this).val());
				});
			$.ajax({
				async : true,
				url : '${basePath}/hotel/updateTcsHotelList?updateOptions='+updateOptions,
				type : 'GET',
				data : {
					chainCode : chainCode,
					crsApi :  crsApi,
				},
				dataType : "text",
				success : function(data) {
					console.log("updateTcsHotelList: " + data);
					alert("请求已提交！");$('#updateTcsHotelListDiv').modal('hide');
				},
				error : function(data) {
					console.log("updateTcsHotelList: " + data);
				}
			});
		}
	</script>
</body>
</html>