<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}"> </@head.head>
<body>
	<#include "/common/header.ftl" /> <#include "/common/menu.ftl" />
	<div id="main" role="main">
		<div id="ribbon">
			<ol class="breadcrumb">
				<li>操作日志</li>
			</ol>
		</div>
		<div id="content">
			<section id="widget-grid" class="">
				<div class="row">
					<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
						<div class="jarviswidget jarviswidget-color-darken"
							id="log-list-1" data-widget-editbutton="false"
							data-widget-sortable="false" data-widget-colorbutton="false"
							data-widget-fullscreenbutton="false"
							data-widget-deletebutton="false">
							<header>
								<span class="widget-icon"> <i class="fa fa-table"></i>
								</span>
								<h2>操作日志列表</h2>
							</header>
							<div>
								<div class="widget-body no-padding">
									<div class="widget-body-toolbar">
										<form id="queryForm" class="form-inline" method="post"
											action="${basePath}/log/list">
											<input type="hidden" id="start" name="start"
												value="${page.currentPage}" /> <input type="hidden"
												id="limit" name="limit" value="${limit}" />
											<div class="form-group">
												<div class="col-md-10">
													<select class="col-xs-14 col-sm-14" id="level" name="level">
														<option value="">请选择类型...</option>
														<option value="error"<#if
															level=="error">selected="selected"</#if>>error</option>
														<option value="info"<#if
															level=="info">selected="selected"</#if>>info</option>
													</select>
												</div>
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<label class="control-label" for="titile">操作名称：</label> <input
													type="text" class="form-control" id="logTitle"
													name="logTitle" value="${logTitle?if_exists}" />
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<label class="control-label" for="operater">操作人：</label> <input
													type="text" class="form-control" id="operater"
													name="operater" value="${operater?if_exists}" />
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<label class="control-label" for="operater">操作内容：</label> <input
													type="text" class="form-control" id="describer"
													name="describer" value="${describer?if_exists}" />
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<label class="control-label" for="minDate">操作时间：</label>
												<input class="form-control" type="text" style="width: 120px"
													class="form-control" id="startTime" name="startTime"
													data-date-format="mm/dd/yyyy" readonly="readonly"
													value="${startTime?if_exists?string("MM/dd/yyyy")}" placeholder="起始时间" /> 
												<span>-</span> 
												<input class="form-control" type="text" style="width: 120px"
													class="form-control" id="endTime" name="endTime"
													data-date-format="mm/dd/yyyy" readonly="readonly"
													value="${endTime?string("MM/dd/yyyy")}" placeholder="结束时间" />
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<label class="control-label" for="items">所属栏目：</label> <select
													class="form-control" id="items" name="item"
													placeholder="栏目">
													<option value="">全部</option> <#list items as item>
													<option value="${item.code}">${item.iname}</option>
													</#list>
												</select>
											</div>
											<div class="form-group" style="margin: 0px 15px 8px 0px;">
												<input type="submit" class="btn btn-primary" value="查询"
													style="margin-right: 8px;" />
											</div>
										</form>
									</div>
									<table id="dt_basic"
										class="table table-striped table-bordered table-hover">
										<thead>
											<tr>
												<th width="140">操作时间</th>
												<th>类型</th>
												<th>操作名称</th>
												<th>操作栏目</th>
												<th>操作人</th>
												<th>操作内容</th>
												<th width="100">操作</th>
											</tr>
										</thead>
										<tbody>
											<#list page.datas as data>
											<tr class="small">
												<td style="text-align: center;">${data.operateDate?string("yyyy-MM-dd HH:mm:ss.SSS")}</td>
												<td>${data.level}</td>
												<td>${data.logTitle}</td>
												<td>${data.itemCode}</td>
												<td>${data.operater}</td>
												<td><pre style="height:100px;">
														<xmp>${data.describer}</xmp>
													</pre>
													</div></td>
												<td style="text-align: center;"><a
													class="btn btn-success" href="#">查看</a></td>
											</tr>
											</#list>
										</tbody>
									</table>
									<#include "/common/page.ftl" />
								</div>
							</div>
						</div>
					</article>
				</div>
			</section>
		</div>
	</div>
	<div class="modal fade" id="logmodal" tabindex="-1" role="dialog"
		aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content" style="width: 480px;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title">日志详情</h4>
				</div>
				<div class="modal-body" style="padding-bottom: 8px;">
					<form id="" class="form-horizontal" method="post" action="">
						<fieldset>
							<div class="form-group">
								<label class="col-md-3 control-label" for="oldpasswd"
									style="font-size: 16px;">名称</label>
								<div class="col-md-9">
									<input class="form-control" type="text" id="" name=""
										readonly="readonly" />
								</div>
							</div>
							<div class="form-group">
								<label class="col-md-3 control-label" for="newpasswd"
									style="font-size: 16px;">描述</label>
								<div class="col-md-9">
									<textarea rows="3" class="form-control" cols="20"
										readonly="readonly"></textarea>
								</div>
							</div>
							<div class="form-group">
								<label class="col-md-3 control-label" for="confirmpasswd"
									style="font-size: 16px;">操作人</label>
								<div class="col-md-9">
									<input class="form-control" type="text" id="" name=""
										readonly="readonly" />
								</div>
							</div>
							<div class="form-group">
								<label class="col-md-3 control-label" for="confirmpasswd"
									style="font-size: 16px;">操作时间</label>
								<div class="col-md-9">
									<input class="form-control" type="text" id="" name=""
										readonly="readonly" />
								</div>
							</div>
						</fieldset>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				</div>
			</div>
		</div>
	</div>
	<@foot.foot basePath="${basePath}"></@foot.foot>
	<script>
		$(document).ready(
				function() {
					$('#startTime').datepicker();
					$('#endTime').datepicker();
					$('#dt_basic a').click(
							function() {
								var parent = $(this).parent().parent();
								$('#logmodal input:eq(0)').val(
										parent.children('td:eq(0)').text());
								$('#logmodal textarea:eq(0)').val(
										parent.children('td:eq(4)').text());
								$('#logmodal input:eq(1)').val(
										parent.children('td:eq(2)').text());
								$('#logmodal input:eq(2)').val(
										parent.children('td:eq(3)').text());
								$('#logmodal').modal('show');
								return false;
							});
					itemVal();
				});
		function itemVal() {
			var c = "${itemVal}";
			if (c && c != "") {
				$("#items").val(c);
			}
		}
		function toPage(pageNo) {
			$('#start').val(pageNo);
			$('#queryForm').submit();
		}
	</script>
</body>
</html>