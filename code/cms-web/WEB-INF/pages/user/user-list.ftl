<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>用户管理</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="user-list-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>用户列表</h2>
						</header>
						<div>
							<div class="widget-body no-padding">
								<div class="widget-body-toolbar">
									<form id="queryForm" class="form-inline" method="post" action="${basePath}/user/list">
										<input type="hidden" id="start" name="start" value="${page.currentPage}" />
										<input type="hidden" id="limit" name="limit" value="${limit}" />
										<div class="form-group" style="margin: 0px 15px 8px 0px;">
											<label class="control-label" for="keyword">用户名：</label>
						                	<input type="text" class="form-control" id="username" name="username" value="${username?if_exists}" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
											<label class="control-label" for="keyword">昵称：</label>
						                	<input type="text" class="form-control" id="name" name="name" value="${name?if_exists}" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<input type="submit" class="btn btn-primary" value="查询" style="margin-right: 12px;" />
					                		<a id="addUserBtn" class="btn btn-success" href="javascript:void(0);">添加</a>
					                	</div>
									</form>
								</div>
								<table id="dt_basic" class="table table-striped table-bordered table-hover">
									<thead>
										<tr>
											<th>用户名</th>
											<th>昵称</th>
											<th>类型</th>
										    <th width="180">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list page.datas as data>
										<tr class="small">
		                                    <td>${data.username}</td>
		                                    <td>${data.name}</td>
		                                    <td style="text-align: center;">
		                                    	<#if data.type=="1">管理员<#else>编辑</#if>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning updateUserBtn" href="javascript:void(0);" data-id="${data.id}" data-username="${data.username}"
		                                    	data-name="${data.name}" data-type="${data.type}" data-roles="${data.roles}" style="margin-right: 12px;">修改</a>
		                                        <a class="btn btn-danger deleteUserBtn" href="javascript:void(0);" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<#include "/common/page.ftl" />
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<div class="modal fade" id="editUserModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content" style="width: 580px;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body" style="padding-bottom: 8px;">
				<form class="form-horizontal" method="post" action="${basePath}/user/save">
					<input type="hidden" name="id" id="id" />
					<input type="hidden" name="roles" id="roles" />
					<fieldset>
						<div class="form-group">
							<label class="col-md-3 control-label" for="username" style="font-size: 16px;">用户名</label>
							<div class="col-md-9">
								<input class="form-control" type="text" id="username" name="username" />
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-3 control-label" for="password" style="font-size: 16px;">密码</label>
							<div class="col-md-9">
								<input class="form-control" type="password" id="password" name="password" />
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-3 control-label" for="name" style="font-size: 16px;">昵称</label>
							<div class="col-md-9">
								<input class="form-control" type="text" id="name" name="name" />
							</div>
						</div>
						<div class="form-group">
							<input type="hidden" id="type" name="type" />
							<label class="col-md-3 control-label" for="type" style="font-size: 16px;">类型</label>
							<div class="col-md-9"><input class="form-control" type="text" id="typename" name="typename" value="编辑" readonly="readonly" /></div>
						</div>
						<div class="form-group rolelayer">
							<label class="col-md-3 control-label" style="font-size: 16px;">权限</label>
							<div class="col-md-9">
								<table>
									<tr>
									<#list menus as menu>
										<td width="100">
											<label class="checkbox-inline">
												<input type="checkbox" value="${menu.itemCode}" />
												<span>${menu.title?if_exists}</span>
											</label>
										</td>
										<#if (menu_index+1)%3==0>
										</tr>
										</#if>
										<#if (menu_index+1)%3==0 && (menu_index+1) lt menus?size>
										<tr>
										</#if>
									</#list>
									<tr>
										<td>
											<label class="checkbox-inline">
												<input type="checkbox" value="active" />
												<span>促销活动</span>
											</label>
										</td>
										<td>
											<label class="checkbox-inline">
												<input type="checkbox" value="hotel" />
												<span>酒店管理</span>
											</label>
										</td>
										<td>
											<label class="checkbox-inline">
												<input type="checkbox" value="horder" />
												<span>酒店排序</span>
											</label>
										</td>
									</tr>
									<tr>
										<td>
											<label class="checkbox-inline">
												<input type="checkbox" value="image" />
												<span>图片管理</span>
											</label>
										</td>
										<td>
											<label class="checkbox-inline">
												<input type="checkbox" value="image-search" />
												<span>图片查询</span>
											</label>
										</td>
										<td>
											<label class="checkbox-inline">
												<input type="checkbox" value="log" />
												<span>操作日志</span>
											</label>
										</td>
									</tr>
									<tr>
										<td>
											<label class="checkbox-inline">
												<input type="checkbox" value="reservation-search" />
												<span>订单查询</span>
											</label>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-12">
								<div class="alert alert-warning">
									<i class="fa-fw fa fa-warning"></i><strong>提示：</strong>
									<span>密码由6-20个字符组成，必须包含字母、数字及特殊符号（“_”、“$”、“#”、“@”符号之一）！</span>
								</div>
							</div>
						</div>
					</fieldset>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				<button type="button" id="saveBtn" class="btn btn-primary">保存</button>
			</div>
		</div>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	var $editUserModal = $('#editUserModal');
	$('#addUserBtn').on('click', function() {
		$editUserModal.find('.modal-title').text('添加用户');
		$editUserModal.find('#id').val('');
		$editUserModal.find('#username').val('');
		$editUserModal.find('#password').val('');
		$editUserModal.find('#name').val('');
		$editUserModal.find('#type').val('0');
		$editUserModal.find('#typename').val('编辑');
		$editUserModal.find('.rolelayer').show();
		$editUserModal.find('input[type=checkbox]').prop('checked', false);
		$('#editUserModal').modal({backdrop: 'static', keyboard: false});
	});
	$editUserModal.find('#saveBtn').on('click', function() {
		var roles = '';
		$editUserModal.find('input[type=checkbox]:checked').each(function() {
			var $obj = $(this);
			roles += $obj.val()+',';
		});
		$editUserModal.find('#roles').val(roles);
		var $form = $editUserModal.find('form');
		var url = $form.attr('action');
		var params = $form.serializeArray();
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				toPage('1');
			}
		}, 'json');
	});
	$('.updateUserBtn').on('click', function() {
		var $obj = $(this);
		$editUserModal.find('.modal-title').text('修改用户');
		$editUserModal.find('#id').val($obj.attr('data-id'));
		$editUserModal.find('#username').val($obj.attr('data-username'));
		$editUserModal.find('#password').val('');
		$editUserModal.find('#name').val($obj.attr('data-name'));
		var dtatype = $.trim($obj.attr('data-type'));
		$editUserModal.find('#type').val(dtatype);
		if(dtatype == '1') {
			$editUserModal.find('#typename').val('管理员');
			$editUserModal.find('.rolelayer').hide();
		} else {
			$editUserModal.find('#typename').val('编辑');
			var roles = $obj.attr('data-roles');
			$editUserModal.find('input[type=checkbox]').each(function() {
				var $obj = $(this);
				if(roles.indexOf($obj.val())>=0) {
					$obj.prop('checked', true);
				}
			});
			$editUserModal.find('.rolelayer').show();
		}
		$('#editUserModal').modal({backdrop: 'static', keyboard: false});
	});
	$('.deleteUserBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/user/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					toPage('1');
				}
			}, 'json');
		}
	});
});
function toPage(pageNo) {
	$('#start').val(pageNo);
	$('#queryForm').submit();
}
</script>
</body>
</html>