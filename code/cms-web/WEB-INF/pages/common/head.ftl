<#macro head title="BSI" basePath="">
    <head>
        <meta charset="utf-8">
        <!--<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">-->

        <title>${title}</title>
        <meta name="description" content="">
        <meta name="author" content="">

        <!-- Use the correct meta names below for your web application
             Ref: http://davidbcalhoun.com/2010/viewport-metatag 
             
        <meta name="HandheldFriendly" content="True">
        <meta name="MobileOptimized" content="320">-->
        
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

        <!-- Basic Styles -->
        <link rel="stylesheet" type="text/css" media="screen" href="${basePath}/static/css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" media="screen" href="${basePath}/static/css/font-awesome.min.css">

        <!-- SmartAdmin Styles : Please note (smartadmin-production.css) was created using LESS variables -->
        <link rel="stylesheet" type="text/css" media="screen" href="${basePath}/static/css/smartadmin-production.css">
        <link rel="stylesheet" type="text/css" media="screen" href="${basePath}/static/css/smartadmin-skins.css">
        
		<link rel="stylesheet" type="text/css" media="screen" href="${basePath}/static/css/bootstrap-datepicker.css">

        <!-- SmartAdmin RTL Support is under construction
        <link rel="stylesheet" type="text/css" media="screen" href="static/css/smartadmin-rtl.css"> -->

        <!-- We recommend you use "your_style.css" to override SmartAdmin
             specific styles this will also ensure you retrain your customization with each SmartAdmin update.
        <link rel="stylesheet" type="text/css" media="screen" href="static/css/your_style.css"> -->

        <!-- Demo purpose only: goes with demo.js, you can delete this css when designing your own WebApp -->
        <link rel="stylesheet" type="text/css" media="screen" href="${basePath}/static/css/demo.css">
        <link rel="stylesheet" type="text/css" media="screen" href="${basePath}/static/css/bootstrap-datetimepicker.css">

		<link rel="stylesheet" type="text/css" media="screen" href="${basePath}/static/select2/select2.min.css">

        <!-- FAVICONS -->
        <link rel="shortcut icon" href="${basePath}/static/images/favicon/favicon.ico" type="image/x-icon">
        <link rel="icon" href="${basePath}/static/images/favicon/favicon.ico" type="image/x-icon">
        <style type="text/css">
			label.error
			{
				color: #F00;
			}
		</style>
        <#nested>
    </head>

</#macro>