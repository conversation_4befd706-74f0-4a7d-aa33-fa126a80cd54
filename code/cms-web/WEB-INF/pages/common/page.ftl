<div class="dt-row dt-bottom-row">
	<div class="row">
		<div class="col-sm-6">
			<div class="dataTables_paginate paging_bootstrap_full">
				<ul class="pagination">
					<#if page.currentPage lte 1>
						<li class="first disabled"><a >首页</a></li>
						<li class="prev disabled"><a >上一页</a></li>
					<#else>
						<li class="first"><a href="javascript:toPage('1');">首页</a></li>
						<li class="prev"><a href="javascript:toPage('${page.prePage}');">上一页</a></li>
					</#if>
					
					<#if page.currentPage gte page.totalPage>
						<li class="next disabled"><a >下一页</a></li>
						<li class="last disabled"><a >末页</a></li>
					<#else>
						<li class="next"><a href="javascript:toPage('${page.nextPage}');">下一页</a></li>
						<li class="last"><a href="javascript:toPage('${page.totalPage}');">末页</a></li>
					</#if>
				</ul>
			</div>
		</div>
		<div class="col-sm-6 text-right">
			<div class="dataTables_info" id="dt_basic_info" style="float: right;">
				<span class="smart-form">
					<label class="select" style="width:60px">
						<select id="pageSelect" name="dt_basic_length" aria-controls="dt_basic">
							<#list 1..page.totalPage as idx>
								<#if idx==page.currentPage]>
									<option value="${idx}" selected="selected">${idx}</option>
								<#else>
									<option value="${idx}">${idx}</option>
								</#if>
							</#list>
						</select>
						<i></i>
					</label>
				</span>
			</div>
		</div>
	</div>
</div>