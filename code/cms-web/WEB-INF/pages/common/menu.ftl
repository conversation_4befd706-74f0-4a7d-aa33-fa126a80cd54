<aside id="left-panel">
	<nav>
		<ul>
			<#if chainCode=="LW">
			<li<#if itemCode=="index"> class="active"</#if>>
				<a href="${basePath}" title="HOME">
					<i class="fa fa-lg fa-fw fa-home"></i>
					<span class="menu-item-parent">HOME</span>
				</a>
			</li>
			<#if suser.type=='1' || suser.roles?index_of('user') gte 0>
			<li<#if itemCode=="user"> class="active"</#if>>
				<a href="${basePath}/user/list">
					<i class="fa fa-lg fa-fw fa-user"></i>
					<span class="menu-item-parent">用户管理</span>
				</a>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('home') gte 0 || suser.roles?index_of('oversea') gte 0
				 || suser.roles?index_of('local') gte 0 || suser.roles?index_of('travel') gte 0 || suser.roles?index_of('brand') gte 0
				 || suser.roles?index_of('history') gte 0 || suser.roles?index_of('foot') gte 0 || suser.roles?index_of('about') gte 0
				 || suser.roles?index_of('roadmap') gte 0 || suser.roles?index_of('privacy') gte 0 || suser.roles?index_of('agreement') gte 0>
			<li>
				<a href="#">
					<i class="fa fa-lg fa-fw fa-bar-chart-o"></i>
					<span class="menu-item-parent">静态页面管理</span>
				</a>
				<ul>
					<#list menus as menu>
						<#if suser.type=='1' || suser.roles?index_of(menu.itemCode) gte 0>
						<li<#if itemCode==menu.itemCode?if_exists> class="active"</#if>><a href="${basePath}${menu.itemurl?if_exists}">${menu.title?if_exists}</a></li>
						</#if>
					</#list>
				</ul>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('active') gte 0>
			<li<#if itemCode="active"> class="active"</#if>>
				<a href="${basePath}/activity/list">
					<i class="fa fa-lg fa-fw fa-comments"></i>
					<span class="menu-item-parent">促销活动</span>
				</a>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('hotel') gte 0>
			<li<#if itemCode="hotel"> class="active"</#if>>
				<a href="${basePath}/hotel/list">
					<i class="fa fa-lg fa-fw fa-glass"></i>
					<span class="menu-item-parent">酒店管理</span>
				</a>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('horder') gte 0>
			<li<#if itemCode="horder"> class="active"</#if>>
				<a href="${basePath}/hotel/orders">
					<i class="fa fa-lg fa-fw fa-list-alt"></i>
					<span class="menu-item-parent">酒店排序</span>
				</a>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('image') gte 0>
			<li<#if itemCode="image"> class="active"</#if>>
				<a href="${basePath}/hotel/images">
					<i class="fa fa-lg fa-fw fa-picture-o"></i>
					<span class="menu-item-parent">图片管理</span>
				</a>
			</li>
			<li>
				<a href="${basePath}/hotel/images/search">
					<i class="fa fa-lg fa-fw fa-picture-o"></i>
					<span class="menu-item-parent">图片查询</span>
				</a>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('log') gte 0>
			<li<#if itemCode="log"> class="active"</#if>>
				<a href="${basePath}/log/list">
					<i class="fa fa-lg fa-fw fa-bug"></i>
					<span class="menu-item-parent">操作日志</span>
				</a>
			</li>
			</#if>
			<#elseif chainCode!="LW">
			<#if suser.type=='1' || suser.roles?index_of('active') gte 0>
			<li class="active">
				<a href="${basePath}/activity/list">
					<i class="fa fa-lg fa-fw fa-comments"></i>
					<span class="menu-item-parent">活动管理</span>
				</a>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('hot') gte 0>
			<li class="active">
				<a href="${basePath}/hot/edit">
					<i class="fa fa-lg fa-fw fa-comments"></i>
					<span class="menu-item-parent">热门酒店</span>
				</a>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('oversea') gte 0>
				<#if chainCode != 'LX'>
				<li class="active">
					<a href="${basePath}/hotel/list">
						<i class="fa fa-lg fa-fw fa-glass"></i>
						<span class="menu-item-parent">酒店管理</span>
					</a>
				</li>
				<#else>
				<li>
					<a href="#">
						<i class="fa fa-lg fa-fw fa-bar-chart-o"></i>
						<span class="menu-item-parent">酒店管理</span>
					</a>
					<ul>
						<li class="active"><a href="${basePath}/hotel/list">酒店列表</a></li>
						<li class="active"><a href="${basePath}/hotel/add">新增酒店</a></li>
					</ul>
				</li>
				</#if>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('image') gte 0>
			<li class="active" >
				<a href="${basePath}/hotel/images">
					<i class="fa fa-lg fa-fw fa-picture-o"></i>
					<span class="menu-item-parent">图片管理</span>
				</a>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('image-search') gte 0>
			<li>
				<a href="${basePath}/hotel/images/search">
					<i class="fa fa-lg fa-fw fa-picture-o"></i>
					<span class="menu-item-parent">图片查询</span>
				</a>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('oversea') gte 0>
			<li class="active" >
				<a href="${basePath}/country/toCountryList">
					<i class="fa fa-lg fa-fw fa-picture-o"></i>
					<span class="menu-item-parent">国家管理</span>
				</a>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('log') gte 0>
			<li class="active">
				<a href="${basePath}/log/list">
					<i class="fa fa-lg fa-fw fa-bug"></i>
					<span class="menu-item-parent">操作日志</span>
				</a>
			</li>
			<li class="active">
				<a href="${basePath}/tcsupdate/list">
					<i class="fa fa-lg fa-fw fa-bug"></i>
					<span class="menu-item-parent">差异对比</span>
				</a>
			</li>
			</#if>
			<#if suser.type=='1' || suser.roles?index_of('reservation-search') gte 0>
			<li>
				<a href="${basePath}/reservation/search">
					<i class="fa fa-lg fa-fw fa-picture-o"></i>
					<span class="menu-item-parent">订单查询</span>
				</a>
			</li>
			</#if>
			<#else>
			</#if>
		</ul>
	</nav>
	<span class="minifyme">
		<i class="fa fa-arrow-circle-left hit"></i>
	</span>
</aside>