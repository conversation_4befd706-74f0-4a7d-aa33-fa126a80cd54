<#macro foot basePath="">
<!--================================================== -->
<div class="modal fade" id="changeNameModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content" style="width: 480px;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">修改昵称</h4>
			</div>
			<div class="modal-body" style="padding-bottom: 8px;">
				<form id="bannerUploadForm" class="form-horizontal" method="post" action="${basePath}/user/name/change">
					<fieldset>
						<div class="form-group">
							<label class="col-md-3 control-label" for="name" style="font-size: 16px;">昵称</label>
							<div class="col-md-9">
								<input class="form-control" type="text" id="name" name="name" value="${(suser.name)?if_exists}" />
							</div>
						</div>
					</fieldset>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				<button type="button" class="btn btn-primary">保存</button>
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="changePasswdModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content" style="width: 580px;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">修改密码</h4>
			</div>
			<div class="modal-body" style="padding-bottom: 8px;">
				<form id="changePasswdForm" class="form-horizontal" method="post">
					<fieldset>
						<div class="form-group">
							<label class="col-md-3 control-label" for="oldpasswd" style="font-size: 16px;">原密码</label>
							<div class="col-md-9">
								<input class="form-control" type="password" id="oldpasswd" name="oldpasswd" />
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-3 control-label" for="newpasswd" style="font-size: 16px;">新密码</label>
							<div class="col-md-9">
								<input class="form-control" type="password" id="newpasswd" name="newpasswd" />
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-3 control-label" for="confirmpasswd" style="font-size: 16px;">确认新密码</label>
							<div class="col-md-9">
								<input class="form-control" type="password" id="confirmpasswd" name="confirmpasswd" />
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-12">
								<div class="alert alert-warning">
									<i class="fa-fw fa fa-warning"></i><strong>提示：</strong>
									<span>密码由6-20个字符组成，必须包含字母、数字及特殊符号（“_”、“$”、“#”、“@”符号之一）！</span>
								</div>
							</div>
						</div>
					</fieldset>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				<button type="button" class="btn btn-primary" id="uppwdb">修改密码</button>
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="imageViewModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog">
		<#if chainCode=="LW">
		<div class="modal-content">
			<img src="${basePath}/static/images/superbox/superbox-full-24.jpg" style="min-width: 768px; max-width: 840px;" />
		</div>
		</#if>
		<#if chainCode!="LW">
		<div>
			<img src="${basePath}/static/images/superbox/superbox-full-24.jpg"/>
		</div>
		</#if>
	</div>
</div>
<!-- PACE LOADER - turn this on if you want ajax loading to show (caution: uses lots of memory on iDevices)-->
<script data-pace-options='{ "restartOnRequestAfter": true }' src="${basePath}/static/js/plugin/pace/pace.min.js"></script>
      <script src="${basePath}/static/js/libs/jquery-2.0.2.min.js"></script>
      <script src="${basePath}/static/js/libs/jquery-ui-1.10.3.min.js"></script>

<!-- BOOTSTRAP JS -->
<script src="${basePath}/static/js/bootstrap/bootstrap.min.js"></script>

<!-- CUSTOM NOTIFICATION -->
<script src="${basePath}/static/js/notification/SmartNotification.min.js"></script>

<!-- JARVIS WIDGETS -->
<script src="${basePath}/static/js/smartwidgets/jarvis.widget.min.js"></script>

<!-- EASY PIE CHARTS -->
<script src="${basePath}/static/js/plugin/easy-pie-chart/jquery.easy-pie-chart.min.js"></script>

<!-- SPARKLINES -->
<script src="${basePath}/static/js/plugin/sparkline/jquery.sparkline.min.js"></script>

<!-- JQUERY VALIDATE -->
<script src="${basePath}/static/js/plugin/jquery-validate/jquery.validate.min.js"></script>

<!-- JQUERY MASKED INPUT -->
<script src="${basePath}/static/js/plugin/masked-input/jquery.maskedinput.min.js"></script>

<!-- JQUERY SELECT2 INPUT -->
<script src="${basePath}/static/js/plugin/select2/select2.min.js"></script>

<!-- JQUERY UI + Bootstrap Slider -->
<script src="${basePath}/static/js/plugin/bootstrap-slider/bootstrap-slider.min.js"></script>

<!-- browser msie issue fix -->
<script src="${basePath}/static/js/plugin/msie-fix/jquery.mb.browser.min.js"></script>

<!-- FastClick: For mobile devices -->
<script src="${basePath}/static/js/plugin/fastclick/fastclick.js"></script>

<!-- <script src="${basePath}/static/js/plugin/bootstrap-datepicker.js"></script> -->
<script src="${basePath}/static/js/bootstrap-datetimepicker.js"></script>
<script src="${basePath}/static/js/plugin/summernote/summernote.js"></script>

<!--[if IE 7]>

<h1>Your browser is out of date, please update your browser by going to www.microsoft.com/download</h1>

<![endif]-->

<!-- Demo purpose only -->
<!--
<script src="${basePath}/static/js/demo.js"></script>
      -->

<!-- MAIN APP JS FILE -->
<script src="${basePath}/static/js/app.js"></script>

<!-- PAGE RELATED PLUGIN(S) -->

<!-- Flot Chart Plugin: Flot Engine, Flot Resizer, Flot Tooltip -->
<script src="${basePath}/static/js/plugin/flot/jquery.flot.cust.js"></script>
<script src="${basePath}/static/js/plugin/flot/jquery.flot.resize.js"></script>
<script src="${basePath}/static/js/plugin/flot/jquery.flot.tooltip.js"></script>

<!-- Vector Maps Plugin: Vectormap engine, Vectormap language -->
<script src="${basePath}/static/js/plugin/vectormap/jquery-jvectormap-1.2.2.min.js"></script>
<script src="${basePath}/static/js/plugin/vectormap/jquery-jvectormap-world-mill-en.js"></script>
<script src="${basePath}/static/js/plugin/flot/jquery.flot.cust.js"></script>
<script src="${basePath}/static/js/plugin/flot/jquery.flot.resize.js"></script>
<script src="${basePath}/static/js/plugin/flot/jquery.flot.fillbetween.min.js"></script>
<script src="${basePath}/static/js/plugin/flot/jquery.flot.orderBar.js"></script>
<script src="${basePath}/static/js/plugin/flot/jquery.flot.pie.js"></script>
<script src="${basePath}/static/js/plugin/flot/jquery.flot.tooltip.js"></script>

<!-- PAGE RELATED PLUGIN(S) -->
<script src="${basePath}/static/js/plugin/datatables/jquery.dataTables-cust.min.js"></script>
<script src="${basePath}/static/js/plugin/datatables/ColReorder.min.js"></script>
<script src="${basePath}/static/js/plugin/datatables/FixedColumns.min.js"></script>
<script src="${basePath}/static/js/plugin/datatables/ColVis.min.js"></script>
<script src="${basePath}/static/js/plugin/datatables/ZeroClipboard.js"></script>
<script src="${basePath}/static/js/plugin/datatables/media/js/TableTools.min.js"></script>
<script src="${basePath}/static/js/plugin/datatables/DT_bootstrap.js"></script>

<!-- Full Calendar -->
<script src="${basePath}/static/js/plugin/fullcalendar/jquery.fullcalendar.min.js"></script>

<!-- 配置文件 -->
<script type="text/javascript" src="${basePath}/static/ueditor/ueditor.config.js"></script>
<!-- 编辑器源码文件 -->
<script type="text/javascript" src="${basePath}/static/ueditor/ueditor.all.js"></script>

<script type="text/javascript" src="${basePath}/static/select2/select2.min.js"></script>

<script type="text/javascript">

$(document).ready(function() {
	pageSetUp();
	
	var $changeNameModal = $('#changeNameModal');
	var $changeNameItem = $('#changeNameItem');
	$changeNameItem.on('click', function() {
		$changeNameModal.modal();
	});
	$changeNameModal.find('.btn-primary').on('click', function() {
		var name = $changeNameModal.find('input#name').val();
		name = $.trim(name);
		var params = {};
		params.name = name;
		var action = $changeNameModal.find('form').attr('action');
		$.post(action, params, function(data) {
			alert(data.desc);
			if(data.code == 1) {
				$('#suser_name').text(name);
				$changeNameModal.modal("hide");
			}
		}, 'json');
	});
	
	var $changePasswdModal = $('#changePasswdModal');
	var $changePasswdItem = $('#changePasswdItem');
	$changePasswdItem.on('click', function() {
		$changePasswdModal.modal();
	});
	
	$("#uppwdb").click(function(){
		var oldpwd = $("#oldpasswd").val();
		var newpwd = $("#newpasswd").val();
		var confirmpasswd = $("#confirmpasswd").val();
		if(!(oldpwd&&oldpwd!="")){
			alert("请填写原密码");
			return;
		}
		if(!(newpwd&&newpwd!="")){
			alert("请填写新密码");
			return;
		}
		if(!(confirmpasswd&&confirmpasswd!="")){
			alert("请填写确认密码");
			return;
		}
		if(confirmpasswd!=newpwd){
			alert("两次输入密码不一致");
			return;
		}
		if(newpwd.length>20){
			alert("密码长度不可超过20位");
			return;
		}
		var flag;
		$.ajax({
			async : false,
			type : "POST",
			dataType:"json",
			data:{
				oldpwd:oldpwd,
				newpwd:newpwd
			},
			url :  "${basePath}/user/uppwd",
			error : function(data) {
				return "执行失败";
			},
			success : function(data) {
				if(data.code==1){
					alert(data.desc);
					flag=true;
				}else{
					alert(data.desc);
					flag=false;
				}
			}
		});
		if(flag){
			$("#changePasswdModal").modal("hide");
		}
	});
	
	$('.viewBanner').on('click', function() {
		var $obj = $(this);
		var imageUrl = $obj.attr('data-img');
		var $imageViewModal = $('#imageViewModal');
		if(imageUrl != '' && imageUrl != '${imageUrl?if_exists}/') {
			$imageViewModal.find('img').attr('src', imageUrl);
			$imageViewModal.modal();
		} else {
			alert('图片还未上传');
		}
	});
	
	/** 分页 **/
	$('#pageSelect').on('change', function() {
		var pageNo = $(this).find('option:selected').text();
		toPage(pageNo);
	});
});
//图片上传完成后调用
function convertImageHtml(datas) {
	var html = '';
	for(var i=0; i<datas.length; i++) {
		var data = datas[i];
		html += '<div class="superbox-list" style="text-align: center;"><img src="${imageUrl?if_exists}/'+data.url+'" onclick="selectImage(\''+data.url+'\')" class="superbox-img" /><a href="javascript:void(0);" onclick="deleteSuperImage(\''+data.id+'\', this);">删除</a></div>';
	}
	html += '<div class="superbox-float"></div>';
	return html;
}
function uploadResult(fcode, fname) {
	if(fcode == '1') {
		alert('没有要上传的图片！');
		return;
	}
	if(fcode == '2') {
		alert('上传的图片不能超过2M!');
		return;
	}
	if(fcode == '3') {
		alert('只允许上传jpg格式的图片！');
		return;
	}
	if(fname == '') {
		alert('没有要上传的图片！');
		return;
	}
	var url = '${imageUrl}/'+fname;
	var html = '<div class="superbox-list" style="text-align: center;"><img src="'+url+'" onclick="selectImage(\''+fname+'\')" class="superbox-img" /><a href="javascript:void(0);" onclick="deleteSuperImage(\'\', this);">删除</a></div>';
	var $selectBannerModal = $('#selectBannerModal');
	if($selectBannerModal.length>0) {
		$selectBannerModal.find('input[type=file]').val('');
		$selectBannerModal.find('.superbox').append(html);
	}
	var $checkCoverModal = $('#checkCoverModal');
	if($checkCoverModal.length>0) {
		$checkCoverModal.find('input[type=file]').val('');
		$checkCoverModal.find('.confirmCover').attr('src', url).attr('data-img-path', fname);
		$checkCoverModal.find('.superbox').append(html);
	}
}
function deleteSuperImage(fid, obj) {
	if(confirm('确定要删除该图片吗？')) {
		if(fid == '') {
			alert('请重新打开选择层删除该图片！');
			return;
		}
		var params = {};
		params.fid = fid;
		$.post('${basePath}/image/remove', params, function(data) {
			if(data.code == '1') {
				alert('删除成功！');
				$(obj).parent().remove();
			} else {
				alert('删除失败！');
			}
		}, 'json');
	}
}
//$.root_.addClass("container");
//$.root_.removeClassPrefix('smart-style').addClass("smart-style-3");
</script>
<#nested>
</#macro>