<#assign basePath = "${rc.getContextPath()}" />
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>console</title>
<style type="text/css">
.requesttable {
	margin-left: 20px;
	border-width: 1px;
	border-style: solid;
	border-color: #101c4b;
	background-color: #ffffff;
	padding: 0px;
	font-size: 11pt;
	font-family: Arial, Helvetica;
	border-collapse: collapse;
}

.xrow {
	border-style: dotted;
	border-width: 1px;
}

.xcolNoWidth {
	height: 24px;
	text-align: center;
	border-width: 1px;
	border-style: solid;
	border-color: #101c4b;
	font-weight: bold;
}

.xcol {
	height: 24px;
	text-align: left;
	border-width: 1px;
	border-style: dotted;
	border-width: 1px;
	border-color: #101c4b;
}

#connect-container {
	float: left;
	width: 400px
}

#connect-container div {
	padding: 5px;
}

#console-container {
	float: left;
	margin-left: 15px;
	width: 400px;
}

#console {
	border: 1px solid #CCCCCC;
	border-right-color: #999999;
	border-bottom-color: #999999;
	height: 170px;
	overflow-y: scroll;
	padding: 5px;
	width: 100%;
}

#console p {
	padding: 0;
	margin: 0;
}
</style>
<script src="${basePath}/static/js/jquery-1.11.1.min.js"></script>
<script type="text/javascript">
	var _basePath = '${basePath}';
	console.log("_basePath: " + _basePath);
</script>
<script src="${basePath}/static/js/xml2json.js"></script>
<script src="${basePath}/static/js/httplog.js"></script>
<script src="${basePath}/static/js/console.js"></script>
<script src="${basePath}/static/js/sockjs-0.3.min.js"></script>
</head>
<body>
	参数区：
	<select id="methodSelect" name="methodSelect"
		onchange="methodSelected()">
		<option value="showAllParameters" selected>显示所有参数</option>
		<option value="downloadHotelContentXml">TCSContent</option>
		<option value="propertySearchCountryList">property_search -
			(country_list)</option>
		<option value="propertySearchCityList">property_search
			(city_list)</option>
		<option value="propertySearchPropertyList">property_search
			(property_list)</option>
		<option value="availability">availability</option>
		<option value="availabilityCalendarRequest">availabilityCalendarRequest</option>
		<option value="multiPropertyAvailabilityRequest">multiPropertyAvailabilityRequest</option>
		<option value="rateInfoRequest">rateInfoRequest
			</option>
		<option value="">------------------------------
			</option>
		<option value="importTcsContentXml">批量下载并入库酒店内容XMl信息
			</option>
		<option value="updateRoomRateListCache">更新房价缓存
			</option>
		<option value="downloadAllData">下载国家/城市/酒店信息
			</option>
	</select>
	<br />
	<div id="chainCodeDiv" name="chainCodeDiv">
		chain_code: <input type="text" id="chainCode" name="chainCode"
			value="LX" size="4" />(LHW项目为LW,SLH项目为LX)&nbsp;&nbsp;
		crs_api: <input type="text" id="crsApi" name="crsApi"
			value="sabre" size="4" />(sabre,trust)&nbsp;&nbsp;
	</div>
	<div id="idTypeDiv" name="idTypeDiv">
		idType: <input type="text" id="idType" name="idType" value="slh"
			size="4" />(LHW项目为lhw,SLH项目为slh,区分大小写)&nbsp;&nbsp;
	</div>
	<div id="countryCodeDiv">
		国家代码: <input type="text" id="countryCode" name="countryCode"
			value="CN" size="4" />(默认中国)&nbsp;&nbsp;
	</div>
	<div id="cityCodeDiv">
		城市代码: <input type="text" id="cityCode" name="cityCode" value="SHA001"
			size="8" />(默认上海)&nbsp;&nbsp;
	</div>
	<div id="guaranteeTypesDi">
		guarantee_types: <input type="text" id="guaranteeTypes" name="guaranteeTypes"
			value="C" size="10" />&nbsp;&nbsp;(列表，可以选：A, C, D, H, P, X)
	</div>
	<div id="hotelCodeDiv">
		hotelCode: <input type="text" id="hotelCode" name="hotelCode"
			value="78557" size="10" />(LHW项目前缀为HL,SLH项目前缀为HU，如HUADLTM)&nbsp;&nbsp;
	</div>
	<div id="hotelCodesDiv">
		<span>hotelCodes:</span>
		<textarea id="hotelCodes" name="hotelCodes" rows="10" cols="100"></textarea>
		(一个酒店一行,LHW项目前缀为HL,SLH项目前缀为HU，如HUADLTM)&nbsp;&nbsp;
	</div>
	<div id="indateDiv">
		入住日期(yyyyMMdd): <input type="text" id="indate" name="indate"
			value="20161219" size="10" />&nbsp;&nbsp;
	</div>
	<div id="nightsDiv">
		房晚数： <input type="text" id="nights" name="nights" value="1" size="4" />&nbsp;&nbsp;
	</div>
	<div id="nightsDiv">
		成人数： <input type="text" id="adultsPerRoom" name="adultsPerRoom"
			value="2" size="4" />&nbsp;&nbsp;
	</div>
	<div id="nightsDiv">
		儿童数： <input type="text" id="childrenPerRoom" name="childrenPerRoom"
			value="0" size="4" />&nbsp;&nbsp;
	</div>
	<div id="nightsDiv">
		房间数： <input type="text" id="rooms" name="rooms" value="1" size="4" />&nbsp;&nbsp;
	</div>
	<div id="roomRateCodeDiv">
		roomRateCode： <input type="text" id="roomRateCode"
			name="roomRateCode" value="IC1KL04" size="10" />&nbsp;&nbsp;
	</div>
	<div id="rateCodesDiv">
		rate_codes： <input type="text" id="rateCodes" name="rateCodes"
			value="L04,L05,L06,L07,L08,L11,L33,L36,L37,L60,LS1,LSW,RR,S1,S11,S2"
			size="100" />(每次最多只能查询8个)&nbsp;&nbsp;
	</div>
	<br />方法区：
	<br />
	<div>
		<button onclick="downloadHotelContentXml();">从trust下载酒店的基本信息TCSContent</button>
		<button onclick="propertySearchCountryList();">下载国家列表property_search
			- (country_list)</button>
		<button onclick="propertySearchCityList();">下载城市列表property_search
			(city_list)</button>
		<button onclick="propertySearchPropertyList();">下载酒店列表property_search
			(property_list)</button>
		<button onclick="availability();">房型级房价查询availability</button>
		<button onclick="availabilityCalendarRequest();">availabilityCalendarRequest</button>
		<button onclick="multiPropertyAvailabilityRequest();">酒店级房价查询multiPropertyAvailabilityRequest</button>
		<button onclick="rateInfoRequest();">预订房价查询rateInfoRequest</button>
		<button onclick="staticSLHSite();">SLH网站静态化</button>
		<button onclick="reloadRedisCache();">更新redis缓存</button>
		<button onclick="test();">特别测试按钮</button>
	</div>
	<button onclick="showUpdateMethod();" id="showUpdateMethod"
		name="showUpdateMethod">显</button>
	<div id="updateMethod" name="updateMethod" style="display: none;">
		<span style="color: red;">以下方法不知道含义的不要乱点！</span><br />
		<button onclick="importTcsContentXmlByChainCode();">按指定的chainCode下载并入库酒店内容XMl信息</button>
		<button onclick="importTcsContentXml();">按指定的hotelCode下载酒店XMl信息</button>
		<button onclick="parseXmlHotelAndRoom();">解析并入库酒店和房型信息</button>
		<button onclick="importLandMarkTrsansport();">从xml中导入地标和交通</button>
		<button onclick="importHotelPicture();">下载酒店图片</button>
		<button onclick="importRoomPicture();">下载房型图片</button>
		<button onclick="importCountryBannerPicture();">下载slh国家banner图片</button>
		<button onclick="downloadFtpHotelImage();">从ftp下载酒店banner图片url</button>
		<button onclick="updateRateCache();">更新价格缓存</button>
		<button onclick="updateRoomRateListCache();">更新房型级房价缓存</button>
		<button onclick="updateHotelRateListCache();">更新酒店最低价缓存</button>
		<button onclick="downloadAllData();">下载国家/城市/酒店信息(调用tcs的propertySearch接口)</button>
		<button onclick="syncCityFromHotel();">从酒店表更新国家和城市表</button>
		<button onclick="updateExchangeRate();">从www.juhe.com更新汇率</button>
		<button onclick="updateExchangeRateFtp();">从lhw的ftp更新汇率</button>
		<button onclick="refrashTrustRateCache();">刷新trust价格缓存(不指定酒店代码会刷新所有)</button>
		<button onclick="reThumbnailHotelImages();">重新生成酒店图片缩略图</button>
		<button onclick="reThumbnailCountryImages();">重新生成国家图片缩略图</button>
	</div>
	<br />sabre方法区：
	<div>
		<div>入住日期(yyyy-MM-dd): <input type="text" id="start" name="start" value="2024-01-29" size="10" />&nbsp;&nbsp;
			退房日期(yyyy-MM-dd): <input type="text" id="end" name="end" value="2024-01-30" size="10" />&nbsp;&nbsp;
			verifyStartDate(yyyy-MM-dd): <input type="text" id="verifyStartDate" name="verifyStartDate" value="2024-01-29" size="10" />&nbsp;&nbsp;
			<br/>
			roomTypeCode: <input type="text" id="roomTypeCode" name="roomTypeCode" value="CLE" size="10" />&nbsp;&nbsp;
			ratePlanCode: <input type="text" id="ratePlanCode" name="ratePlanCode" value="BARBKF" size="10" />&nbsp;&nbsp;
			<br/>
			firstName: <input type="text" id="firstName" name="firstName" value="su" size="10" />&nbsp;&nbsp;
			lastName: <input type="text" id="lastName" name="lastName" value="slee" size="10" />&nbsp;&nbsp;
			email: <input type="text" id="email" name="email" value="<EMAIL>" size="10" />&nbsp;&nbsp;
			phone: <input type="text" id="phone" name="phone" value="86-1234-5678" size="10" />&nbsp;&nbsp;
			<br/>
			CreditCardCode: <input type="text" id="creditCardCode" name="creditCardCode" value="VI" size="10" />&nbsp;&nbsp;
			CardNumber: <input type="text" id="cardNumber" name="cardNumber" value="****************" size="10" />&nbsp;&nbsp;
			CardExpireDate: <input type="text" id="cardExpireDate" name="cardExpireDate" value="09/33" size="10" />&nbsp;&nbsp;
			<br/>
			CardCVV: <input type="text" id="cardCVV" name="cardCVV" value="123" size="10" />&nbsp;&nbsp;
			CardHolderName: <input type="text" id="cardHolderName" name="cardHolderName" value="slee" size="10" />&nbsp;&nbsp;
			<br/>
		订单号： <input type="text" id="reservationId" name="reservationId" value="" size="40" />&nbsp;&nbsp;
		客户profileId： <input type="text" id="profileId" name="profileId" value="" size="40" />&nbsp;&nbsp;
		<br/>
		EchoData： <input type="text" id="echoData" name="echoData" value="" size="40" />&nbsp;&nbsp;
		</div>
		<button onclick="getHotelDescriptiveInfo();">获取酒店基本信息</button>
		<button onclick="testSabreHotelAvailability();">sabre获取酒店价格</button>
		<button onclick="testCreateHotelReservation();">sabre创建订单</button>
		<button onclick="testConfirmHotelReservation();">sabre确认订单</button>
		<button onclick="createHotelReservation();">创建订单</button>
		<button onclick="testRollbackHotelReservation();">回滚订单</button>
		<button onclick="cancelHotelReservation();">取消订单</button>
		<button onclick="testModifyHotelReservation();">修改订单</button>
		<button onclick="testRetrieveHotelReservation();">查询订单</button>
		<button onclick="testVerifyHotelReservation();">验证订单</button>
		<button onclick="testPing();">Ping</button>
		<button onclick="getPreferredHotelList();">璞富腾酒店列表文件下载及解析</button>
		<br/>
	<br />
	<br /> 执行结果：
	<br />
	<xmp id="resultXML" name="resultXML"></xmp>
	<div id="resultText" name="resultText"></div>
	json格式：
	<textarea id="result" name="result" rows="20" cols="150"></textarea>

	<button onclick="executeSql();">执行sql(太危险了！)</button>&nbsp;&nbsp;&nbsp;&nbsp;<button onclick="executeSearchSql();">执行查询sql</button>
	<textarea id="sql" name="sql" rows="4" cols="120">select * from config</textarea>
	<div>结果：<button onclick="javascript:tableToExcel('resultTable','excel');">tableToExcel</button></div>
	<div id="sqlResult" name="sqlResult"></div>


	<div></div>
	<script type="text/javascript">
		
	</script>
</body>
</html>