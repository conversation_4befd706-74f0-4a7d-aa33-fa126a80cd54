<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body id="login" class="animated fadeInDown">
<#include "/common/header.ftl" />
<div id="main" role="main">
	<div id="content" class="container">
       	<div class="row">
       		<div class="col-xs-12 col-sm-4 col-md-4 col-lg-4"></div>
			<div class="col-xs-12 col-sm-4 col-md-4 col-lg-4">
				<div class="well no-padding">
					<form id="login-form" class="smart-form client-form" action="${rc.getContextPath()}/doLogin" method="post" data-uri="${url}">
						<header>
							<h1 class="txt-color-red login-header-big">登录</h1>
						</header>
						<fieldset>
							<section>
								<!-- <label class="label">用户名</label> -->
								<label class="input"><i class="icon-append fa fa-user"></i>
									<input type="text" id="username" name="username" placeholder="请输入用户名" autofocus="autofocus" />
									<b class="tooltip tooltip-top-right"><i class="fa fa-user txt-color-teal"></i>请输入用户名</b>
								</label>
							</section>
							<section>
								<!-- <label class="label">密码</label> -->
								<label class="input"><i class="icon-append fa fa-lock"></i>
									<input type="password" id="password" name="password" placeholder="请输入密码">
									<b class="tooltip tooltip-top-right"><i class="fa fa-lock txt-color-teal"></i>请输入密码</b>
								</label>
							</section>
						</fieldset>
						<footer>
							<label class="txt-color-red" style="margin-top: 16px;"><span class="invalid" id="validTip" value="${errorMsg }"></span></label>
							<button type="submit" class="btn btn-primary">登录</button>
						</footer>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
runAllForms();
$(document).ready(function() {
	var $loginForm = $('#login-form');
	var $username = $('#username');
	var $password = $('#password');
	var $validTip = $('#validTip').hide();
	$username.on('keydown', function() {
		clearValid();
	});
	$password.on('keydown', function() {
		clearValid();
	});
	$username.on('keydown', function(event) {
		if(event.keyCode==13) {
			loginAction();
		}
	});
	$password.on('keydown', function(event) {
		if(event.keyCode==13) {
			loginAction();
		}
	});
	$('#loginBtn').on('click', function() {
		loginAction();
		return false;
	});
	
	function loginAction() {
		var url = '${rc.getContextPath()}/doLogin';
		var params = $loginForm.serializeArray();
		var turl = $loginForm.attr('data-uri');
		$.post(url, params, function(data) {
			if(data.code == '1') {
				$username.parent().removeClass('state-error').addClass('state-success');
				$username.addClass('valid')
				$password.parent().removeClass('state-error').addClass('state-success');
				$password.addClass('valid');
				$validTip.hide();
				window.location.href = turl;
			} else if(data.code == '2') {
				$username.parent().removeClass('state-success').addClass('state-error');
				$username.addClass('invalid').focus();
				$validTip.text(data.desc).show();
			} else if(data.code == '3') {
				$password.parent().removeClass('state-success').addClass('state-error');
				$password.addClass('invalid').focus();
				$validTip.text(data.desc).show();
			} else {
				$username.parent().removeClass('state-success').addClass('state-error');
				$username.addClass('invalid').focus();
				$password.parent().removeClass('state-success').addClass('state-error');
				$password.addClass('invalid');
				$validTip.text(data.desc).show();
			}
		}, 'json');
	}
	
	
	function clearValid() {
		$username.parent().removeClass('state-success').removeClass('state-error');
		$username.removeClass('invalid').removeClass('valid');
		$password.parent().removeClass('state-success').removeClass('state-error');
		$password.removeClass('invalid').removeClass('valid');
		$validTip.hide();
	}
});
</script>
</body>
</html>