<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>图片管理</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="image-list-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>图片管理列表</h2>
						</header>
						<div>
							<div class="widget-body no-padding">
								<div class="widget-body-toolbar">
									<form id="queryForm" class="form-inline" method="post" action="${basePath}/image/list">
										<input type="hidden" id="start" name="start" value="${page.currentPage}" />
										<input type="hidden" id="limit" name="limit" value="${limit}" />
										<!--<div class="form-group" style="margin: 0px 15px 8px 0px;">
											 <label class="control-label" for="keyword">关键字：</label>
						                	<input type="text" class="form-control" style="width: 360px" id="keyword" name="keyword" value="${keyword?if_exists}" placeholder="图片关键字" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<input type="submit" class="btn btn-primary" value="查询" style="margin-right: 8px;" />
					                	</div>-->
									</form>
								</div>
								<div class="superbox col-sm-12" style="margin-top: 12px; margin-bottom: 12px;">
									<#list page.datas as data>
										<div class="superbox-list">
											<img src="${imageUrl}/${data.url}" data-img="${imageUrl}/${data.url}" class="superbox-img viewBanner">
										</div>
									</#list>
									<div class="superbox-float"></div>
								</div>
								<#include "/common/page.ftl" />
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script>
	function toPage(pageNo) {
	$('#start').val(pageNo);
	$('#queryForm').submit();
}
</script>
</body>
</html>