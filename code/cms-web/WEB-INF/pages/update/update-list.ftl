<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}"> </@head.head>
<body>
<#include "/common/header.ftl" /> <#include "/common/menu.ftl" />
<div id="main" role="main">
    <div id="ribbon">
        <ol class="breadcrumb">
            <li>差异对比</li>
        </ol>
    </div>
    <div id="content">
        <section id="widget-grid" class="">
            <div class="row">
                <article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="jarviswidget jarviswidget-color-darken"
                         data-widget-editbutton="false"
                         data-widget-sortable="false" data-widget-colorbutton="false"
                         data-widget-fullscreenbutton="false"
                         data-widget-deletebutton="false">
                        <header>
                            <span class="widget-icon"> <i class="fa fa-table"></i>
                            </span>
                            <h2>酒店列表</h2>
                        </header>
                        <div>
                            <div class="">
                                <div class="widget-body-toolbar">
                                    <form id="queryForm" class="form-inline" method="post"
                                          action="${basePath}/tcsupdate/list">
                                        <div class="form-group" style="margin: 0px 15px 8px 0px;">
                                            <label class="control-label" for="minDate">更新日期：</label>
                                            <input class="form-control" type="text" style="width: 120px"
                                                   class="form-control" id="startTime" name="startTime"
                                                   data-date-format="yyyy-mm-dd" readonly="readonly"
                                                   value="${startTime}" placeholder="起始日期"/>
                                            <span>-</span>
                                            <input class="form-control" type="text" style="width: 120px"
                                                   class="form-control" id="endTime" name="endTime"
                                                   data-date-format="yyyy-mm-dd" readonly="readonly"
                                                   value="${endTime}" placeholder="结束日期"/>
                                        </div>
                                        <div class="form-group" style="margin: 0px 15px 8px 0px;">
                                            <input type="submit" class="btn btn-primary" value="查询"
                                                   style="margin-right: 8px;"/>
                                        </div>
                                    </form>
                                </div>
                                <table id="dt_basic"
                                       class="table table-striped table-bordered table-hover"
                                       style="max-height: 300px;overflow-y: auto;">
                                    <thead>
                                    <tr>
                                        <th>酒店代码</th>
                                        <th width="140">更新时间</th>
                                        <th>酒店英文地址<!--<br/>addressEn--></th>
                                        <th>最早入住时间<!--<br/>checkInAfter--></th>
                                        <th>最晚退房时间<!--<br/>checkOutBefore--></th>
                                        <th>最大儿童年龄<!--<br/>maxChildrenAge--></th>
                                        <th>经度<!--<br/> longitude--></th>
                                        <th>纬度<!--<br/> latitude--></th>
                                        <th>游泳池<!--<br/>hasPools--></th>
                                        <th>餐厅数<!--<br/>restaurantsNumber--></th>
                                        <th>楼层数<!--<br/>floorsNumber--></th>
                                        <th>客房数<!--<br/>roomsTotalNumber--></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <#list hotel as data>
                                        <tr class="small">
                                            <td>${data.hotelcode}</td>
                                            <td style="text-align: center;">${data.updatetime?string("yyyy-MM-dd HH:mm:ss")}</td>
                                            <td>${data.addressEn}</td>
                                            <td>${data.checkInAfter}</td>
                                            <td>${data.checkOutBefore}</td>
                                            <td>${data.maxChildrenAge}</td>
                                            <td>${data.longitude}</td>
                                            <td>${data.latitude}</td>
                                            <td>${data.hasPools}</td>
                                            <td>${data.restaurantsNumber}</td>
                                            <td>${data.floorsNumber}</td>
                                            <td>${data.roomsTotalNumber}</td>
                                        </tr>
                                    </#list>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </article>
            </div>
            <div class="row">
                <article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="jarviswidget jarviswidget-color-darken"
                         data-widget-editbutton="false"
                         data-widget-sortable="false" data-widget-colorbutton="false"
                         data-widget-fullscreenbutton="false"
                         data-widget-deletebutton="false">
                        <header>
                            <span class="widget-icon"> <i class="fa fa-table"></i>
                            </span>
                            <h2>房型列表</h2>
                        </header>
                        <div style="max-height: 300px;overflow-y: auto;">
                            <table id="dt_basic"
                                   class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr>
                                    <th>酒店代码</th>
                                    <th>房型代码</th>
                                    <th width="140">更新时间</th>
                                    <th>房型英文名称<!--<br/>roomNameEn--></th>
                                    <th>房型英文描述<!--<br/>roomDescEn--></th>
                                    <th>房型英文描述<!--<br/>bookerTextEn--></th>
                                    <th>免费wifi<!--<br/>freeWifi--></th>
                                    <th>床型代码<!--<br/>bedType--></th>
                                    <th>最大入住人数<!--<br/>maxOccupancy--></th>
                                    <th>可加床数<!--<br/>rollAwayCount--></th>
                                    <th>特殊入住要求<!--<br/>rmaCode--></th>
                                    <th>新增房型</th>
                                </tr>
                                </thead>
                                <tbody>
                                <#list room as data>
                                    <tr class="small">
                                        <td>${data.hotelcode}</td>
                                        <td>${data.roomcode}</td>
                                        <td style="text-align: center;">${data.updatetime?string("yyyy-MM-dd HH:mm:ss")}</td>
                                        <td>${data.roomNameEn}</td>
                                        <td>${data.roomDescEn}</td>
                                        <td>${data.bookerTextEn}</td>
                                        <td>${data.freeWifi}</td>
                                        <td>${data.bedType}</td>
                                        <td>${data.maxOccupancy}</td>
                                        <td>${data.rollAwayCount}</td>
                                        <td>${data.rmaCode}</td>
                                        <td>${data.newRoom!0}</td>
                                    </tr>
                                </#list>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </article>
            </div>
            <div class="row">
                <article class="col-xs-6 col-sm-6 col-md-6 col-lg-6">
                    <div class="jarviswidget jarviswidget-color-darken"
                         data-widget-editbutton="false"
                         data-widget-sortable="false" data-widget-colorbutton="false"
                         data-widget-fullscreenbutton="false"
                         data-widget-deletebutton="false">
                        <header>
                            <span class="widget-icon"> <i class="fa fa-table"></i>
                            </span>
                            <h2>景点列表</h2>
                        </header>
                        <div style="max-height: 300px;overflow-y: auto;">
                            <table id="dt_basic"
                                   class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr>
                                    <th>酒店代码</th>
                                    <th width="140">更新时间</th>
                                    <th>英文名称<!--<br/>roomDescEn--></th>
                                    <th width="45">方向<!--<br/>bookerTextEn--></th>
                                    <th>距离<!--<br/>freeWifi--></th>
                                </tr>
                                </thead>
                                <tbody>
                                <#list landmark as data>
                                    <tr class="small">
                                        <td>${data.hotelCode}</td>
                                        <td style="text-align: center;">${data.updatetime?string("yyyy-MM-dd HH:mm:ss")}</td>
                                        <td>${data.nameEn}</td>
                                        <td>${data.direction}</td>
                                        <td style="text-align: right;">${data.distance}</td>
                                    </tr>
                                </#list>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </article>

                <article class="col-xs-6 col-sm-6 col-md-6 col-lg-6">
                    <div class="jarviswidget jarviswidget-color-darken"
                         data-widget-editbutton="false"
                         data-widget-sortable="false" data-widget-colorbutton="false"
                         data-widget-fullscreenbutton="false"
                         data-widget-deletebutton="false">
                        <header>
                            <span class="widget-icon"> <i class="fa fa-table"></i>
                            </span>
                            <h2>交通列表</h2>
                        </header>
                        <div style="max-height: 300px;overflow-y: auto;">
                            <table id="dt_basic"
                                   class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr>
                                    <th>酒店代码</th>
                                    <th width="140">更新时间</th>
                                    <th>类型<!--<br/>type--></th>
                                    <th>英文名称<!--<br/>name_en--></th>
                                    <th width="45">方向<!--<br/>direction--></th>
                                    <th>距离<!--<br/>distance--></th>
                                </tr>
                                </thead>
                                <tbody>
                                <#list transport as data>
                                    <tr class="small">
                                        <td>${data.hotelCode}</td>
                                        <td style="text-align: center;">${data.updatetime?string("yyyy-MM-dd HH:mm:ss")}</td>
                                        <td>${data.type}</td>
                                        <td>${data.nameEn}</td>
                                        <td>${data.direction}</td>
                                        <td style="text-align: right;">${data.distance}</td>
                                    </tr>
                                </#list>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </article>
            </div>
        </section>
    </div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script>
    $(document).ready(
        function () {
            $('#startTime').datepicker();
            $('#endTime').datepicker();
        });
</script>
</body>
</html>