<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>促销活动酒店</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="active-hotel-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>新增/修改促销活动酒店</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/about/save">
									<fieldset>
										<div class="form-group">
											<label class="col-md-2 control-label" for="hotel" style="font-size: 16px;">酒店</label>
											<div class="col-md-9">
												<input class="form-control" type="text" id="id" value="${activityHotel.id?if_exists}" style="display:none"/>
												<input class="form-control" type="text" id="hotelCode" value="${activityHotel.hotelCode?if_exists}" style="display:none"/>
												<input class="form-control" type="text" id="hotel" name="hotel" readonly="readonly" value="${activityHotel.hotelName?if_exists}"/>
											</div>
											<div class="col-md-1">
												<input type="button" id="selectHotelBtn" class="btn btn-success" value="选取" />
											</div>
										</div>
										<#if chainCode?if_exists=="LW">
										<div class="form-group">
											<label class="col-md-2 control-label" for="banner" style="font-size: 16px;">封面</label>
											<div class="col-md-7">
												<input class="form-control" type="text" id="cover" name="cover" readonly="readonly" value="${activityHotel.cover?if_exists}"/>
											</div>
											<div class="col-md-1">
												<input type="button" id="selectBannerBtn" class="btn btn-success" value="选取" />
											</div>
											<div class="col-md-2">
												<input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${activityHotel.cover?if_exists}" value="查看封面" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="btntext" style="font-size: 16px;">按钮文字</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="btntext" name="btntext" value="${activityHotel.btntext?if_exists}"/>
											</div>
										</div>
										</#if>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" id="addHotelb" style="margin-right: 12px;" />
												<input type="button" class="btn btn-default" value="返回" onclick="window.history.go(-1)" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
var activityId = "${activityHotel.activityId?if_exists}";
if(!(activityId&&!activityId=="")){
	activityId = "";
}
$(document).ready(function() {
	var $editForm = $('#editForm');
	var $selectBannerModal = $('#selectBannerModal');
	var selectBanner = function() {
		var params = {};
		var type ='active-hotel-cover';
		params.relateType = type;
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val(type);
		$selectBannerModal.find('input[name=relateCode]').val($('#itemCode').val());
		$selectBannerModal.find('input[name=format]').val('223*221c.jpg');
		$selectBannerModal.find('#imgsize').text('223 x 221');
		$selectBannerModal.find('.modal-title').text('添加封面');
		$selectBannerModal.modal();
	};
	$selectBannerModal.find('.superbox img').on('click', function() {
		var $obj = $(this);
		$itemBanner.val('${imageUrl}/'+$obj.attr('src'));
		$selectBannerModal.modal('hide');
	});
	var $itemBanner = $('#cover');
	$('#selectBannerBtn').on('click', selectBanner);
	$itemBanner.on('click', selectBanner);
	
	var $selectHotelModal = $('#selectHotelModal');
	var $selectHotelBtn = $('#selectHotelBtn');
	var $hotel = $('#hotel');
	$hotel.on('click', function() {
		$selectHotelModal.find('iframe').attr('src', '${basePath}/hotel/select');
		$selectHotelModal.modal();
	});
	$selectHotelBtn.on('click', function() {
	$selectHotelModal.find('iframe').attr('src', '${basePath}/hotel/select');
		$selectHotelModal.modal();
	});
	$('#addHotelb').click(addHotel);
	
});
function selectImage(url) {
	$('#cover').val(url);
	$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};

function selectHotelInfo(hotelCode, hotelName, hotelNameEn, countryCode, countryName, cityCode, cityName) {
	$('#hotelCode').val(hotelCode);
	$('#hotel').val(hotelName+"/"+hotelNameEn);
	$('#selectHotelModal').modal('hide');
}
function addHotel(){
	var btntext = $.trim($('#btntext').val());
	if(btntext.length>6) {
		alert('按钮文字不能超过6个字');
		return false;
	}
	$('#editForm').validate({
					 submitHandler:function(){
					 	$.ajax({
							async : false,
							type : "POST",
							dataType:"json",
							data:{
								hotelCode:$('#hotelCode').val(),
								cover:$('#cover').val(),
								activityId:${activityHotel.activityId},
								btntext:$('#btntext').val(),
								id:$('#id').val(),
							},
							url :  "${basePath}/activity/addHotel",
							error : function(data) {
								return "执行失败";
							},
							success : function(data) {
								if(data.code==1){
									alert(data.desc);
									window.location="${basePath}/activity/info?id="+activityId;
								}else{
									alert(data.desc);
								}
							}
						});
					 },
					rules: {
						title: {
							required: true
						},
					},
					messages: {
						title: {
							required: '请输入标题'
						},
					}
				});
}
</script>
</body>
</html>