<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>促销活动</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="active-edit-1" 
						data-widget-editbutton="false" data-widget-sortable="false" 
						data-widget-colorbutton="false" data-widget-fullscreenbutton="false" 
						data-widget-deletebutton="false">
						<header>
							<span class="widget-icon" style="width:100px;">
								<i class="fa fa-table">&nbsp;&nbsp;<#if ((activity.id)?default(0)>0) >修改活动<#else>创建活动</#if></i>
							</span>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/about/save">
									<input type="hidden" id="id" name="id"  value="${activity.id?if_exists}"/>
									<input type="hidden" id="chainCode" name="chainCode"  value="${activity.chainCode?if_exists}"/>
									<input type="hidden" id="precontent" name="precontent" />
									<input type="hidden" id="precontentMobile" name="precontentMobile" />
									
									<div class="form-group">
										<label class="col-md-2 control-label" for="type" style="font-size: 12px;">类型：</label>
										<div class="col-md-10">
											<select class="col-xs-10 col-sm-5" id="type" name="type">
												<option value="">请选择...</option>
												<option value="experience" <#if activity.type?if_exists=="experience">selected="selected"</#if>>主题活动</option>
												<option value="weChat" <#if activity.type?if_exists=="weChat">selected="selected"</#if>>微信精选</option>
												<option value="promotion" <#if activity.type?if_exists=="promotion">selected</#if>>优惠活动</option>
												<!-- <option value="activity" <#if activity.type?if_exists=="activity">selected</#if>>活动</option> -->
												<option value="news" <#if activity.type?if_exists=="news">selected</#if>>新闻</option>
												<option value="about-us" <#if activity.type?if_exists=="about-us">selected</#if>>关于我们</option>
												<option value="social-media" <#if activity.type?if_exists=="social-media">selected</#if>>媒体对接</option>
												<option value="contact-us" <#if activity.type?if_exists=="contact-us">selected</#if>>联系我们</option>
												<option value="travel-agents" <#if activity.type?if_exists=="travel-agents">selected</#if>>旅行社</option>
												<option value="index-swiper-pc" <#if activity.type?if_exists=="index-swiper-pc">selected</#if>>PC端首页轮播图</option>
												<option value="index-swiper-m" <#if activity.type?if_exists=="index-swiper-m">selected</#if>>移动端首页轮播图</option>
												<option value="coronavirus" <#if activity.type?if_exists=="coronavirus">selected</#if>>疫情期间退改政策</option>
												<option value="contact-us-global" <#if activity.type?if_exists=="contact-us-global">selected</#if>>全球联系信息</option>
												<option value="slhforheroes" <#if activity.type?if_exists=="slhforheroes">selected</#if>>抗疫英雄答谢</option>
												<option value="slh-for-heroes-terms-and-conditions" <#if activity.type?if_exists=="slh-for-heroes-terms-and-conditions">selected</#if>>致敬英雄条件条款</option>
												<option value="staysmallstaysafe" <#if activity.type?if_exists=="staysmallstaysafe">selected</#if>>住精品 享安心</option>
												<option value="seek-simplicity" <#if activity.type?if_exists=="seek-simplicity">selected</#if>>寻觅旅行的小确幸</option>
												<option value="seek-simplicity-form-rules" <#if activity.type?if_exists=="seek-simplicity-form-rules">selected</#if>>寻觅旅行的小确幸条款</option>
												<option value="owner-story" <#if activity.type?if_exists=="owner-story">selected</#if>>业主故事</option>
												<option value="china-destination" <#if activity.type?if_exists=="china-destination">selected</#if>>大中华旅行</option>
												<option value="international-destination" <#if activity.type?if_exists=="international-destination">selected</#if>>外国旅行</option>
												<option value="ads-one" <#if activity.type?if_exists=="ads-one">selected</#if>>推荐位一</option>
											</select>
										</div>
									</div>
									<div class="form-group">
										<label class="col-md-2 control-label" for="mtitle" style="font-size: 12px;">活动名称：</label>
										<div class="col-md-10">
											<input class="form-control" type="text" id="mtitle" name="mtitle" value="${activity.title?if_exists}"/>
										</div>
									</div>
									<div class="form-group">
										<label class="col-md-2 control-label" for="mname" style="font-size: 12px;">英文活动名称：</label>
										<div class="col-md-10">
											<input class="form-control" type="text" id="mname" name="mname" value="${activity.name?if_exists}"/>
										</div>
									</div>
									<div class="form-group">
										<label class="col-md-2 control-label" for="location" style="font-size: 12px;">地理位置：</label>
										<div class="col-md-10">
											<input class="form-control" type="text" id="location" name="location" value="${activity.location?if_exists}"/>
										</div>
									</div>
									<div class="form-group" style="margin: 0px 15px 8px 0px;">
										<label class="col-md-2 control-label" for="isShowInfo" style="font-size: 12px;">是否在页面展示文字信息：</label>
										<input type="radio" id="is_show_1" name="isShowInfo" value="1"
											   <#if activity.isShowInfo?if_exists=="1">checked="true"</#if>/><span style="font-size: 16px;">是</span>
										<input type="radio" id="is_show_0" name="isShowInfo" value="0"
											   <#if activity.isShowInfo?if_exists=="0">checked="true"</#if>/><span style="font-size: 16px;">否</span>
									</div>
									<#if chainCode?if_exists=="LW">
									<div class="form-group">
										<label class="col-md-2 control-label" for="pageTitle" style="font-size: 12px;">网页标题：</label>
										<div class="col-md-10">
											<input class="form-control" type="text" id="pageTitle" name="pageTitle" value="${activity.pageTitle?if_exists}"/>
										</div>
									</div>
									</#if>
									<div class="form-group">
										<label class="col-md-2 control-label" for="pageDescription" style="font-size: 12px;">网页描述：</label>
										<div class="col-md-10">
											<textarea class="form-control" rows="3" cols="40" id="pageDescription" name="pageDescription">${activity.pageDescription?if_exists}</textarea>
										</div>
									</div>
									<div class="form-group">
										<label class="col-md-2 control-label" for="keyWords" style="font-size: 12px;">keyWords：</label>
										<div class="col-md-10">
											<input class="form-control" type="text" id="keyWords" name="keyWords" value="${activity.keyWords?if_exists}"/>
										</div>
									</div>
									<div class="form-group">
										<label class="col-md-2 control-label" for="pageTitle" style="font-size: 12px;">pageTitle：</label>
										<div class="col-md-10">
											<input class="form-control" type="text" id="pageTitle" name="pageTitle" value="${activity.pageTitle?if_exists}"/>
										</div>
									</div>
									<div class="form-group">
										<label class="col-md-2 control-label" for="description" style="font-size: 12px;">description：</label>
										<div class="col-md-10">
											<input class="form-control" type="text" id="description" name="description" value="${activity.description?if_exists}"/>
										</div>
									</div>
									<div class="form-group">
										<label class="col-md-2 control-label" for="activityLink" style="font-size: 12px;">activityLink：</label>
										<div class="col-md-10">
											<input class="form-control" type="text" id="activityLink" name="activityLink" value="${activity.activityLink?if_exists}"/>
										</div>
									</div>
									<div class="form-group">
										<label class="col-md-2 control-label" for="iataNumber" style="font-size: 12px;">特殊价格码rate：</label>
										<div class="col-md-10">
											<input class="form-control" type="text" id="iataNumber" name="iataNumber" value="${activity.iataNumber?if_exists}"/>
										</div>
									</div>

									<div class="form-group">
										<label class="col-md-2 control-label" for="rateAccessCode" style="font-size: 12px;">AccessCode：</label>
										<div class="col-md-10">
											<input class="form-control" type="text" id="rateAccessCode" name="rateAccessCode" value="${activity.rateAccessCode?if_exists}"/>
										</div>
									</div>
									<div class="form-group" style="margin: 0px 15px 8px 0px;">
										<label class="col-md-2 control-label" for="status" style="font-size: 12px;">状态：</label>
										<input type="radio" id="status_1" name="status" value="1"
											<#if activity.status?if_exists=="1">checked="true"</#if>/><span style="font-size: 16px;">上线</span> 
										<input type="radio" id="status_0" name="status" value="0"
											<#if activity.status?if_exists=="0">checked="true"</#if>/><span style="font-size: 16px;">下线</span>
									</div>
									<#if chainCode?if_exists=="LW">
									<div class="form-group">
										<label class="col-md-2 control-label" for="activeSite" style="font-size: 12px;">网址：</label>
										<div class="col-md-10">
											<label class="control-label col-md-2" for="">http://www.lhw.cn/offers/</label>
											<input class="form-control col-md-4" type="text" style="width: 600px" onblur="getSite();" id="activeSite" name="activeSite" value="${activity.activeSite?if_exists}"/>
										</div>
									</div>
									</#if>
									<#if chainCode!="LW">
									<div class="form-group" id="link_type_div">
										<label class="col-md-2 control-label" for="type" style="font-size: 16px;">链接类型：</label>
										<div class="col-md-10">
											<select class="form-control" id="linkType" name="linkType">
												<option value="internal"<#if activity.linkType?if_exists?if_exists=="internal"> selected="selected"</#if>>内部链接</option>
												<option value="external"<#if activity.linkType?if_exists?if_exists=="external"> selected="selected"</#if>>外部链接</option>
											</select>
										</div>
									</div>
									<div class="form-group" id="link_div">
										<label class="col-md-2 control-label" for="title" style="font-size: 16px;">链接地址：</label>
										<div class="col-md-10">
											<input class="form-control col-md-8" type="text" id="link" name="link" value="${activity.link?if_exists}" />
											<input type="button" class="col-md-2 btn btn-success" onclick="javasript:downloadWechat()" value="下载" />
											请输入完整的网址，如：http://www.baidu.com/
										</div>
									</div>
									<div class="form-group" >
										<label class="control-label col-md-2" for="publishTime">发布时间：</label>
										<div class="col-md-10">
											<input class="form-control col-md-4 form_datetime" type="text" style="width: 140px" id="publishTime" name="publishTime"  data-date-format="yyyy-mm-dd hh:ii" readonly="readonly" value="<#if activity.publishTime??>${activity.publishTime?string("yyyy-MM-dd HH:mm")}</#if>" placeholder="" />
										</div>
									</div>
									</#if>
									
									<div class="form-group">
										<label class="col-md-2 control-label" for="banner" style="font-size: 12px; margin-bottom: 20px;">图片：</label>
										<input type="hidden" id="banner" name="banner" value="${activity.banner?if_exists}"/>
										<input type="hidden" id="bannerTra" name="bannerTra" value="${activity.bannerTra?if_exists}"/>
										<div class="col-md-10">
											<div class="col-md-10">
												<div class="col-md-2" style="padding-left: 0px; margin-bottom: 10px;">
													<input type="button" class="btn btn-success" onclick="javasript:selectActivityImage()" value="选择简体图片" />
												</div>
												<div>
													<img class="col-md-12" id="bannerImage" name="bannerImage" src="${imageUrl}/${activity.banner?if_exists}"/>
													<a class="col-md-2" href="javascript:void(0);" onclick="removeActivityBanner('${activity.id?if_exists}', this);">删除图片</a>
												</div>
											</div>
											<div class="col-md-10" style="margin-top: 20px;">
												<div class="col-md-2" style="padding-left: 0px; margin-bottom: 10px;">
													<input type="button" class="btn btn-success" onclick="javasript:selectActivityImage('tra')" value="选择繁体图片" />
												</div>
												<div>
													<img class="col-md-12" id="bannerImageTra" name="bannerImageTra" src="${imageUrl}/${activity.bannerTra?if_exists}"/>
													<a class="col-md-2" href="javascript:void(0);" onclick="removeActivityBanner('${activity.id?if_exists}', this, 'tra');">删除图片</a>
												</div>
											</div>
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-2 control-label" for="" style="font-size: 12px;">PC版活动内容：</label>
									</div>
									
									<!-- <fieldset>
										<div class="form-group">
											<div class="col-md-12">
												<div id="itemContent" style="height:100px;">${activity.content?if_exists}</div>
												</div>
										</div>
									</fieldset> --> 
									<div class="form-group">
											<div class="col-md-12">
												<!-- 加载编辑器的容器 -->
												<script id="itemContent" name="itemContent" type="text/plain">${activity.content?if_exists}</script>
											</div>
										</div>
									<#if chainCode?if_exists=="LW">
									<div class="form-group">
										<label class="col-md-2 control-label" for="" style="font-size: 12px;">Mobile版活动内容：</label>
									</div>
									<fieldset>
										<div class="form-group">
											<div class="col-md-12">
												<div id="itemContentMobile" style="height:100px;">${activity.contentMobile?if_exists}</div>
											</div>
										</div>
									</fieldset>
									</#if>
									<div class="form-group">
										<label class="col-md-2 control-label" for="sequence" style="font-size: 12px;">排序：</label>
										<div class="col-md-10">
											<input class="form-control" type="number" id="sequence" name="sequence" value="${activity.sequence?if_exists}"/>
										</div>
									</div>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<a class="btn btn-default" style="margin-right: 12px;" href="${basePath}/activity/list" >返回</a>
												<input type="submit" class="btn btn-primary" value="保存" style="margin-right: 12px;" id="addActivity"/>
												<#if chainCode?if_exists=="LW">
												<input type="button" class="btn btn-warning" value="预览PC版" id="previewBtn"/>
												<input type="button" class="btn btn-warning" value="预览Mobile版" id="previewBtnMobile"/>
												</#if>
											</div>
										</div>
									</div>
								</form>
							</div>
							<#if chainCode!="LW">
							<div class="row">
									<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
										<div class="jarviswidget jarviswidget-color-darken" id="travel-item-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
											<header>
												<span class="widget-icon">
													<i class="fa fa-table"></i>
												</span>
												<h2>促销活动酒店设置</h2>
											</header>
											<div>
												<div class="jarviswidget-editbox"></div>
												<div class="widget-body">
													<table id="dt_basic" class="table table-striped table-bordered table-hover overseaGrid">
														<thead>
															<tr>
																<th>国家</th>
																<th>城市</th>
																<th>酒店代码</th>
																<th>酒店名</th>
																<th width="54">状态</th>
																<th>排序</th>
																<th>操作</th>
															</tr>
														</thead>
														<tbody>
															<#list activityHotels?if_exists as activityHotel>
															<tr class="small">
																<td style="display:none">${activityHotel.id?if_exists}</td>
																<td>${activityHotel.hotelCountry?if_exists}</td>
																<td>${activityHotel.hotelCity?if_exists}</td>
																<td>${activityHotel.hotelCode?if_exists}</td>
																<td>${activityHotel.hotelName?if_exists}</td>
																<td><#if (activityHotel.hotel.onlineStatus)?if_exists="online">
																	<span style="color: green;">上线</span>
																	</#if>
																	<#if (activityHotel.hotel.onlineStatus)?if_exists="offline">
																	<span style="color: red;">下线</span>
																	</#if>
																	<#if (activityHotel.hotel.onlineStatus)?if_exists="new">
																	<span style="color: red;">新增</span>
																	</#if>
																</td>
							                                    <td style="display:none">${activityHotel.cover?if_exists}</td>
							                                    <td width="140" style="text-align: center;">
							                                    	<input type="hidden" name="ordernum" class="form-control" style="" data-id="${activityHotel.id}" value="${activityHotel.orderNum?if_exists}" data-img="${imageUrl}/${activityHotel.cover?if_exists}" data-img-path="${activityHotel.cover?if_exists}"/>
							                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
							                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
							                                    </td>
							                                    <td width="100" style="text-align: center;">
							                                    		<#if chainCode?if_exists=="LW">
							                                        <a class="btn btn-info viewBanner" data-img="${imageUrl}/${(activityHotel.cover)?if_exists}">查看封面</a>
							                                        <a class="btn btn-warning" href="${basePath}/active/hotel/info?id=${activityHotel.id}" style="margin: 0px 12px;">修改</a>
							                                        </#if>
							                                        <a class="btn btn-danger" href="" ids="del" data-id="${activityHotel.id}">删除</a>
							                                    </td>
															</tr>
															</#list>
														</tbody>
													</table>
													<div style="margin-top: 12px;">
														<input type="button" id="addBtn" class="btn btn-primary" value="新增推荐酒店" style="margin-right: 12px;" />
														<input type="button" id="uploadExcelBtn" class="btn btn-primary" value="excel导入酒店" onclick="showUploadDiv()" style="margin-right: 12px;" />
														<#if chainCode?if_exists=="LW">
														<input type="button" class="btn bg-color-pink txt-color-white" value="保存排序" id="saveOrderBtn"/>
														</#if>
													</div>
												</div>
											</div>
										</div>
									</article>
								</div>
							</#if>	
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>

<div class="modal fade" id="uploadExcelModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content" style="width: 840px;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">上传文件</h4>
			</div>
			<div class="modal-body" style="padding-bottom: 8px;">
				<form id="uploadExcelForm" class="form-horizontal" method="post" action="" enctype="multipart/form-data" target="uploadframe">
					<input type="hidden" id="activityId" name="activityId" value="${activity.id?if_exists}"/>
					<fieldset>
						<div class="form-group">
							<div class="col-md-10">
								<input class="form-control" type="file" id="file" name="file" />
							</div>
							<div class="col-md-2">
								<input type="button" onclick="uploadExcel()" class="btn btn-success" value="上传" />
							</div>
						</div>
						<div class="form-group">
							<div class="superbox col-sm-12">xls和xlsx文件均可，请从第一个sheet的第一列第一行开始，每行一个酒店code</div>
						</div>
					</fieldset>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
			</div>
		</div>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
var type ='active-banner';
var ba;
$(document).ready(function() {
	$('#addBtn').on('click', function() {
		var aid = '${activity.id?if_exists}';
		if(aid == '') {
			alert('请先保存活动信息');
			return;
		}
		window.location.href = '${basePath}/activity/hotel/info?activityId=${activity.id?if_exists}';
	});
	
	$('a[ids=del]').click(delHotel);
	
	$('#availStartDate').datepicker();
	$('#availEndDate').datepicker();
	$('#publishTime').datetimepicker({
		format: 'yyyy-mm-dd hh:ii',
	});
	
	var $selectBannerModal = $('#selectBannerModal');
	var selectBanner = function() {
		ba = "active";
		var params = {};
		params.relateType = type;
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val(type);
		$selectBannerModal.find('input[name=relateCode]').val($('#itemCode').val());
		$selectBannerModal.find('input[name=format]').val('1600*558c.jpg');
		$selectBannerModal.find('#imgsize').text('1600 x 558');
		$selectBannerModal.modal();
	};
	$selectBannerModal.find('.superbox img').on('click', function() {
		var $obj = $(this);
		$itemBanner.val('${imageUrl}/'+$obj.attr('src'));
		$selectBannerModal.modal('hide');
	});
	var $itemBanner = $('#banner');
	$('#selectBannerBtn').on('click', selectBanner);
	$itemBanner.on('click', selectBanner);
	
	//var $itemContent = $('#itemContent');
	//$itemContent.summernote({height: 180});
	
	var $itemContentMobile = $('#itemContentMobile');
	$itemContentMobile.summernote({height: 180});
	
	$("#addActivity").click(addActivity);
	
	var $checkCoverModal = $('#checkCoverModal');
	$('#saveOrderBtn').on('click', function() {
		var aid = '${activity.id?if_exists}';
		if(aid == '') {
			alert('请先保存活动信息');
			return;
		}
		saveHotelOrder();
	});
	
	function saveHotelOrder() {
			var items = '';
			var orderIndex = 1;
			$('input[type=hidden][name=ordernum]').each(function() {
				var $obj = $(this);
				var id = $obj.attr('data-id');
				//var value = $obj.val();
				items += id+':'+orderIndex+';';
				orderIndex ++;
			});
			var params = {};
			params.orders = items;
			$.post('${basePath}/activity/hotel/order', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/toLogin';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/activity/info?id=${activity.id?if_exists}';
				}
			}, 'json');
	}
	$checkCoverModal.find('#confirmBtn').on('click', function() {
		var _cover = $checkCoverModal.find('.confirmCover').attr('data-img-path');
		var _code = $checkCoverModal.find('input[name=relateCode]').val();
		var _url = '${basePath}/activity/hotel/cover/update';
		var _params = {};
		_params.cover = _cover;
		_params.id = _code;
		$.post(_url, _params, function(data) {
			if(data.code==1){
				saveHotelOrder();
			}else{
				alert("修改失败");
			}
		}, 'json');
	});
	$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
	
	$('#previewBtn').click(function() {
		$('#precontent').val($("#itemContent").code());
		$('#precontentMobile').val($("#itemContentMobile").code());
		if(isNull(false)){
			var params = $('#editForm').serializeArray();
			$.post('${basePath}/activity/preview', params, function(data) {
				if(data.code == '1') {
					window.open('${activityPreviewUrl}'+data.desc);
				} else {
					alert(data.desc);
				}
			}, 'json');
		}
	});
	
	$('#previewBtnMobile').click(function() {
		$('#precontentMobile').val($("#itemContentMobile").code());
		$('#precontent').val($("#itemContent").code());
		if(isNull(false)){
			var params = $('#editForm').serializeArray();
			$.post('${basePath}/activity/preview', params, function(data) {
				if(data.code == '1') {
					window.open('${activityPreviewMobileUrl}'+data.desc);
				} else {
					alert(data.desc);
				}
			}, 'json');
		}
	});
});

function isNull(save){
	var site = $("#activeSite").val();
	site = $.trim(site) ;
	if(!site){
		site = (new Date()).getMilliseconds();
		//alert("网址不能为空");
		//return false;
	}
	if(save==true){
		var contentPc = $("#itemContent").code(), contentMobile = $("#itemContentMobile").code();
		contentPc = $.trim(contentPc);
		contentMobile = $.trim(contentMobile);
		if(!contentPc || !contentMobile){
			//alert("PC版和移动版活动内容都不能为空");
			//return false;
		}
	}else{
		var content = $('#precontent').val();
		content = $.trim(content);
		if(!content){
			//alert("活动内容不能为空");
			//return false;
		}
	}
	return true;
}

function getSite(){
	var site = $("#activeSite").val();
	site = $.trim(site) ;
	if(!site){
		site = (new Date()).getMilliseconds();
		//alert("网址不能为空");
		//return false;
	}
	$.ajax({
		async : false,
		type : "POST",
		dataType:"json",
		data:{
			activeSite:site
		},
		url :  "${basePath}/activity/getSite",
		error : function(data) {
			return false;
		},
		success : function(data) {
			if(data.code == 1){
				alert("该网址已使用，请更换网址");
				return false;
			}else{
				return false;
			}
		}
	});
	return false;
}

function delHotel(){
	var confirmResult = confirm("是否删除");
	if(confirmResult == true){
		$.ajax({
			async : false,
			type : "POST",
			dataType:"json",
			data:{
				id:$(this).attr("data-id")
			},
			url :  "${basePath}/activity/hotel/del",
			error : function(data) {
				return "执行失败";
			},
			success : function(data) {
				if(data.code==1){
					alert(data.desc);
					window.location.href = '${basePath}/activity/info?id=${activity.id?if_exists}';
					return false;
				}else{
					alert(data.desc);
					return false;
				}
			}
		});
		return false;
	}
	return false;
}

function selectImage(url) {

	if(ba=="hotel"){
		$("#checkCoverModal").find('.confirmCover').attr('data-img-path',url);
	}else{
		$('#banner').val(url);
		$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
		$('#selectBannerModal').modal('hide');
	}
}

function addActivity(){
	var data = {
			pageTitle:$("#pageTitle").val(),
			activeSite:$("#activeSite").val(),
			pageDescription:$("#pageDescription").val(),
			link:$("#link").val(),
			linkType:$("#linkType").val(),
			keyWords:$("#keyWords").val(),
			pageTitle:$("#pageTitle").val(),
			description:$("#description").val(),
			id:$("#id").val(),
			chainCode:$("#chainCode").val(),
			type:$("#type").val(),
			title:$("#mtitle").val(),
			name:$("#mname").val(),
			location:$("#location").val(),
			content:ue.getContent(),//$("#itemContent").code(),
			contentMobile:$("#itemContentMobile").code(),
			activityLink:$("#activityLink").val(),
			iataNumber:$("#iataNumber").val(),
			rateAccessCode:$("#rateAccessCode").val(),
			availStartDate:$("#availStartDate").val(),
			availEndDate:$("#availEndDate").val(),
			banner:$("#banner").val(),
			bannerTra:$("#bannerTra").val(),
			publishTime:$("#publishTime").val(),
			status:$("input:radio[name='status']:checked").val(),
			isShowInfo:$("input:radio[name='isShowInfo']:checked").val(),
			sequence:$("#sequence").val(),
		};
	if(isNull(true)){
		$.ajax({
		async : false,
		type : "POST",
		dataType : "json",
		data : data,
		url :  "${basePath}/activity/add",
		error : function(data) {
			return "执行失败";
		},
		success : function(data) {
			if(data.code==1){
				alert(data.desc);
				window.location="${basePath}/activity/list";
			}else{
				alert(data.desc);
			}
		}
	});
	}
	return false;
}

var $selectHotelImageModal = $('#selectHotelImageModal');

function selectActivityImage(type) {
	var params = {};
	if(type == 'tra') {
		$('#hotelImageUploadForm').attr('action', '${basePath}/image/hotel/upload?type=tra');
	}
	$selectHotelImageModal.find('.modal-title').text('上传活动图片');
	$selectHotelImageModal.modal();
}

function hotelImageUploadResult(fcode, fname, type) {
	if(fcode == '1') {
		alert('没有要上传的图片！');
		return;
	}
	if(fcode == '2') {
		alert('上传的图片不能超过2M');
		return;
	}
	if(fcode == '3') {
		alert('只允许上传jpg和png格式的图片！');
		return;
	}
	if(fname == '') {
		alert('没有要上传的图片！');
		return;
	}
	alert('上传成功！');
	$selectHotelImageModal.modal('hide');
	var url = '${imageUrl}/'+fname;
	if(type == 'tra') {
		$('#bannerImageTra').attr('src', url);
		$('#bannerTra').val(fname);
	} else {
		$('#bannerImage').attr('src', url);
		$('#banner').val(fname);
	}
}

/**
 * 删除活动的banner图片
 * @param id: 活动ID
 * @param div：删除按钮所在标签ID，使用时把删除按钮和图片放到一个div里，后台删除成功会自动删除前台的图片div
 * @returns
 */
function removeActivityBanner(id, div, type) {
	if(confirm('确定要删除该图片吗？')) {
		if(banner == '') {
			alert('未发现待删除图片！');
			return;
		}
		var params = {};
		params.id = id;
		if(type == 'tra') {
			params.type = type;
		}
		if(id>0){
			$.post('${basePath}/activity/removeBanner', params, function(data) {
				if(data.code == '1') {
					alert('删除成功！');
					$("#editForm #banner").val("");
					$(div).parent().remove();
					$selectHotelImageModal.find('#file')[0].value="";
				} else {
					alert('删除失败: '+data.desc);
					return false;
				}
			}, 'json');
		}else{
			alert('删除成功！');
			$("#editForm #banner").val("");
			$(div).parent().remove();
			$selectHotelImageModal.find('#file')[0].value="";
		}
		
	}
}

//实例化编辑器
var ue = UE.getEditor('itemContent');
ue.ready(function() {
    //设置编辑器的内容
   // editor.setContent();
    //获取html内容
    //var html = ue.getContent();
    //获取纯文本内容
   // var txt = ue.getContentTxt();
});

function downloadWechat(){
	$.ajax({
		async : false,
		type : "POST",
		dataType:"json",
		data:{
			pageTitle:$("#pageTitle").val(),
			activeSite:$("#activeSite").val(),
			pageDescription:$("#pageDescription").val(),
			link:$("#link").val(),
			linkType:$("#linkType").val(),
			keyWords:$("#keyWords").val(),
			id:$("#id").val(),
			chainCode:$("#chainCode").val(),
			type:$("#type").val(),
			title:$("#mtitle").val(),
			content:ue.getContent(),//$("#itemContent").code(),
			contentMobile:$("#itemContentMobile").code(),
			name:$("#name").val(),
			availStartDate:$("#availStartDate").val(),
			availEndDate:$("#availEndDate").val(),
			banner:$("#banner").val(),
			publishTime:$("#publishTime").val(),
			status:$("input:radio[name='status']:checked").val(),
		},
		url :  "${basePath}/activity/downloadWechat",
		error : function(data) {
			return "执行失败";
		},
		success : function(data) {
			if(data.errorCode==1){
				alert(data.errorMsg);
				window.location="${basePath}/activity/info?id="+data.data;
			}else{
				alert(data.desc);
			}
		}
	});
}

function showUploadDiv(){
	$("#uploadExcelModal").modal();
}

function uploadExcel(){
	var url = '${rc.getContextPath()}/activity/uploadExcel';
	var formData = new FormData($("#uploadExcelForm")[0]);
    $.ajax({  
         url: url ,
         type: 'POST',  
         data: formData,  
         async: false,  
         cache: false,//上传文件不需要缓存。
         contentType: false,//因为是由<form>表单构造的FormData对象，且已经声明了属性enctype="multipart/form-data"，所以这里设置为false
         processData: false,//因为data值是FormData对象，不需要对数据做处理
		 dataType:"json",
         success: function (data) {  
             alert(data.desc);
             console.log(JSON.stringify(data));
             if(data.code=="ok"){
            	$("#uploadExcelModal").modal('hide');
            	$("#uploadExcelForm file").val("");
            	location.reload(true);
             }
         },  
         error: function (data) {
             console.log(JSON.stringify(data));
        	 console.log(data.status+" "+data.statusText);
        	 console.log(data.responseText); 
             alert(data.status+" "+data.statusText+" "+data.responseText);  
         }  
    }); 
}
</script>
</body>
</html>