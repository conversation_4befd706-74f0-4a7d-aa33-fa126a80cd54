<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<div id="main" role="main" style="margin: 16px 0px 0px 8px;">
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row" style="margin: -12px -38px -72px -20px">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<div class="widget-body no-padding" style="border-top: 1px solid #ccc;">
							<div class="widget-body-toolbar">
									<form id="queryForm" class="form-inline" method="post" action="${basePath}/activity/select">
										<input type="hidden" id="start" name="start" value="${page.currentPage}" />
										<div class="form-group" style="margin: 0px 15px 8px 0px;">
											<label class="control-label" for="keyword">活动名称：</label>
						                	<input type="text" class="form-control"  id="title" name="title" value="${tl?if_exists}" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<input type="submit" class="btn btn-primary" value="查询" id="searchActiveB" style="margin-right: 8px;" />
					                	</div>
									</form>
								</div>
								<table id="dt_basic" class="table table-striped table-bordered table-hover">
									<thead>
										<tr>
											<th>活动名称</th>
										    <th width="100">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list page.datas as date>
										<tr class="small">
											<td style="display:none">${date.id}</td>
											<td>${date.title}</td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning selectBtn" href="" dt-id="${date.id}" dt-title="${date.title}" >选择</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
							<#include "/common/page.ftl" />
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script>
$(document).ready(function(){
	$('a.selectBtn').click(selectActive);
	$('table tr.small').dblclick(function(){
		var m = $(this).find("a.selectBtn");
		selectActive(m,1);
	});
});
function selectActive(date,flag){
	if(flag!=1){
		date = $(this);
	}
	parent.selectActiveInfo(date.attr('dt-id'),date.attr('dt-title'));
	return false;
}


function toPage(pageNo) {
	$('#start').val(pageNo);
	$('#queryForm').submit();
}
</script>
</body>
</html>