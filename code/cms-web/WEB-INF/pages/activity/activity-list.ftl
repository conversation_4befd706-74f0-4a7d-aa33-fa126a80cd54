<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>活动管理</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="active-list-0" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>活动列表</h2>
						</header>
						<div>
							<div class="widget-body no-padding">
								<div class="widget-body-toolbar">
									<form id="queryForm" class="form-inline" method="post" action="${basePath}/activity/list">
										<input type="hidden" id="research" name="research" value="true" /> 
										<input type="hidden" id="start" name="start" value="${page.currentPage}" />
									<#if chainCode!="LW">
									<div class="form-group">
											<div class="col-md-10">
												<select class="col-xs-14 col-sm-14" id="type" name="type">
													<option value="">请选择类型...</option>
													<option value="experience" <#if type?if_exists=="experience">selected="selected"</#if>>主题活动</option>
													<option value="weChat" <#if type?if_exists=="weChat">selected="selected"</#if>>微信精选</option>
													<option value="promotion" <#if type?if_exists=="promotion">selected</#if>>优惠活动</option>
													<!-- <option value="activity" <#if type?if_exists=="activity">selected</#if>>活动</option> -->
													<option value="news" <#if type?if_exists=="news">selected</#if>>新闻</option>
													<option value="about-us" <#if type?if_exists=="about-us">selected</#if>>关于我们</option>
													<option value="social-media" <#if type?if_exists=="social-media">selected</#if>>媒体对接</option>
													<option value="contact-us" <#if type?if_exists=="contact-us">selected</#if>>联系我们</option>
													<option value="travel-agents" <#if type?if_exists=="travel-agents">selected</#if>>旅行社</option>
													<option value="index-swiper-pc" <#if type?if_exists=="index-swiper-pc">selected</#if>>PC端首页轮播图</option>
													<option value="index-swiper-m" <#if type?if_exists=="index-swiper-m">selected</#if>>移动端首页轮播图</option>
													<option value="coronavirus" <#if type?if_exists=="coronavirus">selected</#if>>疫情期间退改政策</option>
													<option value="contact-us-global" <#if type?if_exists=="contact-us-global">selected</#if>>全球联系信息</option>
													<option value="slhforheroes" <#if type?if_exists=="slhforheroes">selected</#if>>抗疫英雄答谢</option>
													<option value="slh-for-heroes-terms-and-conditions" <#if type?if_exists=="slh-for-heroes-terms-and-conditions">selected</#if>>致敬英雄条件条款</option>
													<option value="staysmallstaysafe" <#if type?if_exists=="staysmallstaysafe">selected</#if>>住精品 享安心</option>
													<option value="seek-simplicity" <#if type?if_exists=="seek-simplicity">selected</#if>>寻觅旅行的小确幸</option>
													<option value="seek-simplicity-form-rules" <#if type?if_exists=="seek-simplicity-form-rules">selected</#if>>寻觅旅行的小确幸条款</option>
													<option value="owner-story" <#if type?if_exists=="owner-story">selected</#if>>业主故事</option>
													<option value="china-destination" <#if type?if_exists=="china-destination">selected</#if>>大中华旅行</option>
													<option value="international-destination" <#if type?if_exists=="international-destination">selected</#if>>外国旅行</option>
													<option value="ads-one" <#if type?if_exists=="ads-one">selected</#if>>推荐位一</option>
												</select>
											</div>
										</div>
										</#if>
										<div class="form-group" style="margin: 0px 15px 8px 0px;">
											<!--<label class="control-label" for="keyword">活动名称：</label> -->
						                	<input type="text" class="form-control" id="title" name="title" value="${tl?if_exists}" placeholder="活动名称" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
											<!--<label class="control-label" for="keyword">操作人员：</label> -->
						                	<input type="text" class="form-control" id="name" name="name" value="${name?if_exists}" placeholder="操作人员" />
					                	</div>
					                	<#if chainCode?if_exists=="LW">
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
											<!--<label class="control-label" for="keyword">：</label> -->
											<input class="form-control col-md-4" type="text" style="width: 120px" class="form-control" id="startDate" name="startDate"  data-date-format="yyyy-mm-dd" readonly="readonly" value="<#if startDate??>${startDate?string("yyyy-MM-dd")}</#if>" placeholder="有效开始日期" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
											<!--<label class="control-label" for="keyword">活动名称：</label> -->
							                <input class="form-control col-md-4" type="text" style="width: 120px" class="form-control" id="endDate" name="endDate"  data-date-format="yyyy-mm-dd" readonly="readonly" value="<#if endDate??>${endDate?string("yyyy-MM-dd")?if_exists}</#if>" placeholder="有效结束日期" />
					                	</div>
					                	</#if>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<select class="form-control" id="status" name="status">
					                			<option value="1" "<#if status?if_exists=='1'> selected="selected"</#if>>上线</option>
					                			<option value="0" "<#if status?if_exists=='0'> selected="selected"</#if>>下线</option>
					                			<!-- <option value="">全部</option> -->
					                		</select>
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<input type="button" class="btn btn-primary" value="查询" id="searchActiveB" style="margin-right: 8px;" onclick="toPage(1)" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<a href="${basePath}/activity/info"><input type="button" class="btn btn-success" value="创建活动"  id="addActiveB" style="margin-right: 8px;" /></a>
					                	</div>
									</form>
								</div>
								<table id="dt_basic" class="table table-striped table-bordered table-hover">
									<thead>
										<tr>
											<th>类型</th>
											<th>活动名称</th>
											<th>操作人员</th>
											<#if chainCode?if_exists=="LW">
											<th width="90">有效开始日期</th>
											<th width="90">有效结束日期</th>
											</#if>
										    <th>状态</th>
										    <th width="220">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list page.datas as data>
										<tr class="small">
											<td style="display:none">${data.id?if_exists}</td>
											<td>
												<#if data.type?if_exists=="experience">主题活动</#if>
												<#if data.type?if_exists=="weChat">微信精选</#if>
												<#if data.type?if_exists=="promotion">优惠活动</#if>
												<#if data.type?if_exists=="activity">活动</#if>
												<#if data.type?if_exists=="news">新闻</#if>
												<#if data.type?if_exists=="about-us">关于我们</#if>
												<#if data.type?if_exists=="social-media">媒体对接</#if>
												<#if data.type?if_exists=="contact-us">联系我们</#if>
												<#if data.type?if_exists=="travel-agents">旅行社</#if>
												<#if data.type?if_exists=="index-swiper-pc">PC端首页轮播图</#if>
												<#if data.type?if_exists=="index-swiper-m">移动端首页轮播图</#if>
												<#if data.type?if_exists=="coronavirus">疫情期间退改政策</#if>
												<#if data.type?if_exists=="contact-us-global">全球联系信息</#if>
												<#if data.type?if_exists=="slhforheroes">抗疫英雄答谢</#if>
												<#if data.type?if_exists=="slh-for-heroes-terms-and-conditions">致敬英雄条件条款</#if>
												<#if data.type?if_exists=="staysmallstaysafe">住精品 享安心</#if>
												<#if data.type?if_exists=="seek-simplicity">寻觅旅行的小确幸</#if>
												<#if data.type?if_exists=="seek-simplicity-form-rules">寻觅旅行的小确幸条款</#if>
												<#if data.type?if_exists=="owner-story">业主故事</#if>
												<#if data.type?if_exists=="china-destination">大中华旅行</#if>
												<#if data.type?if_exists=="international-destination">外国旅行</#if>
												<#if data.type?if_exists=="ads-one">推荐位一</#if>
											</td>
											<td>${data.title?if_exists}</td>
											<td>${data.name?if_exists}</td>
											<#if chainCode?if_exists=="LW">
											<td><#if data.availStartDate??>${data.availStartDate?string("yyyy-MM-dd")}</#if></td>
											<td><#if data.availEndDate??>${data.availEndDate?string("yyyy-MM-dd")}</#if></td>
											</#if>
											<td><#if data.status?if_exists=="1"><span
													style="color: green;">上线</span></#if>
												<#if data.status?if_exists=="0"><span
													style="color: red;">下线</span></#if>
												</td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning" href="${basePath}/activity/info?id=${data.id}" style="margin-right: 12px;">修改</a>
		                                        <a class="btn btn-danger" href="javascript:void(0);" onclick="offline(${data.id?if_exists})" style="margin-right: 12px;">下线</a>
		                                        <a class="btn btn-danger" href="javascript:void(0);" onclick="online(${data.id?if_exists})">上线</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<#include "/common/page.ftl" />
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script>
$(document).ready(function(){
	$('#startDate').datepicker();
	$('#endDate').datepicker();
	//$("a[ids='del']").click(del);
});

function offline(id){
	if(confirm("是否下线")){
		$.ajax({
				async : false,
				type : "POST",
				dataType:"json",
				data:{
					id:id
				},
				url :  "${basePath}/activity/offline",
				error : function(data) {
					return "执行失败";
				},
				success : function(data) {
					if(data.code==1){
						alert(data.desc);
						toPage(0);
						return false;
					}else{
						alert(data.desc);
						return false;
					}
				}
			});
			return false;
	}
}

function online(id){
	if(confirm("是否上线")){
		$.ajax({
				async : false,
				type : "POST",
				dataType:"json",
				data:{
					id:id
				},
				url :  "${basePath}/activity/online",
				error : function(data) {
					return "执行失败";
				},
				success : function(data) {
					if(data.code==1){
						alert(data.desc);
						toPage(0);
						return false;
					}else{
						alert(data.desc);
						return false;
					}
				}
			});
			return false;
	}
}

function toPage(pageNo) {
	$('#start').val(pageNo);
	$('#queryForm').submit();
}
</script>
</body>
</html>