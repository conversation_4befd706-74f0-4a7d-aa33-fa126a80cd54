<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="country-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>修改国家信息</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body" style="margin-left:-30px;">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/country/updateCountry">
									<fieldset >
										<input class="form-control" type="text" id="chainCode" name="chainCode" value="${country.chainCode?if_exists}" style='display:none'/>
										<input class="form-control" type="text" id="countryCode" name="countryCode" value="${country.countryCode?if_exists}" style='display:none'/>
										<div class="form-group">
											<label class="col-md-2 control-label" for="countryName" style="font-size: 16px;" check-type="required" required-message="名称不能为空。" >国家中文名</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="countryName" value="${country.countryName?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="countryNameEn" style="font-size: 16px;">国家英文名</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="countryNameEn" value="${country.countryNameEn?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="countryNamePinyin" style="font-size: 16px;">国家名拼音</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="countryNamePinyin" value="${country.countryNamePinyin?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="countryLink" style="font-size: 16px;">countryLink</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="countryLink" value="${country.countryLink?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="continentNameEn" style="font-size: 16px;">所属大洲</label>
											<div class="col-md-10">
												<select class="form-control" id="continentNameEn" name="continentNameEn">
						                			<option value="">请选择大洲${country.continentNameEn}</option>
						                			<option value="Africa"<#if country.continentNameEn=="Africa"> selected="selected"</#if>>Africa</option>
						                			<option value="Americas"<#if country.continentNameEn=="Americas"> selected="selected"</#if>>Americas</option>
						                			<option value="Australasia"<#if country.continentNameEn=="Australasia"> selected="selected"</#if>>Australasia</option>
						                			<option value="Asia"<#if country.continentNameEn=="Asia"> selected="selected"</#if>>Asia</option>
						                			<option value="Caribbean"<#if country.continentNameEn=="Caribbean"> selected="selected"</#if>>Caribbean</option>
						                			<option value="Europe"<#if country.continentNameEn=="Europe"> selected="selected"</#if>>Europe</option>
						                			<option value="Indian Ocean"<#if country.continentNameEn=="Indian Ocean"> selected="selected"</#if>>Indian Ocean</option>
						                			<option value="Middle East"<#if country.continentNameEn=="Middle East"> selected="selected"</#if>>Middle East</option>
						                		</select>
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="banner" style="font-size: 16px;">国家图片</label>
											<img style="height:400px;" src="${country.imageUrl?if_exists}"/>
											<!-- <div class="col-md-10">
												<input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${country.coverUrl?if_exists}" value="查看" />
											</div> -->
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="brief" style="font-size: 16px;">brief</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="brief" value="${country.brief?if_exists}"/>
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="brief" style="font-size: 16px;">briefEn</label>
											<div class="col-md-10">${country.briefEn?if_exists}</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="detail" style="font-size: 16px;">detail</label>
											<div class="col-md-10">
												<textarea class="form-control" id="detail" name="detail" rows="4">${country.detail?if_exists}</textarea>
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="brief" style="font-size: 16px;">detailEn</label>
											<div class="col-md-10">${country.detailEn?if_exists}</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="introducing" style="font-size: 16px;">introducing</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="introducing" value="${country.introducing?if_exists}"/>
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="brief" style="font-size: 16px;">introducingEn</label>
											<div class="col-md-10">${country.introducingEn?if_exists}</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="cover" style="font-size: 16px;">状态</label>
											<div class="col-md-10">
												<input type="radio" id="is_online_1" name="status" value="online" <#if country.status=='online'>checked="true"</#if>/><span style="font-size: 16px;">上线</span>
												<input type="radio" id="is_online_0" name="status" value="offline" <#if country.status=='offline'>checked="true"</#if>/><span style="font-size: 16px;">下线</span>
												<input type="radio" id="is_online_2" name="status" value="new" <#if country.status=='new'>checked="true"</#if>/><span style="font-size: 16px;">新增</span>
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="introducing" style="font-size: 16px;">countryUrl</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="countryUrl" value="${country.countryUrl?if_exists}"/>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="button" class="btn btn-primary" value="从官网下载国家信息" id="downloadCountryInfo" style="margin-left: 12px;float:left;" />
												<input type="button" class="btn btn-primary" value="保存" id="itemAdd" style="margin-right: 12px;" />
												<input type="button" class="btn btn-default" value="返回" onclick="javascirpt:returnToCountryList();" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
							<#if chainCode!="LW">
							<div class="row">
									<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
										<div class="jarviswidget jarviswidget-color-darken" id="travel-item-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
											<header>
												<span class="widget-icon">
													<i class="fa fa-table"></i>
												</span>
												<h2>城市列表</h2>
											</header>
											<div>
												<div class="jarviswidget-editbox"></div>
												<div class="widget-body">
													<table id="dt_basic" class="table table-striped table-bordered table-hover overseaGrid">
														<thead>
															<tr>
																<th>cityCode</th>
																<th>英文名</th>
																<th>中文名</th>
																<th>状态</th>
															    <th>操作</th>
															</tr>
														</thead>
														<tbody>
															<#list country.cities as city>
															<tr class="small">
																<td>${city.cityCode?if_exists}</td>
							                                    <td>${city.cityNameEn?if_exists}</td>
							                                    <td>${city.cityName?if_exists}</td>
		                                    					<td><#if city.status=='online'><span style="color: green;">已上线</span><#elseif city.status=='new'><span style="color: green;">新增</span><#else><span style="color: red;">已下线</span></#if></td>
							                                    <td width="240" style="text-align: center;">
							                                        <a class="btn btn-warning" href="${basePath}/country/toCityInfo?cityCode=${city.cityCode}" style="margin: 0px 12px;">修改</a>
							                                    </td>
															</tr>
															</#list>
														</tbody>
													</table>
												</div>
											</div>
										</div>
									</article>
								</div>
							</#if>
							
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="hot-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>热门酒店</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover hotHotel">
									<thead>
										<tr>
											<th>酒店中文名</th>
											<th>酒店英文名</th>
											<!--<th width="100">排序</th>-->
										    <th width="250">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list country.hotHotels as data>
										<tr class="small">
		                                    <td>${data.hotelName?if_exists}</td>
		                                    <td>${data.hotelNameEn?if_exists}</td>
		                                    <td style="text-align: center;">
												<#if chainCode=="LW">
		                                        <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0);">查看封面</a>
												</#if>
		                                        <a class="btn btn-warning updateHotHotelBtn" href="javascript:void(0)" data-id="${data.hotSiteId}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteHotHotelBtn" href="javascript:void(0)" data-id="${data.hotSiteId}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addHotHotelBtn" class="btn btn-primary" value="添加热门酒店" style="margin-right: 12px;"/>
									<!--
									<input type="button" id="saveHotOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序"/>
									-->
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
var type ='country-cover';
$(document).ready(function() {
	console.log("country: ${country}");
	
	$("#itemAdd").click(function(){
			updateCountry();
	});
	

	$("#downloadCountryInfo").click(function(){
		downloadCountryInfo();
	});
	
	var $selectBannerModal = $('#selectBannerModal');
	var $itemBanner = $('#coverUrl');
	var selectBanner = function() {
		var params = {};
		params.relateType = type;
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val(type);
		$selectBannerModal.find('input[name=relateCode]').val($('#itemCode').val());
		$selectBannerModal.find('input[name=format]').val('269*168c.jpg');
		$selectBannerModal.find('#imgsize').text('269 x 168');
		$selectBannerModal.find('.modal-title').text('添加封面');
		$selectBannerModal.modal();
	};
	
	$('#selectBannerBtn').on('click', selectBanner);
	$itemBanner.on('click', selectBanner);
	
	$("input:radio[name='is_online']").on('click', function() {
		var $obj = $(this);
		if($obj.val() == '1') {
			$('#conditionLayer').show();
			$('#remarkLayer').show();
			/**
			if($("input:radio[name='condition']:checked").val() == '0') {
				$('#remarkLayer').hide();
			} else {
				$('#remarkLayer').show();
			}
			**/
		} else {
			$('#conditionLayer').hide();
			$('#remarkLayer').hide();
		}
	});
	/**
	$("input:radio[name='condition']").on('click', function() {
		var $obj = $(this);
		if($obj.val() == '0') {
			$('#remarkLayer').hide();
		} else {
			$('#remarkLayer').show();
		}
	});
	**/
	
	$('#addHotHotelBtn').on('click', function() {
		window.location.href = '${basePath}/hot/hotel?type=6&countryCode=${country.countryCode}';
	});
	
	$('.updateHotHotelBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/hot/hotel?id='+$obj.attr('data-id');
	});
	
	$('.deleteHotHotelBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hot/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/country/toCountryInfo?countryCode=${country.countryCode}';
				}
			}, 'json');
		}
	});
});

function selectImage(url) {
	$('#coverUrl').val(url);
	$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};

function returnToCountryList(){
	window.location="${basePath}/country/toCountryList";
}

function updateCountry(){
	$.ajax({
			async : false,
			type : "POST",
			dataType:"json",
			data:{
				chainCode:$("#chainCode").val(),
				countryCode:$("#countryCode").val(),
				countryName:$("#countryName").val(),
				countryNameEn:$("#countryNameEn").val(),
				countryNamePinyin:$("#countryNamePinyin").val(),
				countryLink:$("#countryLink").val(),
				continentNameEn:$("#continentNameEn").val(),
				brief:$("#brief").val(),
				detail:$("#detail").val(),
				introducing:$("#introducing").val(),
				countryUrl:$("#countryUrl").val(),
				status:$("input:radio[name='status']:checked").val()
			},
			url :  "${basePath}/country/updateCountry",
			error : function(data) {
				return "执行失败";
			},
			success : function(data) {
				if(data.code==1){
					alert(data.desc);
					return false;
				}else{
					alert(data.desc);
					return false;
				}
			}
		});
}

function downloadCountryInfo(){
	$.ajax({
			async : false,
			type : "POST",
			dataType:"json",
			data:{
				chainCode:$("#chainCode").val(),
				countryCode:$("#countryCode").val(),
			},
			url :  "${basePath}/country/downloadCountryInfo",
			error : function(data) {
				return "执行失败";
			},
			success : function(data) {
				if(data.code==1){
					alert(data.desc);
					window.location.reload();
					return false;
				}else{
					alert(data.desc);
					return false;
				}
			}
		});
}
</script>
</body>
</html>