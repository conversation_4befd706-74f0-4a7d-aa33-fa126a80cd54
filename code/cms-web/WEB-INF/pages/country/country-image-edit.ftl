<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li><#if id gte 1>修改<#else>添加</#if>图片</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="country-image-edit-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2><#if id gte 1>修改<#else>添加</#if>图片</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/country/image-save">
									<input type="hidden" id="id" name="id" value="${data.id?if_exists}" />
									<input type="hidden" id="countryCode" name="countryCode" value="${data.countryCode?if_exists}" />
									<input type="hidden" id="roomCode" name="roomCode" value="${data.roomCode?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-2 control-label" for="name" style="font-size: 16px;">中文名称</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="name" name="name" value="${data.name?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="nameEn" style="font-size: 16px;">英文名称</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="nameEn" name="nameEn" value="${data.nameEn?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="type" style="font-size: 16px;">类型</label>
											<div class="col-md-10">
												<select class="form-control" id="type" name="type">
													<option value="3"<#if data.type?if_exists=="3"> selected="selected"</#if>>国家图片</option>
												<!--	<option value="1"<#if data.type?if_exists=="1"> selected="selected"</#if>>酒店图片</option>
													<option value="2"<#if data.type?if_exists=="2"> selected="selected"</#if>>房型图片</option>-->
												</select>
											</div>
										</div>
										<!--<div id="roomLayer" class="form-group" style="display: none;">
											<label class="col-md-2 control-label" for="roomName" style="font-size: 16px;">房型</label>
											<div class="col-md-9">
												<input class="form-control" type="text" id="roomName" name="roomName" value="${data.roomName?if_exists}" readonly="readonly" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectRoomBtn" class="btn btn-success" value="选取" />
											</div>
										</div>-->
										<div class="form-group">
											<label class="col-md-2 control-label" for="url" style="font-size: 16px;">图片</label>
											<div class="col-md-7">
												<input class="form-control" type="text" id="url" name="url" value="${data.url?if_exists}" readonly="readonly" />
											</div>
											<div class="col-md-2">
												<input type="button" id="selectBannerBtn" class="btn btn-success" value="选择图片" />
											</div>
											<div class="col-md-1">
												<input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${data.url?if_exists}" value="查看" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="isDefault" style="font-size: 16px;">默认</label>
											<div class="col-md-10">
												<label class="radio-inline">
													<input type="radio" id="isDefault_no" name="isDefault" value="0" <#if data.isDefault=='0'>checked="checked"</#if> />
													<span>否</span>
												</label>
												<label class="radio-inline">
													<input type="radio" id="isDefault_yes" name="isDefault" value="1" <#if data.isDefault=='1'>checked="checked"</#if> />
													<span>是</span>
												</label>
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="remarkCn" style="font-size: 16px;">中文描述</label>
											<div class="col-md-10">
												<textarea class="form-control" style="min-height: 100px;" id="remarkCn" name="remarkCn">${data.remarkCn?if_exists}</textarea>
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="remarkEn" style="font-size: 16px;">英文描述</label>
											<div class="col-md-10">
												<textarea class="form-control" style="min-height: 100px;" id="remarkEn" name="remarkEn">${data.remarkEn?if_exists}</textarea>
											</div>
										</div>
										<!-- 
										<div class="form-group">
											<label class="col-md-2 control-label" for="orderNum" style="font-size: 16px;">排序</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="orderNum" name="orderNum" value="${data.orderNum?if_exists}" />
											</div>
										</div>
										 -->
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" style="margin-right: 12px;" />
												<a class="btn btn-default" href="${basePath}/country/photos?countryCode=${data.countryCode?if_exists}" >返回</a>
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	var $roomLayer = $('#roomLayer');
	var $stype = $('select#type option:selected');
	if($stype.length<=0 || $stype.val() == '1') {
		$roomLayer.hide();
	} else {
		$roomLayer.show();
	}
	
	var $type = $('select#type');
	$type.on('change', function() {
		if($type.val() == '1') {
			$roomLayer.hide();
		} else {
			$roomLayer.show();
		}
	});
	
	var $selectHotelImageModal = $('#selectHotelImageModal');
	var selectHotelImage = function() {
		var params = {};
		$selectHotelImageModal.find('.modal-title').text('上传酒店图片');
		$selectHotelImageModal.modal();
	};
	var $itemBanner = $('#url');
	$('#selectBannerBtn').on('click', selectHotelImage);
	$itemBanner.on('click', selectHotelImage);
	
	var $selectRoomModal = $('#selectRoomModal');
	$('#selectRoomBtn').on('click', showRoomModel);
	$('#roomName').on('click', showRoomModel);
	function showRoomModel() {
		$selectRoomModal.find('iframe').attr('src', '${basePath}/country/room/select?countryCode=${data.countryCode?if_exists}');
		$selectRoomModal.modal();
	}
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var url = $editForm.attr('action');
		var params = $editForm.serializeArray();
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/country/photos?countryCode=${data.countryCode?if_exists}';
			}
		}, 'json');
		return false;
	});
});
function selectRoomInfo(code, name){
	$('#roomCode').val(code);
	$('#roomName').val(name);
	$('#selectRoomModal').modal('hide');
}
function selectImage(url) {
	$('#url').val(url);
	$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};
function hotelImageUploadResult(fcode, fname) {
	if(fcode == '1') {
		alert('没有要上传的图片！');
		return;
	}
	if(fcode == '2') {
		alert('上传的图片不能超过2M');
		return;
	}
	if(fcode == '3') {
		alert('只允许上传jpg格式的图片！');
		return;
	}
	if(fname == '') {
		alert('没有要上传的图片！');
		return;
	}
	alert('上传成功！');
	$('#selectHotelImageModal').modal('hide');
	var url = '${imageUrl}/'+fname;
	$('.viewBanner').attr('data-img', url);
	$('#url').val(fname);
}
</script>
</body>
</html>