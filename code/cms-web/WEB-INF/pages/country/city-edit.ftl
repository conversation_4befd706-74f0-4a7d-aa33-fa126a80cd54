<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="hotel-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>修改城市信息</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body" style="margin-left:-30px;">
								<form id="editForm" class="form-horizontal" method="post" action="">
									<fieldset >
										<input class="form-control" type="text" id="chainCode" name="chainCode" value="${city.chainCode?if_exists}" style='display:none'/>
										<input class="form-control" type="text" id="countryCode" name="countryCode" value="${city.countryCode?if_exists}" style='display:none'/>
										<input class="form-control" type="text" id="cityCode" name="cityCode" value="${city.cityCode?if_exists}" style='display:none'/>
										<div class="form-group">
											<label class="col-md-2 control-label" for="cityNameEn" style="font-size: 16px;" check-type="required" required-message="名称不能为空。" >英文名</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="cityNameEn" value="${city.cityNameEn?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="cityName" style="font-size: 16px;">中文名</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="cityName" value="${city.cityName?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="cityNamePinyin" style="font-size: 16px;">城市名拼音</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="cityNamePinyin" value="${city.cityNamePinyin?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="cityLink" style="font-size: 16px;">cityLink</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="cityLink" value="${city.cityLink?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="cover" style="font-size: 16px;">状态</label>
											<div class="col-md-10">
												<input type="radio" id="is_online_1" name="status" value="online" <#if city.status=='online'>checked="true"</#if>/><span style="font-size: 16px;">上线</span>
												<input type="radio" id="is_online_0" name="status" value="offline" <#if city.status=='offline'>checked="true"</#if>/><span style="font-size: 16px;">下线</span>
												<input type="radio" id="is_online_2" name="status" value="new" <#if city.status=='new'>checked="true"</#if>/><span style="font-size: 16px;">新增</span>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="button" class="btn btn-primary" value="保存" id="updateCity" style="margin-right: 12px;" />
												<input type="button" class="btn btn-default" value="返回" onclick="returnToCityList()" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
var type ='hotel-cover';
$(document).ready(function() {
	$("#updateCity").click(function(){
		updateCity();
	}); 
});
 
function updateCity(){
	$.ajax({
			async : false,
			type : "POST",
			dataType:"json",
			data:{
				chainCode:$("#chainCode").val(),
				countryCode:$("#countryCode").val(),
				cityCode:$("#cityCode").val(),
				cityName:$("#cityName").val(),
				cityNameEn:$("#cityNameEn").val(),
				cityNamePinyin:$("#cityNamePinyin").val(),
				cityLink:$("#cityLink").val(),
				status:$("input:radio[name='status']:checked").val()
			},
			url :  "${basePath}/country/updateCity",
			error : function(data) {
				return "执行失败";
			},
			success : function(data) {
				if(data.code==1){
					alert(data.desc);
					return false;
				}else{
					alert(data.desc);
					return false;
				}
			}
		});
}

function returnToCityList(){
	window.location="${basePath}/country/toCountryInfo?countryCode=${city.countryCode?if_exists}";
}
</script>
</body>
</html>