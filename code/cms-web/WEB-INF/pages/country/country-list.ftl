<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>国家管理</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="country-list-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>国家列表</h2>
						</header>
						<div>
							<div class="widget-body no-padding">
								<div class="widget-body-toolbar">
									<form id="queryForm" class="form-inline" method="post" action="${basePath}/country/toCountryList">
										<input type="hidden" id="research" name="research" value="true" />
										<input type="hidden" id="start" name="start" value="${page.currentPage}" />
										<input type="hidden" id="limit" name="limit" value="${limit}" />
										<div class="form-group" style="margin: 0px 15px 8px 0px;">
								        	<input type="text" class="form-control" name="countryCode" value="${countryCode?if_exists}" placeholder="国家代码" />
								       	</div>
										<div class="form-group" style="margin: 0px 15px 8px 0px;">
						                	<input type="text" class="form-control" name="name" value="${name?if_exists}" placeholder="国家中文名" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
						                	<input type="text" class="form-control" name="enname" value="${enname?if_exists}" placeholder="国家英文名" />
					                	</div>
					                	<div class="form-group" style="margin: 0px 15px 8px 0px;">
					                		<input type="submit" class="btn btn-primary" value="查询" style="margin-right: 8px;" />
					                	</div>
									</form>
								</div>
								<table id="dt_basic" class="table table-striped table-bordered table-hover">
									<thead>
										<tr>
											<th>countryCode</th>
											<th>国家英文名</th>
											<th>国家中文名</th>
										    <th width="80">状态</th>
										    <th width="200">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list page.datas as country>
										<tr class="small">
											<td>${country.countryCode?if_exists}</td>
		                                    <td>${country.countryNameEn?if_exists}</td>
											<td>${country.countryName?if_exists}</td>
		                                    <td><#if country.status=='online'><span style="color: green;">已上线</span><#elseif country.status=='new'><span style="color: green;">新增</span><#else><span style="color: red;">已下线</span></#if></td>
		                                    <td style="text-align: center;">
		                                   		 <input type="button" class="btn btn-info" value="国家图片" onclick="window.location.href='${basePath}/country/photos?countryCode=${country.countryCode?if_exists}'" />
		                                        <a class="btn btn-warning" href="${basePath}/country/toCountryInfo?countryCode=${country.countryCode?if_exists}">修改</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<#include "/common/page.ftl" />
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() { 
});
function toPage(pageNo) {
	$('#start').val(pageNo);
	$('#queryForm').submit();
}
</script>
</body>
</html>