<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="foot-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>热门国内酒店设置</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover localGrid">
									<thead>
										<tr>
											<th>酒店名</th>
											<th>酒店英文名</th>
											<th width="100">排序</th>
										    <th width="240">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list localseaDatas as data>
										<tr class="small">
		                                    <td>${data.hotelName?if_exists}</td>
		                                    <td>${data.hotelNameEn?if_exists}</td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" data-id="${data.id}" value="${data.orderNum}" data-img="${imageUrl}/${data.cover}" data-img-path="${data.cover}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0);">查看封面</a>
		                                        <a class="btn btn-warning updateLocalBtn" href="javascript:void(0)" data-id="${data.id}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteBtn" href="javascript:void(0)" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addLocalBtn" class="btn btn-primary" value="添加国内酒店" style="margin-right: 12px;" />
									<input type="button" id="saveLocalOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序" />
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="foot-edit-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>热门海外酒店设置</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover overseaGrid">
									<thead>
										<tr>
											<th>酒店名</th>
											<th>酒店英文名</th>
											<th width="100">排序</th>
										    <th width="240">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list overseaDatas as data>
										<tr class="small">
		                                    <td>${data.hotelName?if_exists}</td>
		                                    <td>${data.hotelNameEn?if_exists}</td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" data-id="${data.id}" value="${data.orderNum}" data-img="${imageUrl}/${data.cover}" data-img-path="${data.cover}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0);">查看封面</a>
		                                        <a class="btn btn-warning updateOverseaBtn" href="javascript:void(0)" data-id="${data.id}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteBtn" href="javascript:void(0)" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addOverseaBtn" class="btn btn-primary" value="添加海外酒店" style="margin-right: 12px;" />
									<input type="button" id="saveOverseaOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序" />
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="foot-edit-3" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>为何选择立鼎世设置</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/about/save">
									<input type="hidden" id="itemCode" name="itemCode" value="${data.itemCode?if_exists}" />
									<input type="hidden" id="itemName" name="itemName" value="${data.title?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-1 control-label" for="precover" style="font-size: 16px;">图片</label>
											<div class="col-md-8">
												<input class="form-control" type="text" id="precover" name="precover" value="${data.banner?if_exists}" readonly="readonly" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectCoverBtn" class="btn btn-success" value="选取" />
											</div>
											<div class="col-md-2">
												<input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${data.banner?if_exists}" value="查看图片" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-1 control-label" for="subname" style="font-size: 16px;">标题</label>
											<div class="col-md-11">
												<input class="form-control" type="text" id="subname" name="subname" value="${data.describer?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<div class="col-md-12">
												<div id="subitems">${data.content?if_exists}</div>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<div class="modal fade" id="checkCoverModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content" style="width: 840px;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">确认封面</h4>
			</div>
			<div class="modal-body" style="padding-bottom: 8px;">
				<form id="bannerUploadForm" class="form-horizontal" method="post" action="${basePath}/image/upload" enctype="multipart/form-data" target="uploadframe">
					<input type="hidden" name="relateType" />
					<input type="hidden" name="relateCode" />
					<fieldset>
						<div class="form-group">
							<div class="col-sm-12" style="text-align: center;"><img class="confirmCover" style="max-width: 480px;" /></div>
						</div>
						<div class="form-group">
							<div class="col-md-11">
								<input class="form-control" type="file" id="file" name="file" />
							</div>
							<div class="col-md-1">
								<input type="submit" class="btn btn-success" value="上传" />
							</div>
						</div>
						<div class="form-group">
							<div class="superbox col-sm-12"></div>
						</div>
					</fieldset>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" id="confirmBtn" class="btn btn-primary">确认</button>
				<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
			</div>
		</div>
	</div>
</div>
<div id="contentTemp" style="display:none;"></div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	var $selectBannerModal = $('#selectBannerModal');
	var selectBanner = function() {
		var params = {};
		params.relateType = 'footpre-banner';
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val('footpre-banner');
		$selectBannerModal.find('input[name=relateCode]').val($editForm.find('#itemCode').val());
		$selectBannerModal.find('input[name=format]').val('303*157c.jpg');
		$selectBannerModal.find('#imgsize').text('303 x 157');
		$selectBannerModal.find('.modal-title').text('添加图片');
		$selectBannerModal.modal();
	};
	
	var $precover = $('#precover');
	$('#selectCoverBtn').on('click', selectBanner);
	$precover.on('click', selectBanner);
	
	$('#addLocalBtn').on('click', function() {
		window.location.href = '${basePath}/foot/local';
	});
	$('#addOverseaBtn').on('click', function() {
		window.location.href = '${basePath}/foot/oversea';
	});
	
	var $subitems = $('#subitems');
	$subitems.summernote({height: 180});
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var subname = $editForm.find('#subname').val();
		subname = $.trim(subname);
		if(subname.length <= 0) {
			alert('标题不能为空！');
			return false;
		}
		if(subname.length > 10) {
			alert('标题最多10个字！');
			return false;
		}
		var content = $subitems.code();
		content = $.trim(content);
		var tcontent = $('#contentTemp').html(content).text();
		if(tcontent.length <= 0) {
			alert('正文不能为空');
			return false;
		}
		if(tcontent.length > 100) {
			alert('正文最多100个字');
			return false;
		}
		
		var url = $editForm.attr('action');
		var params = {};
		params.itemCode = $editForm.find('#itemCode').val();
		params.itemName = $editForm.find('#itemName').val();
		params.banner = $editForm.find('#precover').val();
		params.describer = subname;
		params.content = content;
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			}
		}, 'json');
		return false;
	});
	
	$('.updateLocalBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/foot/local?id='+$obj.attr('data-id');
	});
	$('.updateOverseaBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/foot/oversea?id='+$obj.attr('data-id');
	});
	
	$('#saveOverseaOrderBtn').on('click', function() {
		var $firstOrder = $('.overseaGrid input[type=hidden][name=ordernum]:first');
		if($firstOrder.val() != '1') {
			var params = {};
			params.relateType = 'footsea-banner';
			$.post('${basePath}/image/list.json', params, function(datas) {
				$checkCoverModal.find('.superbox').html(convertImageHtml(datas));
			}, 'json');
			$checkCoverModal.find('.confirmCover').attr('src', $firstOrder.attr('data-img')).attr('data-img-path', $firstOrder.attr('data-img-path'));;
			$checkCoverModal.find('input[name=relateType]').val('footsea-banner');
			$checkCoverModal.find('input[name=relateCode]').val($firstOrder.attr('data-id'));
			$selectBannerModal.find('input[name=format]').val('303*157c.jpg');
			$selectBannerModal.find('#imgsize').text('303 x 157');
			$selectBannerModal.find('.modal-title').text('添加封面');
			$checkCoverModal.modal();
		} else {
			saveOverseaOrder();
		}
	});
	function saveOverseaOrder() {
		var orderIndex = 1;
		var items = '';
		$('.overseaGrid input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/hotel/hot/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/foot/edit';
			}
		}, 'json');
	}
	
	$('#saveLocalOrderBtn').on('click', function() {
		var $firstOrder = $('.localGrid input[type=hidden][name=ordernum]:first');
		if($firstOrder.val() != '1') {
			var params = {};
			params.relateType = 'footlocal-banner';
			$.post('${basePath}/image/list.json', params, function(datas) {
				$checkCoverModal.find('.superbox').html(convertImageHtml(datas));
			}, 'json');
			$checkCoverModal.find('.confirmCover').attr('src', $firstOrder.attr('data-img')).attr('data-img-path', $firstOrder.attr('data-img-path'));
			$checkCoverModal.find('input[name=relateType]').val('footlocal-banner');
			$checkCoverModal.find('input[name=relateCode]').val($firstOrder.attr('data-id'));
			$selectBannerModal.find('input[name=format]').val('303*157c.jpg');
			$selectBannerModal.find('#imgsize').text('303 x 157');
			$selectBannerModal.find('.modal-title').text('添加封面');
			$checkCoverModal.modal();
		} else {
			saveLocalOrder();
		}
	});
	function saveLocalOrder() {
		var ordeIndex = 1;
		var items = '';
		$('.localGrid input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+ordeIndex+';';
			ordeIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/hotel/hot/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/foot/edit';
			}
		}, 'json');
	}
	
	var $checkCoverModal = $('#checkCoverModal');
	$checkCoverModal.find('#confirmBtn').on('click', function() {
		var _cover = $checkCoverModal.find('.confirmCover').attr('data-img-path');
		var _type = $checkCoverModal.find('input[name=relateType]').val();
		var _code = $checkCoverModal.find('input[name=relateCode]').val();
		var _url = '${basePath}/hotel/hot/cover/update';
		var _params = {};
		_params.cover = _cover;
		_params.code = _code;
		$.post(_url, _params, function(data) {
			if(_type == 'footlocal-banner') {
				saveLocalOrder();
			} else {
				saveOverseaOrder();
			}
		}, 'json');
	});
	
	$('.deleteBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hotel/hot/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/foot/edit';
				}
			}, 'json');
		}
	});
	
	$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
});
function selectImage(url) {
	$('#precover').val(url);
	$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};
</script>
</body>
</html>