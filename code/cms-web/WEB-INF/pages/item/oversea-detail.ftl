<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="oversea-detail-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2><#if data.id gte 1>修改<#else>添加</#if>海外酒店Banner</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/banner/save">
									<input type="hidden" id="id" name="id" value="${data.id?if_exists}" />
									<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
									<input type="hidden" id="activeId" name="activeId" value="${data.activeId?if_exists}" />
									<input type="hidden" id="title" name="title" value="${data.title?if_exists}" />
									<input type="hidden" id="hotelCode" name="hotelCode" value="${data.hotelCode?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-2 control-label" for="cover" style="font-size: 16px;">Banner</label>
											<div class="col-md-9">
												<input class="form-control" type="text" id="cover" name="cover" value="${data.cover?if_exists}" readonly="readonly" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectBannerBtn" class="btn btn-success" value="选取" />
											</div>
											<!-- 
											<div class="col-md-2">
												<input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${data.cover?if_exists}" value="查看Banner" />
											</div>
											 -->
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="type" style="font-size: 16px;">类型</label>
											<div class="col-md-10">
												<select class="form-control" id="type" name="type">
													<option value="1"<#if data.type?if_exists=="1"> selected="selected"</#if>>酒店</option>
													<option value="2"<#if data.type?if_exists=="2"> selected="selected"</#if>>促销活动</option>
												</select>
											</div>
										</div>
										<div id="hotelGroup" class="form-group" style="display: none;">
											<label class="col-md-2 control-label" for="hotel" style="font-size: 16px;">酒店</label>
											<div class="col-md-9">
												<input class="form-control" type="text" id="hotel" name="hotel" value="${data.hotelName?if_exists}" readonly="readonly" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectHotelBtn" class="btn btn-success" value="选取" />
											</div>
										</div>
										<div id="activeGroup" class="form-group" style="display: none;">
											<label class="col-md-2 control-label" for="active" style="font-size: 16px;" >促销活动</label>
											<div class="col-md-9">
												<input class="form-control" type="text" id="active" name="active" readonly="readonly" value="${data.activeName?if_exists}"/>
											</div>
											<div class="col-md-1">
												<input type="button" id="selectActiveBtn" class="btn btn-success" value="选取" />
											</div>
										</div>
										<div style="text-align: right;">
											<input type="button" class="btn btn-info reviewBanner" data-img="${imageUrl}/${data.cover?if_exists}" value="预览效果" />
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" style="margin-right: 12px;" />
												<input type="button" class="btn btn-default" value="返回" onclick="window.location.href='${basePath}/oversea/edit'" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	var $hotelGroup = $('#hotelGroup');
	var $activeGroup = $('#activeGroup');
	var $titleGroup = $('#titleGroup');
	var $stype = $('select#type option:selected');
	if($stype.length<=0 || $stype.val() == '1') {
		$hotelGroup.show();
	} else {
		$activeGroup.show();
		$titleGroup.show();
	}
	
	var $type = $('select#type');
	$type.on('change', function() {
		if($type.val() == '1') {
			$activeGroup.hide();
			$titleGroup.hide();
			$hotelGroup.show();
		} else {
			$hotelGroup.hide();
			$activeGroup.show();
			$titleGroup.show();
		}
	});
	
	var $selectBannerModal = $('#selectBannerModal');
	var selectBanner = function() {
		var params = {};
		params.relateType = 'oversea-banner';
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val('oversea-banner');
		$selectBannerModal.find('input[name=relateCode]').val($editForm.find('#id').val());
		$selectBannerModal.find('input[name=format]').val('1600*558c.jpg');
		$selectBannerModal.find('#imgsize').text('1600 x 558');
		$selectBannerModal.modal();
	};
	var $itemBanner = $('#cover');
	$('#selectBannerBtn').on('click', selectBanner);
	$itemBanner.on('click', selectBanner);
	
	var $selectHotelModal = $('#selectHotelModal');
	var $selectHotelBtn = $('#selectHotelBtn');
	var $hotel = $('#hotel');
	$hotel.on('click', function() {
		showHotelModel();
	});
	$selectHotelBtn.on('click', function() {
		showHotelModel();
	});
	function showHotelModel() {
		$selectHotelModal.find('iframe').attr('src', '${basePath}/hotel/select');
		$selectHotelModal.modal();
	}
	
	var $selectActiveModal = $('#selectActiveModal');
	var $selectActiveBtn = $('#selectActiveBtn');
	var $active = $('#active');
	$active.on('click', function() {
		$selectActiveModal.find('iframe').attr('src', '${basePath}/activity/select');
		$selectActiveModal.modal();
	});
	$selectActiveBtn.on('click', function() {
		$selectActiveModal.find('iframe').attr('src', '${basePath}/activity/select');
		$selectActiveModal.modal();
	});
	$selectActiveModal.find('table tr.small').on('dblclick', function() {
		$active.val('促销活动来啦');
		$selectActiveModal.modal('hide');
	});
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var url = $editForm.attr('action');
		var params = $editForm.serializeArray();
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/edit';
			}
		}, 'json');
		return false;
	});
	$('.reviewBanner').on('click', function() {
		var params = $editForm.serializeArray();
		$.post('${basePath}/banner/preview/one', params, function(data) {
			if(data.code == '1') {
				window.open('${overseaPreviewUrl}'+data.desc);
			} else {
				alert(data.desc);
			}
		}, 'json');
	});
});
function selectHotelInfo(hotelCode, hotelName, hotelNameEn, countryCode, countryName, cityCode, cityName) {
	$('#hotelCode').val(hotelCode);
	var hotel = hotelName+'（'+hotelNameEn+'）【 '+cityName+'，'+countryName+'】';
	$('#hotel').val(hotel);
	$('#title').val(hotel);
	$('#selectHotelModal').modal('hide');
}

function selectActiveInfo(id,title){
	$('#activeId').val(id);
	$('#title').val(title);
	$('#active').val(title);
	$('#selectActiveModal').modal('hide');
}
function selectImage(url) {
	$('#cover').val(url);
	$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};
</script>
</body>
</html>