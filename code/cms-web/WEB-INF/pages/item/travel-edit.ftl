<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="travel-edit-0" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>璀璨之旅主推Banner</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table id="dt_basic" class="table table-striped table-bordered table-hover overseaGrid">
									<thead>
										<tr>
											<th>标题</th>
										    <th>操作</th>
										</tr>
									</thead>
									<tbody>
									<#list pjourneys as jy>
										<tr class="small">
		                                    <td>${jy.title?if_exists}</td>
		                                    <td width="380" style="text-align: center;">
		                                    	<a class="btn btn-success viewBanner"  data-img="${imageUrl}/${jy.pbanner?if_exists}" style="margin-right: 12px;">查看主推Banner</a>
		                                        <a class="btn btn-warning" href="${basePath}/travel/banners" style="margin-right: 12px;">更换主推Banner</a>
		                                        <a class="btn btn-warning" href="${basePath}/travel/detail?id=${jy.id?if_exists}" style="margin-right: 12px;">修改</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<#if (pjourneys?size<=0)>
								<div style="margin-top: 12px;">
									<a class="btn btn-primary" href="${basePath}/travel/banners">添加主推Banner</a>
								</div>
								</#if>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="travel-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>璀璨之旅</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/about/save">
									<input type="hidden" id="itemCode" name="itemCode" value="${itemData.itemCode?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-2 control-label" for="itemName" style="font-size: 16px;">栏目名</label>
											<div class="col-md-10"><input class="form-control" type="text" id="itemName" name="itemName" value="${itemData.title?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webTitle" style="font-size: 16px;">网页Title</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webTitle" name="webTitle" value="${itemData.webtitle?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webDesc" style="font-size: 16px;">网页Description</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webDesc" name="webDesc" value="${itemData.webdesc?if_exists}"></div>
										</div>
										<div class="form-group">
											<div class="col-md-12">
												<textarea class="form-control" style="min-height: 120px;" id="describer" name="describer">${itemData.describer?if_exists}</textarea>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="travel-edit-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>璀璨之旅行程</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table id="dt_basic" class="table table-striped table-bordered table-hover overseaGrid">
									<thead>
										<tr>
											<th>标题</th>
											<th>排序</th>
										    <th>操作</th>
										</tr>
									</thead>
									<tbody>
									<#list journeys as jy>
										<tr class="small">
		                                    <td>${jy.title?if_exists}</td>
		                                    <td width="100" style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" class="form-control" style="width: 84px;" data-id="${jy.id}" value="${jy.orderNum?if_exists}" data-img="${imageUrl}/${jy.cover?if_exists}" data-img-path="${jy.cover?if_exists}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a></td>
		                                    <td style="display:none">${jy.id?if_exists}</td>
		                                    <td style="display:none">${jy.banner?if_exists}</td>
		                                    <td style="display:none">${jy.cover?if_exists}</td>
		                                    <td style="display:none">${jy.describer?if_exists}</td>
		                                    <td width="260" style="text-align: center;">
		                                    	<!-- <a class="btn btn-success viewBanner"  data-img="${imageUrl}/${jy.banner?if_exists}" style="margin-right: 12px;">查看Banner</a> -->
		                                        <a class="btn btn-info viewBanner" data-img="${imageUrl}/${jy.cover?if_exists}" style="margin-right: 12px;">查看封面</a>
		                                        <a class="btn btn-warning" ids='up' href="" style="margin-right: 12px;">修改</a>
		                                        <a class="btn btn-danger" ids='del' href="" did="${jy.id?if_exists}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addBtn" class="btn btn-primary" value="新增璀璨之旅" style="margin-right: 12px;" />
									<input type="button" class="btn bg-color-pink txt-color-white" value="保存排序" id="saveOrderBtn"/>
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<div class="modal fade" id="checkCoverModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content" style="width: 840px;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">确认封面</h4>
			</div>
			<div class="modal-body" style="padding-bottom: 8px;">
				<form id="bannerUploadForm" class="form-horizontal" method="post" action="${basePath}/image/upload" enctype="multipart/form-data" target="uploadframe">
					<input type="hidden" name="relateCode" />
					<input type="hidden" name="relateType" />
					<fieldset>
						<div class="form-group">
							<div class="col-sm-12" style="text-align: center;"><img class="confirmCover" style="max-width: 480px;" /></div>
						</div>
						<div class="form-group">
							<div class="col-md-11">
								<input class="form-control" type="file" id="file" name="file" />
							</div>
							<div class="col-md-1">
								<input type="submit" class="btn btn-success" value="上传" />
							</div>
						</div>
						<div class="form-group">
							<div class="superbox col-sm-12"></div>
						</div>
					</fieldset>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" id="confirmBtn" class="btn btn-primary" style="margin-right: 12px;">确认</button>
				<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
			</div>
		</div>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	$("a[ids='del']").click(deleteTr);
	$("a[ids='up']").click(updateTr);
	$('#addBtn').on('click', function() {
		window.location.href = '${basePath}/travel/detail';
	});
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var describer = $editForm.find('#describer').val();
		describer = $.trim(describer);
		if(describer.length>300) {
			alert('璀璨之旅文案在300个字以内');
			return false;
		}
		var url = $editForm.attr('action');
		var params = {};
		params.itemCode = $editForm.find('#itemCode').val();
		params.itemName = $editForm.find('#itemName').val();
		params.webTitle = $editForm.find('#webTitle').val();
		params.webDesc = $editForm.find('#webDesc').val();
		params.describer = describer;
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			}
		}, 'json');
		return false;
	});
});
function updateTr(){
	var pa = $(this).parent().parent();
	var url =  "${basePath}/travel/detail?"+"id="+pa.children("td:eq(2)").text();
	window.location =url;
	return false;
}
function deleteTr(){
	if(!confirm("是否删除？")){
		return false;
	}
	var pa = $(this).parent().parent();
	var id = $(this).attr("did");
	$.ajax({
			async : false,
			type : "POST",
			dataType:"json",
			data:{
				id:id
			},
			url :  "${basePath}/travel/delete",
			error : function(data) {
				return "执行失败";
			},
			success : function(data) {
				if(data.code==1){
					alert(data.desc);
					$(pa).remove();
					return false;
				}else{
					alert(data.desc);
					return false;
				}
			}
		});
		return false;
}
var $checkCoverModal = $('#checkCoverModal');
$('#saveOrderBtn').on('click', function() {
		/**
		var $firstOrder = $('.overseaGrid input[type=hidden][name=ordernum]:first');
		if($firstOrder.val() != '1') {
			var params = {};
			params.relateType = 'travel-cover';
			$.post('${basePath}/image/list.json', params, function(datas) {
				$checkCoverModal.find('.superbox').html(convertImageHtml(datas));
			}, 'json');
			$checkCoverModal.find('.confirmCover').attr('src', $firstOrder.attr('data-img')).attr('data-img-path', $firstOrder.attr('data-img-path'));;
			$checkCoverModal.find('input[name=relateCode]').val($firstOrder.attr('data-id'));
			$checkCoverModal.find('input[name=relateType]').val('travel-item-cover');
			
			$checkCoverModal.modal();
		} else {
			saveTravelOrder();
		}
		**/
		saveTravelOrder();
	});
 function saveTravelOrder() {
		var items = '';
		var orderIndex = 1;
		$('input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			//var value = $obj.val();
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.orders = items;
		$.post('${basePath}/travel/order', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/travel/edit';
			}
		}, 'json');
}
function selectImage(url) {
	$('#checkCoverModal img.confirmCover').attr('src', '${imageUrl}/'+url).attr('data-img-path', url);
};

$checkCoverModal.find('#confirmBtn').on('click', function() {
	var _cover = $checkCoverModal.find('.confirmCover').attr('data-img-path');
	var _code = $checkCoverModal.find('input[name=relateCode]').val();
	var _url = '${basePath}/travel/cover/update';
	var _params = {};
	_params.cover = _cover;
	_params.id = _code;
	$.post(_url, _params, function(data) {
		if(data.code==1){
			saveTravelOrder();
		}else{
			alert("修改失败");
		}
		
	}, 'json');
});
$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
</script>
</body>
</html>