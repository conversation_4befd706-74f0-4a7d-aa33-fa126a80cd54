<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="brand-list-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>Banner管理</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/about/save">
									<input type="hidden" id="itemCode" name="itemCode" value="${itemData.itemCode?if_exists}" />
									<input type="hidden" id="hotelCode" name="hotelCode" value="${itemData.hotelCode?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-2 control-label" for="itemName" style="font-size: 16px;">栏目名</label>
											<div class="col-md-10"><input class="form-control" type="text" id="itemName" name="itemName" value="${itemData.title?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webTitle" style="font-size: 16px;">网页Title</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webTitle" name="webTitle" value="${itemData.webtitle?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webDesc" style="font-size: 16px;">网页Description</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webDesc" name="webDesc" value="${itemData.webdesc?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="banner" style="font-size: 16px;">Banner</label>
											<div class="col-md-7">
												<input class="form-control" type="text" id="banner" name="banner" value="${itemData.banner?if_exists}" readonly="readonly" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectBannerBtn" class="btn btn-success" value="选取" />
											</div>
											<div class="col-md-2">
												<input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${itemData.banner?if_exists}" value="查看Banner" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="hotel" style="font-size: 16px;">酒店</label>
											<div class="col-md-9">
												<input class="form-control" type="text" id="hotel" name="hotel" value="${itemData.hotelName?if_exists}" readonly="readonly" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectHotelBtn" class="btn btn-success" value="选取" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="describer" style="font-size: 16px;">品牌精髓</label>
											<div class="col-md-10">
												<textarea class="form-control" style="min-height: 120px;" id="describer" name="describer">${itemData.describer?if_exists}</textarea>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="brand-list-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>品牌精髓</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table id="dt_basic" class="table table-striped table-bordered table-hover">
									<thead>
										<tr>
											<th>标题</th>
											<th width="100">排序</th>
										    <th width="140">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list datas as data>
										<tr class="small">
		                                    <td>${data.name}</td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" data-id="${data.id}" value="${data.orderNum}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning updateBtn" href="javascript:void(0)" data-id="${data.id}" data-name="${data.name}" data-describer="${data.describer}" style="margin-right: 12px;">修改</a>
		                                        <a class="btn btn-danger deleteBtn" href="javascript:void(0)" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addBtn" class="btn btn-primary" value="添加" style="margin-right: 12px;" />
									<input type="button" id="saveOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序" />
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<div class="modal fade" id="editBrandModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content" style="width: 640px;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body" style="padding-bottom: 8px;">
				<form class="form-horizontal" method="post" action="${basePath}/brand/save">
					<input type="hidden" name="id" id="id" />
					<input type="hidden" name="itemCode" id="itemCode" value="${itemCode}" />
					<fieldset>
						<div class="form-group">
							<label class="col-md-2 control-label" for="name" style="font-size: 16px;">名称</label>
							<div class="col-md-10">
								<input class="form-control" type="text" id="name" name="name" />
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-2 control-label" for="describer" style="font-size: 16px;">正文</label>
							<div class="col-md-10">
								<textarea class="form-control" style="min-height: 120px;" id="describer" name="describer"></textarea>
							</div>
						</div>
					</fieldset>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal" style="margin-right: 12px;">关闭</button>
				<button type="button" id="saveBtn" class="btn btn-primary">保存</button>
			</div>
		</div>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	var $selectBannerModal = $('#selectBannerModal');
	var selectBanner = function() {
		var params = {};
		params.relateType = 'brand-banner';
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val('brand-banner');
		$selectBannerModal.find('input[name=relateCode]').val($editForm.find('#itemCode').val());
		$selectBannerModal.find('input[name=format]').val('1600*558c.jpg');
		$selectBannerModal.find('#imgsize').text('1600 x 558');
		$selectBannerModal.modal();
	};
	var $itemBanner = $('#banner');
	$('#selectBannerBtn').on('click', selectBanner);
	$itemBanner.on('click', selectBanner);
	
	var $selectHotelModal = $('#selectHotelModal');
	var $selectHotelBtn = $('#selectHotelBtn');
	var $hotel = $('#hotel');
	$hotel.on('click', function() {
		showHotelModel();
	});
	$selectHotelBtn.on('click', function() {
		showHotelModel();
	});
	function showHotelModel() {
		$selectHotelModal.find('iframe').attr('src', '${basePath}/hotel/select');
		$selectHotelModal.modal();
	}
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var url = $editForm.attr('action');
		var params = {};
		params.itemCode = $editForm.find('#itemCode').val();
		params.itemName = $editForm.find('#itemName').val();
		params.webTitle = $editForm.find('#webTitle').val();
		params.webDesc = $editForm.find('#webDesc').val();
		params.banner = $editForm.find('#banner').val();
		params.hotelCode = $editForm.find('#hotelCode').val();
		params.describer = $editForm.find('#describer').val();
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			}
		}, 'json');
		return false;
	});
	
	var $editBrandModal = $('#editBrandModal');
	$('#addBtn').on('click', function() {
		$editBrandModal.find('.modal-title').text('添加品牌精髓');
		$editBrandModal.find('#id').val('');
		$editBrandModal.find('#name').val('');
		$editBrandModal.find('#describer').val('');
		$editBrandModal.modal({backdrop: 'static', keyboard: false});
	});
	$('.updateBtn').on('click', function() {
		var $obj = $(this);
		$editBrandModal.find('.modal-title').text('修改品牌精髓');
		$editBrandModal.find('#id').val($obj.attr('data-id'));
		$editBrandModal.find('#name').val($obj.attr('data-name'));
		$editBrandModal.find('#describer').val($obj.attr('data-describer'));
		$editBrandModal.modal({backdrop: 'static', keyboard: false});
	});
	$editBrandModal.find('#saveBtn').on('click', function() {
		var $form = $editBrandModal.find('form');
		var url = $form.attr('action');
		var params = $form.serializeArray();
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/brand/edit';
			}
		}, 'json');
	});
	
	$('#saveOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			var value = orderIndex;
			items += id+':'+value+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/brand/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/brand/edit';
			}
		}, 'json');
	});
	
	$('.deleteBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/brand/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/brand/edit';
				}
			}, 'json');
		}
	});
	
	$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
});
function selectHotelInfo(hotelCode, hotelName, hotelNameEn, countryCode, countryName, cityCode, cityName) {
	$('#hotelCode').val(hotelCode);
	var hotel = hotelName+'（'+hotelNameEn+'）【 '+cityName+'，'+countryName+'】';
	$('#hotel').val(hotel);
	$('#selectHotelModal').modal('hide');
}
function selectImage(url) {
	$('#banner').val(url);
	$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};
</script>
</body>
</html>