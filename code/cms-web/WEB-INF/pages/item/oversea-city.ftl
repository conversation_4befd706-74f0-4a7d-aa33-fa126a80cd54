<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="oversea-city-101" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2><#if data.id gte 1>修改<#else>添加</#if>精选海外目的地</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/hotel/area/save">
									<input type="hidden" id="id" name="id" value="${data.id?if_exists}" />
									<input type="hidden" id="type" name="type" value="3" />
									<input type="hidden" id="name" name="name" value="${data.name?if_exists}" />
									<input type="hidden" id="parentId" name="parentId" value="${parentId?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-1 control-label" for="areaCode" style="font-size: 16px;">城市</label>
											<div class="col-md-11">
												<select class="form-control" id="areaCode" name="areaCode">
									       			<option value="">请选择城市</option>
									       			<#list countries as datax>
									       				<#list datax.city as datar>
									       					<option value="${datar.cityCode?if_exists}" data-name="${datar.cityName?if_exists}"<#if datar.cityCode=data.areaCode?if_exists> selected="selected"</#if>>${datax.countryName} - ${datar.cityName}</option>
									       				</#list>
									       			</#list>
									       		</select>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" style="margin-right: 12px;" />
												<input type="button" class="btn btn-default" value="返回" onclick="window.location.href='${basePath}/oversea/address?id=${parentId}'" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var url = $editForm.attr('action');
		var $areaCode = $editForm.find('#areaCode');
		if($areaCode.val() == '') {
			alert('请选择城市');
			return false;
		}
		var nameText = $areaCode.find('option:selected').attr('data-name');
		$editForm.find('#name').val(nameText);
		var params = $editForm.serializeArray();
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/address?id=${parentId}';
			}
		}, 'json');
		return false;
	});
});
</script>
</body>
</html>