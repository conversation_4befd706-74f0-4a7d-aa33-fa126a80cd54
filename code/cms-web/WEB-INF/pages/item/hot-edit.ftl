<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="hot-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>热门酒店</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover hotHotel">
									<thead>
										<tr>
											<th>酒店中文名</th>
											<th>酒店英文名</th>
											<!--<th width="100">排序</th>-->
										    <th width="250">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list hotHotels as data>
										<tr class="small">
		                                    <td>${data.hotelName?if_exists}</td>
		                                    <td>${data.hotelNameEn?if_exists}</td>
		                                    <!--
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" value="${data.orderNum}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    -->
		                                    <td style="text-align: center;">
												<#if chainCode=="LW">
		                                        <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0);">查看封面</a>
												</#if>
		                                        <a class="btn btn-warning updateHotHotelBtn" href="javascript:void(0)" data-id="${data.id}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteHotBtn" href="javascript:void(0)" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addHotBtn" class="btn btn-primary" value="添加热门酒店" style="margin-right: 12px;"/>
									<!--
									<input type="button" id="saveHotOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序"/>
									-->
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="hot-edit-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>热门城市</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover seaAreaGrid">
									<thead>
										<tr>
											<th>城市中文名</th>
											<th>城市英文名</th>
											<!-- 
											<th width="100">排序</th>
											-->
										    <th width="250">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list hotCities as data>
										<tr class="small">
		                                    <td>${data.name?if_exists}</td>
		                                    <td>${data.nameEn?if_exists}</td>
		                                    <!--
		                                    <td style="text-align: center;">
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    -->
		                                    <td style="text-align: center;">
		                                    		<#if chainCode=="LW">
		                                        <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0)">查看封面</a>
		                                        </#if>
		                                        <a class="btn btn-warning updateHotCityBtn" href="javascript:void(0)" data-id="${data.id}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteHotBtn" href="javascript:void(0)" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addHotCityBtn" class="btn btn-primary" value="添加热门城市" style="margin-right: 12px;"/>
									<!--
									<input type="button" id="saveSeaAreaOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序"/>
									-->
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="hot-edit-3" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>热门国家</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover seaAreaGrid">
									<thead>
										<tr>
											<th>国家中文名</th>
											<th>国家英文名</th>
											<!--
											<th width="100">排序</th>
											-->
										    <th width="250">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list hotCountries as data>
										<tr class="small">
		                                    <td>${data.countryName?if_exists}</td>
		                                    <td>${data.countryNameEn?if_exists}</td>
		                                    <!--
		                                    <td style="text-align: center;">
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    -->
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning updateHotCountryBtn" href="javascript:void(0)" data-id="${data.hotSiteId}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteHotBtn" href="javascript:void(0)" data-id="${data.hotSiteId}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addHotCountryBtn" class="btn btn-primary" value="添加热门国家" style="margin-right: 12px;"/>
									<!--
									<input type="button" id="saveSeaAreaOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序"/>
									-->
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="hot-edit-4" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>大洲的热门城市和酒店</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover jdAreaGrid">
									<thead>
										<tr>
											<th>大洲名</th>
											<!--
											<th width="100">排序</th>
										    <th width="140">操作</th>
										    -->
										</tr>
									</thead>
									<tbody>
										<#if chainCode!="LW">
										<#list continents as continent>
											<tr class="small">
			                                    <td><a href="${basePath}/hot/continent?id=${continent.id?if_exists}">${continent.name?if_exists}</a></td>
			                                    <!--
			                                    <td style="text-align: center;">
			                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
			                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
			                                    </td>
			                                    <td style="text-align: center;">
			                                        <a class="btn btn-warning updateJDAreaBtn" href="javascript:void(0)">修改</a>
			                                        <a class="btn btn-danger deleteJDAreaBtn" href="javascript:void(0)">删除</a>
			                                    </td>
			                                    -->
											</tr>
										</#list>
										</#if>
										<#if chainCode=="LW">
										<tr class="small">
		                                    <td><a href="${basePath}/hot/continent?id=2">欧洲</a></td>
		                                    <!--
		                                    <td style="text-align: center;">
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning updateJDAreaBtn" href="javascript:void(0)">修改</a>
		                                        <a class="btn btn-danger deleteJDAreaBtn" href="javascript:void(0)">删除</a>
		                                    </td>
		                                    -->
										</tr>
										<tr class="small">
		                                    <td><a href="${basePath}/hot/continent?id=3">大洋洲</a></td>
		                                    <!--
		                                    <td style="text-align: center;">
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning updateJDAreaBtn" href="javascript:void(0)">修改</a>
		                                        <a class="btn btn-danger deleteHotBtn" href="javascript:void(0)">删除</a>
		                                    </td>
		                                    -->
										</tr>
										<tr class="small">
		                                    <td><a href="${basePath}/hot/continent?id=6">北美洲</a></td>
		                                    <!--
		                                    <td style="text-align: center;">
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning updateJDAreaBtn" href="javascript:void(0)">修改</a>
		                                        <a class="btn btn-danger deleteJDAreaBtn" href="javascript:void(0)">删除</a>
		                                    </td>
		                                    -->
										</tr>
										<tr class="small">
		                                    <td><a href="${basePath}/hot/continent?id=1">南美洲</a></td>
		                                    <!--
		                                    <td style="text-align: center;">
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning updateJDAreaBtn" href="javascript:void(0)">修改</a>
		                                        <a class="btn btn-danger deleteJDAreaBtn" href="javascript:void(0)">删除</a>
		                                    </td>
		                                    -->
										</tr>
										<tr class="small">
		                                    <td><a href="${basePath}/hot/continent?id=4">亚洲</a></td>
		                                    <!--
		                                    <td style="text-align: center;">
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning updateJDAreaBtn" href="javascript:void(0)">修改</a>
		                                        <a class="btn btn-danger deleteJDAreaBtn" href="javascript:void(0)">删除</a>
		                                    </td>
		                                    -->
										</tr>
										<tr class="small">
		                                    <td><a href="${basePath}/hot/continent?id=5">非洲</a></td>
		                                    <!--
		                                    <td style="text-align: center;">
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning updateJDAreaBtn" href="javascript:void(0)">修改</a>
		                                        <a class="btn btn-danger deleteJDAreaBtn" href="javascript:void(0)">删除</a>
		                                    </td>
		                                    -->
										</tr>
										</#if>
									</tbody>
								</table>
								<!--
								<div style="margin-top: 12px;">
									<input type="button" id="saveAreaOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序"/>
								</div>
								-->
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	$('#areaitems').summernote({height: 240});
	
	$('#addHotBtn').on('click', function() {
		window.location.href = '${basePath}/hot/hotel?type=2';
	});
	
	$('#addHotCityBtn').on('click', function() {
		window.location.href = '${basePath}/hot/city';
	});
	
	$('#addHotCountryBtn').on('click', function() {
		window.location.href = '${basePath}/hot/country';
	});
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var describer = $editForm.find('#describer').val();
		describer = $.trim(describer);
		if(describer == '') {
			alert('海外酒店概述不能为空');
			return false;
		}
		if(describer.length>500) {
			alert('海外酒店概述不能超过500个字');
			return false;
		}
		var url = $editForm.attr('action');
		var params = {};
		params.itemCode = $editForm.find('#itemCode').val();
		params.itemName = $editForm.find('#itemName').val();
		params.webTitle = $editForm.find('#webTitle').val();
		params.webDesc = $editForm.find('#webDesc').val();
		params.describer = describer;
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			}
		}, 'json');
		return false;
	});
	
	$('.updateBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/oversea/detail?id='+$obj.attr('data-id');
	});
	$('#saveOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('.banner input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/banner/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/edit';
			}
		}, 'json');
	});
	
	$('.deleteBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/banner/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/oversea/edit';
				}
			}, 'json');
		}
	});
	
	$('.updateHotHotelBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/hot/hotel?id='+$obj.attr('data-id');
	});
	$('.updateHotCityBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/hot/city?id='+$obj.attr('data-id');
	});
	$('.updateHotCountryBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/hot/country?id='+$obj.attr('data-id');
	});
	
	
	$('#saveHotOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('.hotHotel input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/hotel/hot/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/edit';
			}
		}, 'json');
	});
	
	$('.deleteHotBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hot/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/hot/edit';
				}
			}, 'json');
		}
	});
	
	var $overseaAreaModal = $('#overseaAreaModal');
	$('#addNewAreaBtn').on('click', function() {
		$overseaAreaModal.find('.modal-title').text('添加海外区域');
		$overseaAreaModal.find('input[name=id]').val('');
		$overseaAreaModal.find('input[name=name]').val('');
		$overseaAreaModal.modal();
	});
	$overseaAreaModal.find('#saveAreaBtn').on('click', function() {
		var $form = $overseaAreaModal.find('form');
		var url = $form.attr('action');
		var params = {};
		params.id = $.trim($form.find('input[name=id]').val());
		params.name = $.trim($form.find('input[name=name]').val());
		params.type = '2';
		//params.parentId = ;
		//params.nameEn = ;
		//params.areaCode = ;
		//params.cover = ;
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/edit';
			}
		}, 'json');
	});
	
	$('#saveAreaOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('.jdAreaGrid input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/hotel/area/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/edit';
			}
		}, 'json');
	});
	
	$('.updateJDAreaBtn').on('click', function() {
		var $obj = $(this);
		$overseaAreaModal.find('.modal-title').text('修改海外区域');
		$overseaAreaModal.find('input[name=id]').val($obj.attr('data-id'));
		$overseaAreaModal.find('input[name=name]').val($obj.attr('data-name'));
		$overseaAreaModal.modal();
	});
	
	$('.deleteJDAreaBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hotel/area/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/oversea/edit';
				}
			}, 'json');
		}
	});
	
	$('#saveSeaAreaOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('.seaAreaGrid input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/hotel/area/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/edit';
			}
		}, 'json');
	});
	
	$('.deleteSeaAreaBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hotel/area/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/oversea/edit';
				}
			}, 'json');
		}
	});
	$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
	
});
</script>
</body>
</html>