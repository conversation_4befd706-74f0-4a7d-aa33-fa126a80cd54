<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="about-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>${itemName}</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/about/save">
									<input type="hidden" id="itemCode" name="itemCode" value="${data.itemCode?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-2 control-label" for="itemName" style="font-size: 16px;">栏目名</label>
											<div class="col-md-10"><input class="form-control" type="text" id="itemName" name="itemName" value="${data.title?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webTitle" style="font-size: 16px;">网页Title</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webTitle" name="webTitle" value="${data.webtitle?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webDesc" style="font-size: 16px;">网页Description</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webDesc" name="webDesc" value="${data.webdesc?if_exists}"></div>
										</div>
										<#if itemCode == "about">
										<div class="form-group">
											<label class="col-md-2 control-label" for="itemBanner" style="font-size: 16px;">Banner</label>
											<div class="col-md-7">
												<input class="form-control" type="text" id="itemBanner" name="itemBanner" value="${data.banner?if_exists}" readonly="readonly" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectBannerBtn" class="btn btn-success" value="选取" />
											</div>
											<div class="col-md-2">
												<input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${data.banner?if_exists}" value="查看Banner" />
											</div>
										</div>
										</#if>
										<div class="form-group">
											<div class="col-md-12">
												<div id="itemContent">${data.content?if_exists}</div>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	var $selectBannerModal = $('#selectBannerModal');
	var selectBanner = function() {
		var relateType = 'about-banner';
		var params = {};
		params.relateType = relateType;
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val(relateType);
		$selectBannerModal.find('input[name=relateCode]').val($editForm.find('#itemCode').val());
		$selectBannerModal.find('input[name=format]').val('1600*350c.jpg');
		$selectBannerModal.find('#imgsize').text('1600 x 350');
		$selectBannerModal.modal();
	};
	var $itemBanner = $('#itemBanner');
	$('#selectBannerBtn').on('click', selectBanner);
	$itemBanner.on('click', selectBanner);
	
	var $itemContent = $('#itemContent');
	$itemContent.summernote({height: 480});
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var url = $editForm.attr('action');
		var params = {};
		params.itemCode = $editForm.find('#itemCode').val();
		params.itemName = $editForm.find('#itemName').val();
		params.banner = $editForm.find('#itemBanner').val();
		params.webTitle = $editForm.find('#webTitle').val();
		params.webDesc = $editForm.find('#webDesc').val();
		params.content = $itemContent.code();
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			}
		}, 'json');
		return false;
	});
});
function selectImage(url) {
	$('#itemBanner').val(url);
	$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};
</script>
</body>
</html>