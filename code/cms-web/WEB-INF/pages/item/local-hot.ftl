<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="local-hot-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2><#if data.id gte 1>修改<#else>添加</#if>热门国内酒店</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/hotel/hot/save">
									<input type="hidden" id="id" name="id" value="${data.id}" />
									<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
									<input type="hidden" id="type" name="type" value="1" />
									<input type="hidden" id="hotelCode" name="hotelCode" value="${data.hotelCode?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-1 control-label" for="hotel" style="font-size: 16px;">酒店</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="hotel" name="hotel" readonly="readonly" value="${data.hotelName?if_exists}" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectHotelBtn" class="btn btn-success" value="选取" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-1 control-label" for="cover" style="font-size: 16px;">封面</label>
											<div class="col-md-8">
												<input class="form-control" type="text" id="cover" name="cover" readonly="readonly" value="${data.cover?if_exists}" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectBannerBtn" class="btn btn-success" value="选取" />
											</div>
											<div class="col-md-2">
												<input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${data.cover?if_exists}" value="查看封面" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-1 control-label" for="describer" style="font-size: 16px;">描述</label>
											<div class="col-md-11">
												<textarea class="form-control" style="min-height: 120px;" id="describer" name="describer">${data.describer?if_exists}</textarea>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" style="margin-right: 12px;" />
												<input type="button" class="btn btn-default" value="返回" onclick="window.location.href='${basePath}/local/edit'" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	var $selectBannerModal = $('#selectBannerModal');
	var selectBanner = function() {
		var params = {};
		params.relateType = 'local-hot-banner';
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val('local-hot-banner');
		$selectBannerModal.find('input[name=relateCode]').val($editForm.find('#itemCode').val());
		$selectBannerModal.find('input[name=format]').val('294*204c.jpg');
		$selectBannerModal.find('#imgsize').text('294 x 204');
		$selectBannerModal.find('.modal-title').text('添加封面');
		$selectBannerModal.modal();
	};
	var $itemBanner = $('#cover');
	$('#selectBannerBtn').on('click', selectBanner);
	$itemBanner.on('click', selectBanner);
	
	var $selectHotelModal = $('#selectHotelModal');
	var $selectHotelBtn = $('#selectHotelBtn');
	var $hotel = $('#hotel');
	$hotel.on('click', function() {
		showHotelModel();
	});
	$selectHotelBtn.on('click', function() {
		showHotelModel();
	});
	function showHotelModel() {
		$selectHotelModal.find('iframe').attr('src', '${basePath}/hotel/select?state=CN');
		$selectHotelModal.modal();
	}
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var url = $editForm.attr('action');
		var hotelCode = $editForm.find('#hotelCode').val();
		hotelCode = $.trim(hotelCode);
		if(hotelCode == '') {
			alert('请选取酒店');
			return false;
		}
		var describer = $editForm.find('#describer').val();
		describer = $.trim(describer);
		if(describer.length>98) {
			alert('描述不能超过98个字');
			return false;
		}
		var params = $editForm.serializeArray();
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/local/edit';
			}
		}, 'json');
		return false;
	});
});
function selectHotelInfo(hotelCode, hotelName, hotelNameEn, countryCode, countryName, cityCode, cityName) {
	$('#hotelCode').val(hotelCode);
	var hotel = hotelName+'（'+hotelNameEn+'）【 '+cityName+'，'+countryName+'】';
	$('#hotel').val(hotel);
	$('#selectHotelModal').modal('hide');
}
function selectImage(url) {
	$('#cover').val(url);
	$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};
</script>
</body>
</html>