<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>大洲</li>
			<li>${continent.name?if_exists}</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>${continent.name?if_exists}</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table id="dt_basic" class="table table-striped table-bordered table-hover">
									<thead>
										<tr>
											<th>目的地</th>
											<!--
											<th width="100">排序</th>
										    -->
										    <th width="140">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list continent.hotCities as city>
											<tr class="small">
			                                    <td>${city.cityName?if_exists}</td>
			                                    <td style="text-align: center;">
			                                        <!-- <a class="btn btn-warning updateBtn" href="javascript:void(0)" data-id="${city.hotSiteId}" style="margin-right: 12px;">修改</a> -->
			                                        <a class="btn btn-danger deleteBtn" href="javascript:void(0)" data-id="${city.hotSiteId}">删除</a>
			                                    </td>
											</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addBtn" class="btn btn-primary" value="添加热门城市" />
									<input type="button" class="btn btn-default" value="返回" onclick="window.location.href='${basePath}/hot/edit'" />
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="hot-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>热门酒店</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover hotHotel">
									<thead>
										<tr>
											<th>酒店中文名</th>
											<th>酒店英文名</th>
											<!--<th width="100">排序</th>-->
										    <th width="250">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list continent.hotHotels as data>
										<tr class="small">
		                                    <td>${data.hotelName?if_exists}</td>
		                                    <td>${data.hotelNameEn?if_exists}</td>
		                                    <td style="text-align: center;">
												<#if chainCode=="LW">
		                                        <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0);">查看封面</a>
												</#if>
		                                        <a class="btn btn-warning updateHotHotelBtn" href="javascript:void(0)" data-id="${data.hotSiteId}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteHotHotelBtn" href="javascript:void(0)" data-id="${data.hotSiteId}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addHotHotelBtn" class="btn btn-primary" value="添加热门酒店" style="margin-right: 12px;"/>
									<!--
									<input type="button" id="saveHotOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序"/>
									-->
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	$('#addBtn').on('click', function() {
		window.location.href = '${basePath}/hot/continent/city?continentId=${continent.id}';
	});
	
	$('.updateBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/hot/continent/city?continentId=${continent.id}&hotId='+$obj.attr('data-id');
	});
	
	$('#addHotHotelBtn').on('click', function() {
		window.location.href = '${basePath}/hot/hotel?type=5&continentId=${continent.id}';
	});
	
	$('.updateHotHotelBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/hot/hotel?id='+$obj.attr('data-id');
	});
	
	$('.deleteBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hot/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/hot/continent?id=${continent.id}';
				}
			}, 'json');
		}
	});
	
	$('.deleteHotHotelBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hot/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/hot/continent?id=${continent.id}';
				}
			}, 'json');
		}
	});
});
</script>
</body>
</html>