<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="local-edit-11" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>Banner管理</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover banner">
									<thead>
										<tr>
											<th>类型</th>
											<th>名称</th>
											<th width="100">排序</th>
										    <th width="160">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list datas as data>
										<tr class="small">
		                                    <td><#if data.type="1">酒店<#else>促销活动</#if></td>
		                                    <td>${data.title}</td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" data-id="${data.id}" value="${data.orderNum}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>	
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <!-- <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0);">查看Banner</a> -->
		                                        <a class="btn btn-warning updateBtn" href="javascript:void(0)" data-id="${data.id}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteBtn" href="javascript:void(0)" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addBtn" class="btn btn-primary" value="添加 Banner" <#if (datas?size>=3)>disabled="disabled" </#if>/>
									<input type="button" id="saveOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序" style="margin: 0px 12px;" <#if (datas?size<=1)>disabled="disabled" </#if>/>
									<input type="button" id="previewBtn" class="btn btn-info" value="预览 Banner" <#if (datas?size<1)>disabled="disabled" </#if>/>
								</div>
							</div>
							<div class="alert alert-warning">
								<i class="fa-fw fa fa-warning"></i><strong>提示：</strong>
								<#if (datas?size<1)>
									请至少维护1幅Banner。
								<#elseif (datas?size>=3)>
									Banner已达到3幅，不能继续添加。
								<#else>
									Banner最多3幅，至少1幅，还可以添加${3-datas?size}幅。
								</#if>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="local-edit-21" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>中国.国内酒店</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/about/save">
									<input type="hidden" id="itemCode" name="itemCode" value="${itemData.itemCode?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-2 control-label" for="itemName" style="font-size: 16px;">栏目名</label>
											<div class="col-md-10"><input class="form-control" type="text" id="itemName" name="itemName" value="${itemData.title?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webTitle" style="font-size: 16px;">网页Title</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webTitle" name="webTitle" value="${itemData.webtitle?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webDesc" style="font-size: 16px;">网页Description</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webDesc" name="webDesc" value="${itemData.webdesc?if_exists}"></div>
										</div>
										<div class="form-group">
											<!-- <label class="col-md-1 control-label" for="describer" style="font-size: 16px;">描述</label> -->
											<div class="col-md-12">
												<textarea class="form-control" style="min-height: 120px;" id="describer" name="describer">${itemData.describer?if_exists}</textarea>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="local-edit-31" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>热门国内酒店</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover hotHotel">
									<thead>
										<tr>
											<th>酒店名</th>
											<th>酒店英文名</th>
											<th width="100">排序</th>
										    <th width="240">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list localseaDatas as data>
										<tr class="small">
		                                    <td>${data.hotelName?if_exists}</td>
		                                    <td>${data.hotelNameEn?if_exists}</td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" data-id="${data.id}" value="${data.orderNum}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0);">查看封面</a>
		                                        <a class="btn btn-warning updateHotBtn" href="javascript:void(0)" data-id="${data.id}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteHotBtn" href="javascript:void(0)" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addHotBtn" class="btn btn-primary" value="添加国内酒店" style="margin-right: 12px;" <#if (localseaDatas?size>=6)>disabled="disabled" </#if>/>
									<input type="button" id="saveHotOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序" <#if (localseaDatas?size<=1)>disabled="disabled" </#if>/>
								</div>
							</div>
							<div class="alert alert-warning">
								<i class="fa-fw fa fa-warning"></i><strong>提示：</strong>
								<#if (localseaDatas?size>=6)>
									热门国内酒店已满6家，无需添加。
								<#else>
									热门国内酒店不满6家，请继续添加。
								</#if>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	$('#addBtn').on('click', function() {
		window.location.href = '${basePath}/local/detail';
	});
	$('#addHotBtn').on('click', function() {
		window.location.href = '${basePath}/local/hot';
	});
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var url = $editForm.attr('action');
		var describer = $editForm.find('#describer').val();
		describer = $.trim(describer);
		if(describer.length > 500) {
			alert('文字不能超过500个字');
			return false;
		}
		var params = {};
		params.itemCode = $editForm.find('#itemCode').val();
		params.itemName = $editForm.find('#itemName').val();
		params.webTitle = $editForm.find('#webTitle').val();
		params.webDesc = $editForm.find('#webDesc').val();
		params.describer = describer;
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			}
		}, 'json');
		return false;
	});
	
	$('.updateBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/local/detail?id='+$obj.attr('data-id');
	});
	$('#saveOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('.banner input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/banner/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/local/edit';
			}
		}, 'json');
	});
	
	$('.deleteBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/banner/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/local/edit';
				}
			}, 'json');
		}
	});
	
	$('.updateHotBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/local/hot?id='+$obj.attr('data-id');
	});
	$('#saveHotOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('.hotHotel input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/hotel/hot/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/local/edit';
			}
		}, 'json');
	});
	
	$('.deleteHotBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hotel/hot/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/local/edit';
				}
			}, 'json');
		}
	});
	
	$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
	
	var $previewForm = $('#previewForm');
	$('#previewBtn').on('click', function() {
		var ids = '';
		$('.banner input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			ids += id+',';
		});
		var params = {};
		params.ids = ids;
		$.post('${basePath}/banner/preview', params, function(data) {
			if(data.code == '1') {
				window.open('${localPreviewUrl}'+data.desc);
			} else {
				alert(data.desc);
			}
		}, 'json');
	});
});
</script>
<form id="previewForm" method="post" action="" target="_blank" style="display: none;">
<input type="hidden" id="previewId" name="previewId" />
</form>
</body>
</html>