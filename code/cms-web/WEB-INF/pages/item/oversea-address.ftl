<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>更多精选海外目的地</li>
			<li>${data.name?if_exists}</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>${data.name?if_exists}</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table id="dt_basic" class="table table-striped table-bordered table-hover">
									<thead>
										<tr>
											<th>目的地</th>
											<th width="100">排序</th>
										    <th width="140">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list datas as datax>
										<tr class="small">
		                                    <td>${datax.name?if_exists}</td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" data-id="${datax.id}" value="${datax.orderNum}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning updateBtn" href="javascript:void(0)" data-id="${datax.id}" style="margin-right: 12px;">修改</a>
		                                        <a class="btn btn-danger deleteBtn" href="javascript:void(0)" data-id="${datax.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<#if data.id gt 0>
								<div style="margin-top: 12px;">
									<input type="button" id="addBtn" class="btn btn-primary" value="添加海外目的地" <#if (datas?size>=9)>disabled="disabled" </#if> />
									<input type="button" id="saveOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序" style="margin: 0px 12px;" <#if (datas?size<=1)>disabled="disabled" </#if> />
									<input type="button" class="btn btn-default" value="返回" onclick="window.location.href='${basePath}/oversea/edit'" />
								</div>
								</#if>
							</div>
							<div class="alert alert-warning">
								<i class="fa-fw fa fa-warning"></i><strong>提示：</strong>
								<#if (datas?size>=9)>
									已达到9个城市，不能继续添加。
								<#else>
									最多9个城市，还可以添加${9-datas?size}个城市。
								</#if>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	$('#addBtn').on('click', function() {
		window.location.href = '${basePath}/oversea/city?pid=${data.id}';
	});
	$('.updateBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/oversea/city?pid=${data.id}&id='+$obj.attr('data-id');
	});
	$('#saveOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			var value = orderIndex;
			items += id+':'+value+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/hotel/area/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/address?id=${data.id}';
			}
		}, 'json');
	});
	$('.deleteBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hotel/area/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/oversea/address?id=${data.id}';
				}
			}, 'json');
		}
	});
	$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
});
</script>
</body>
</html>