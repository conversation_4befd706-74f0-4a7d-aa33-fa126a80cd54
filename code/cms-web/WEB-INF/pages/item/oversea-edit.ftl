<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="oversea-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>Banner管理</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover banner">
									<thead>
										<tr>
											<th>类型</th>
											<th>名称</th>
											<th width="100">排序</th>
										    <th width="160">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list datas as data>
										<tr class="small">
		                                    <td><#if data.type="1">酒店<#else>促销活动</#if></td>
		                                    <td>${data.title?if_exists}</td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" data-id="${data.id}" value="${data.orderNum}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <!-- <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0);">查看Banner</a> -->
		                                        <a class="btn btn-warning updateBtn" href="javascript:void(0)" data-id="${data.id}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteBtn" href="javascript:void(0)" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addBtn" class="btn btn-primary" value="添加 Banner" <#if (datas?size>=3)>disabled="disabled" </#if>/>
									<input type="button" id="saveOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序" style="margin: 0px 12px;" <#if (datas?size<=1)>disabled="disabled" </#if> />
									<input type="button" id="previewBtn" class="btn btn-info" value="预览 Banner" <#if (datas?size<=0)>disabled="disabled" </#if>/>
								</div>
							</div>
							<div class="alert alert-warning">
								<i class="fa-fw fa fa-warning"></i><strong>提示：</strong>
								<#if (datas?size<1)>
									请至少维护1幅Banner。
								<#elseif (datas?size>=3)>
									Banner已达到3幅，不能继续添加。
								<#else>
									Banner最多3幅，至少1幅，还可以添加${3-datas?size}幅。
								</#if>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="oversea-edit-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>海外酒店概述</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/about/save">
									<input type="hidden" id="itemCode" name="itemCode" value="${itemData.itemCode?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-2 control-label" for="itemName" style="font-size: 16px;">栏目名</label>
											<div class="col-md-10"><input class="form-control" type="text" id="itemName" name="itemName" value="${itemData.title?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webTitle" style="font-size: 16px;">网页Title</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webTitle" name="webTitle" value="${itemData.webtitle?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webDesc" style="font-size: 16px;">网页Description</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webDesc" name="webDesc" value="${itemData.webdesc?if_exists}"></div>
										</div>
										<div class="form-group">
											<!-- 
											<label class="col-md-2 control-label" for="describer" style="font-size: 16px;">海外酒店概述</label>
											 -->
											<div class="col-md-12">
												<textarea class="form-control" style="min-height: 120px;" id="describer" name="describer">${itemData.describer?if_exists}</textarea>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="oversea-edit-3" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>热门海外酒店</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover hotHotel">
									<thead>
										<tr>
											<th>酒店名</th>
											<th>酒店英文名</th>
											<th width="100">排序</th>
										    <th width="250">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list overseaDatas as data>
										<tr class="small">
		                                    <td>${data.hotelName?if_exists}</td>
		                                    <td>${data.hotelNameEn?if_exists}</td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" data-id="${data.id}" value="${data.orderNum}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0);">查看封面</a>
		                                        <a class="btn btn-warning updateHotBtn" href="javascript:void(0)" data-id="${data.id}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteHotBtn" href="javascript:void(0)" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addHotBtn" class="btn btn-primary" value="添加海外酒店" style="margin-right: 12px;"  <#if (overseaDatas?size>=4)>disabled="disabled" </#if>/>
									<input type="button" id="saveHotOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序"  <#if (overseaDatas?size<=1)>disabled="disabled" </#if>/>
								</div>
							</div>
							<div class="alert alert-warning">
								<i class="fa-fw fa fa-warning"></i><strong>提示：</strong>
								<#if (overseaDatas?size>=4)>
									热门海外酒店已达到4项，不能继续添加。
								<#else>
									热门海外酒店最多4项，需添加${4-overseaDatas?size}项热门海外酒店。
								</#if>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="oversea-edit-4" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>热门海外目的地</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover seaAreaGrid">
									<thead>
										<tr>
											<th>目的地</th>
											<th>目的地英文名</th>
											<th width="100">排序</th>
										    <th width="250">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list hotDatas as data>
										<tr class="small">
		                                    <td>${data.name?if_exists}</td>
		                                    <td>${data.nameEn?if_exists}</td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" data-id="${data.id}" value="${data.orderNum}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0)">查看封面</a>
		                                        <a class="btn btn-warning updateSeaAreaBtn" href="javascript:void(0)" data-id="${data.id}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteSeaAreaBtn" href="javascript:void(0)" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addSeaAreaBtn" class="btn btn-primary" value="添加海外目的地" style="margin-right: 12px;" <#if (hotDatas?size>=4)>disabled="disabled" </#if> />
									<input type="button" id="saveSeaAreaOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序" <#if (hotDatas?size<=1)>disabled="disabled" </#if> />
								</div>
							</div>
							<div class="alert alert-warning">
								<i class="fa-fw fa fa-warning"></i><strong>提示：</strong>
								<#if (hotDatas?size>=4)>
									热门海外目的地已达到4项，不能继续添加。
								<#else>
									热门海外目的地最多4项，需添加${4-hotDatas?size}项热门海外目的地。
								</#if>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="oversea-edit-7" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>更多精选海外区域</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table class="table table-striped table-bordered table-hover jdAreaGrid">
									<thead>
										<tr>
											<th>区域名</th>
											<th width="100">排序</th>
										    <th width="140">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list areaDatas as data>
										<tr class="small">
		                                    <td><a href="${basePath}/oversea/address?id=${data.id}">${data.name?if_exists}</a></td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" data-id="${data.id}" value="${data.orderNum}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <a class="btn btn-warning updateJDAreaBtn" href="javascript:void(0)" data-id="${data.id}" data-name="${data.name?if_exists}">修改</a>
		                                        <a class="btn btn-danger deleteJDAreaBtn" href="javascript:void(0)" data-id="${data.id}" style="margin-left: 12px;">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addNewAreaBtn" class="btn btn-primary" value="添加海外区域" style="margin-right: 12px;" <#if (areaDatas?size>=4)>disabled="disabled" </#if> />
									<input type="button" id="saveAreaOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序" <#if (areaDatas?size<=1)>disabled="disabled" </#if> />
								</div>
							</div>
							<div class="alert alert-warning">
								<i class="fa-fw fa fa-warning"></i><strong>提示：</strong>
								<#if (areaDatas?size>=4)>
									海外区域已达到4项，不能继续添加。
								<#else>
									海外区域最多4项，需添加${4-areaDatas?size}项海外区域。
								</#if>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	$('#areaitems').summernote({height: 240});
	
	$('#addBtn').on('click', function() {
		window.location.href = '${basePath}/oversea/detail';
	});
	$('#addHotBtn').on('click', function() {
		window.location.href = '${basePath}/oversea/hot';
	});
	$('#addSeaAreaBtn').on('click', function() {
		window.location.href = '${basePath}/oversea/area';
	});
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var describer = $editForm.find('#describer').val();
		describer = $.trim(describer);
		if(describer == '') {
			alert('海外酒店概述不能为空');
			return false;
		}
		if(describer.length>500) {
			alert('海外酒店概述不能超过500个字');
			return false;
		}
		var url = $editForm.attr('action');
		var params = {};
		params.itemCode = $editForm.find('#itemCode').val();
		params.itemName = $editForm.find('#itemName').val();
		params.webTitle = $editForm.find('#webTitle').val();
		params.webDesc = $editForm.find('#webDesc').val();
		params.describer = describer;
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			}
		}, 'json');
		return false;
	});
	
	$('.updateBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/oversea/detail?id='+$obj.attr('data-id');
	});
	$('#saveOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('.banner input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/banner/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/edit';
			}
		}, 'json');
	});
	
	$('.deleteBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/banner/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/oversea/edit';
				}
			}, 'json');
		}
	});
	
	$('.updateHotBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/oversea/hot?id='+$obj.attr('data-id');
	});
	$('#saveHotOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('.hotHotel input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/hotel/hot/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/edit';
			}
		}, 'json');
	});
	
	$('.deleteHotBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hotel/hot/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/oversea/edit';
				}
			}, 'json');
		}
	});
	
	var $overseaAreaModal = $('#overseaAreaModal');
	$('#addNewAreaBtn').on('click', function() {
		$overseaAreaModal.find('.modal-title').text('添加海外区域');
		$overseaAreaModal.find('input[name=id]').val('');
		$overseaAreaModal.find('input[name=name]').val('');
		$overseaAreaModal.modal();
	});
	$overseaAreaModal.find('#saveAreaBtn').on('click', function() {
		var $form = $overseaAreaModal.find('form');
		var url = $form.attr('action');
		var params = {};
		params.id = $.trim($form.find('input[name=id]').val());
		params.name = $.trim($form.find('input[name=name]').val());
		params.type = '2';
		//params.parentId = ;
		//params.nameEn = ;
		//params.areaCode = ;
		//params.cover = ;
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/edit';
			}
		}, 'json');
	});
	
	$('#saveAreaOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('.jdAreaGrid input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/hotel/area/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/edit';
			}
		}, 'json');
	});
	
	$('.updateJDAreaBtn').on('click', function() {
		var $obj = $(this);
		$overseaAreaModal.find('.modal-title').text('修改海外区域');
		$overseaAreaModal.find('input[name=id]').val($obj.attr('data-id'));
		$overseaAreaModal.find('input[name=name]').val($obj.attr('data-name'));
		$overseaAreaModal.modal();
	});
	
	$('.deleteJDAreaBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hotel/area/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/oversea/edit';
				}
			}, 'json');
		}
	});
	
	$('.updateSeaAreaBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/oversea/area?id='+$obj.attr('data-id');
	});
	$('#saveSeaAreaOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('.seaAreaGrid input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/hotel/area/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/oversea/edit';
			}
		}, 'json');
	});
	
	$('.deleteSeaAreaBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/hotel/area/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/oversea/edit';
				}
			}, 'json');
		}
	});
	$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
	
	var $previewForm = $('#previewForm');
	$('#previewBtn').on('click', function() {
		var ids = '';
		$('.banner input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			ids += id+',';
		});
		var params = {};
		params.ids = ids;
		$.post('${basePath}/banner/preview', params, function(data) {
			if(data.code == '1') {
				window.open('${overseaPreviewUrl}'+data.desc);
			} else {
				alert(data.desc);
			}
		}, 'json');
	});
});
</script>
<form id="previewForm" method="post" action="" target="_blank" style="display: none;">
<input type="hidden" id="previewId" name="previewId" />
</form>
</body>
</html>