<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="travel-item-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2><#if im.id??>修改<#else>添加</#if>璀璨之旅旅程</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/travel/itemAdd">
									<input type="hidden" id="id" name="id" value="${im.id?if_exists}" />
									<input type="hidden" id="journeyId" name="journeyId" value="${im.journeyId?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-1 control-label" for="title" style="font-size: 16px;">标题</label>
											<div class="col-md-11">
												<input class="form-control" type="text" id="title" name="title" value="${im.name?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-1 control-label" for="cover" style="font-size: 16px;">插图</label>
											<div class="col-md-8">
												<input class="form-control" type="text" id="cover" name="cover" readonly="readonly" value="${im.cover?if_exists}"/>
											</div>
											<div class="col-md-1">
												<input type="button" id="selectCoverBtn" class="btn btn-success" value="选取" />
											</div>
											<div class="col-md-2">
												<input type="button" id="itmeBanner" class="btn btn-info viewBanner" data-img="${imageUrl}/${im.cover?if_exists}" value="查看插图" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-1 control-label" for="describer" style="font-size: 16px;">描述</label>
											<div class="col-md-11">
												<textarea class="form-control" style="min-height: 120px;" id="describer" name="describer">${im.describer?if_exists}</textarea>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" style="margin-right: 12px;" />
												<input type="button" class="btn btn-default" value="返回" onclick="window.location.href='${basePath}/travel/detail?id=${im.journeyId?if_exists}'" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="travel-item-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>璀璨之旅旅程推荐酒店设置</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table id="dt_basic" class="table table-striped table-bordered table-hover overseaGrid">
									<thead>
										<tr>
											<th>酒店名</th>
											<th>排序</th>
										    <th>操作</th>
										</tr>
									</thead>
									<tbody>
										<#list hotels?if_exists as hotel>
										<tr class="small">
											<td style="display:none">${hotel.id?if_exists}</td>
		                                    <td>${hotel.hotelName?if_exists}</td>
		                                    <td style="display:none">${hotel.cover?if_exists}</td>
		                                    <td width="100" style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" class="form-control" style="width: 84px;" data-id="${hotel.id}" value="${hotel.orderNum?if_exists}" data-img="${imageUrl}/${hotel.cover?if_exists}" data-img-path="${hotel.cover?if_exists}"/>
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td width="240" style="text-align: center;">
		                                        <a class="btn btn-info viewBanner" data-img="${imageUrl}/${hotel.cover}">查看封面</a>
		                                        <a class="btn btn-warning" href="" ids="up" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger" href="" ids="del">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addBtn" class="btn btn-primary" value="新增推荐酒店" style="margin-right: 12px;" />
									<input type="button" class="btn bg-color-pink txt-color-white" value="保存排序" id="saveOrderBtn"/>
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<div class="modal fade" id="checkCoverModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content" style="width: 840px;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">确认封面</h4>
			</div>
			<div class="modal-body" style="padding-bottom: 8px;">
				<form id="bannerUploadForm" class="form-horizontal" method="post" action="${basePath}/image/upload" enctype="multipart/form-data" target="uploadframe">
					<input type="hidden" name="relateType" />
					<input type="hidden" name="relateCode" />
					<fieldset>
						<div class="form-group">
							<div class="col-sm-12" style="text-align: center;"><img class="confirmCover" style="max-width: 480px;" /></div>
						</div>
						<div class="form-group">
							<div class="col-md-11">
								<input class="form-control" type="file" id="file" name="file" />
							</div>
							<div class="col-md-1">
								<input type="submit" class="btn btn-success" value="上传" />
							</div>
						</div>
						<div class="form-group">
							<div class="superbox col-sm-12"></div>
						</div>
					</fieldset>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" id="confirmBtn" class="btn btn-primary" style="margin-right: 12px;">确认</button>
				<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
			</div>
		</div>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	$('#addBtn').on('click', function() {
		var jyid = '${im.id?if_exists}';
		if(jyid == '') {
			alert('请先保存旅程信息');
			return;
		}
		window.location.href = "${basePath}/travel/hotel?journeyItemId="+jyid;
	});
	
	var $selectBannerModal = $('#selectBannerModal');
	var selectBanner = function() {
		var params = {};
		var type ='travel-item-cover';
		params.relateType = type;
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val(type);
		$selectBannerModal.find('input[name=relateCode]').val($('#itemCode').val());
		$selectBannerModal.find('input[name=format]').val('384*216c.jpg');
		$selectBannerModal.find('#imgsize').text('384 x 216');
		$selectBannerModal.find('.modal-title').text('添加插图');
		$selectBannerModal.modal();
	};
	$selectBannerModal.find('.superbox img').on('click', function() {
		var $obj = $(this);
		$itemBanner.val('${imageUrl}/'+$obj.attr('src'));
		$selectBannerModal.modal('hide');
	});
	var $itemBanner = $('#cover');
	$('#selectCoverBtn').on('click', selectBanner);
	$itemBanner.on('click', selectBanner);
	
	//$('#itemAdd').click(addItem);
	$("a[ids='del']").click(delHotel);
	$("a[ids='up']").click(upHotel);
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var url = $editForm.attr('action');
		var params = $editForm.serializeArray();
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location="${basePath}/travel/detail?id=${im.journeyId?if_exists}";
			}
		}, 'json');
		return false;
	});
	
});
 function saveHotelOrder() {
		var items = '';
		var orderIndex = 1;
		$('input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			//var value = $obj.val();
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.orders = items;
		$.post('${basePath}/travel/orderHotel', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/travel/item?id='+"${im.id?if_exists}";
			}
		}, 'json');
	}
	var $checkCoverModal = $('#checkCoverModal');
	$('#saveOrderBtn').on('click', function() {
		/**	
		var $firstOrder = $('.overseaGrid input[type=hidden][name=ordernum]:first');
		if($firstOrder.val() != '1') {
			var params = {};
			params.relateType = 'travel-hotel-banner';
			$.post('${basePath}/image/list.json', params, function(datas) {
				$checkCoverModal.find('.superbox').html(convertImageHtml(datas));
			}, 'json');
			$checkCoverModal.find('.confirmCover').attr('src', $firstOrder.attr('data-img')).attr('data-img-path', $firstOrder.attr('data-img-path'));;
			$checkCoverModal.find('input[name=relateCode]').val($firstOrder.attr('data-id'));
			$('#checkCoverModal').find('input[name=relateType]').val('travel-hotel-banner');
			$checkCoverModal.modal();
		} else {
			saveHotelOrder();
		}
		**/
		
		var jyid = '${im.id?if_exists}';
		if(jyid == '') {
			alert('请先保存旅程信息');
			return;
		}
		saveHotelOrder();
	});
$checkCoverModal.find('#confirmBtn').on('click', function() {
	var _cover = $checkCoverModal.find('.confirmCover').attr('data-img-path');
	var _code = $checkCoverModal.find('input[name=relateCode]').val();
	var _url = '${basePath}/travel/hotelcover/update';
	var _params = {};
	_params.cover = _cover;
	_params.id = _code;
	$.post(_url, _params, function(data) {
		if(data.code==1){
			saveHotelOrder();
		}else{
			alert("修改失败");
		}
		
	}, 'json');
});

function selectImage(url) {
	$('#cover').val(url);
	$('#itmeBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};
function upHotel(){
	var pa = $(this).parent().parent();
	var url =  "${basePath}/travel/hotel?"+"id="+pa.children("td:eq(0)").text();
	window.location =url;
	return false;
}
function delHotel(){
	if(!confirm("是否删除？")){
		return false;
	}
	var pa = $(this).parent().parent();
	var id = pa.children("td:eq(0)").text();
	$.ajax({
			async : false,
			type : "POST",
			dataType:"json",
			data:{
				id:id
			},
			url :  "${basePath}/travel/delHotel",
			error : function(data) {
				return "执行失败";
			},
			success : function(data) {
				if(data.code==1){
					alert(data.desc);
					$(pa).remove();
					return false;
				}else{
					alert(data.desc);
					return false;
				}
			}
		});
		return false;
}
/**
function addItem(){
	$('#editForm').validate({
					 submitHandler:function(){
					 	$.ajax({
							async : false,
							type : "POST",
							dataType:"json",
							data:{
								name:$('#title').val(),
								cover:$('#cover').val(),
								describer:$('#describer').val(),
								journeyId:"${im.journeyId?if_exists}",
								id:$('#id').val()
							},
							url :  "${basePath}/travel/itemAdd",
							error : function(data) {
								return "执行失败";
							},
							success : function(data) {
								if(data.code==1){
									alert(data.desc);
									window.location="${basePath}/travel/detail?id=${im.journeyId?if_exists}";
								}else{
									alert(data.desc);
								}
							}
						});
					 },
					rules: {
						title: {
							required: true
						},
					},
					messages: {
						title: {
							required: '请输入标题'
						},
					}
				});
}
**/

$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
</script>
</body>
</html>