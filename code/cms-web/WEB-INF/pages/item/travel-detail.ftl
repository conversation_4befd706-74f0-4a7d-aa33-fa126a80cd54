<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row" id="addtravalrow" >
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="travel-detail-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2><#if jy.id??>修改<#else>添加</#if>璀璨之旅</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/travel/save" >
									<fieldset>
										<div class="form-group">
											<label class="col-md-2 control-label" for="title" style="font-size: 16px;">标题</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="title" name="title" value="${jy.title?if_exists}"/>
												<input class="form-control" style="display:none;" type="text" id="trid" name="title" value="${jy.id?if_exists}"/>
											</div>
										</div>
										<div class="form-group" style="display:none">
											<label class="col-md-2 control-label" for="subtitle" style="font-size: 16px;">小标题</label>
											<div class="col-md-10">
												<input class="form-control" type="text" id="subtitle" name="subtitle" value="${jy.subtitle?if_exists}" />
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="banner" style="font-size: 16px;">头图</label>
											<div class="col-md-7">
												<input class="form-control" type="text" id="banner" name="banner" readonly="readonly" value="${jy.banner?if_exists}" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectBannerBtn" class="btn btn-success" value="选取"/>
											</div>
											<div class="col-md-2">
												<input type="button" class="btn btn-info viewBanner" id="itmeBanner" value="查看头图" data-img="${imageUrl}/${jy.banner?if_exists}"/>
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="cover" style="font-size: 16px;">封面</label>
											<div class="col-md-7">
												<input class="form-control" type="text" id="cover" name="cover" readonly="readonly"  value="${jy.cover?if_exists}"/>
											</div>
											<div class="col-md-1">
												<input type="button" id="selectCoverBtn" class="btn btn-success" value="选取" />
											</div>
											<div class="col-md-2">
												<input type="button" class="btn btn-info viewBanner" id="itmeCover" value="查看封面" data-img="${imageUrl}/${jy.cover?if_exists}"/>
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="describer" style="font-size: 16px;">描述</label>
											<div class="col-md-10">
												<textarea class="form-control" style="min-height: 120px;" id="describer" name="describer" >${jy.describer?if_exists}</textarea>
											</div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="isPrimary" style="font-size: 16px;">主推位</label>
											<div class="col-md-10">
												<label class="radio-inline">
													<input type="radio" id="isPrimaryNo" name="isPrimary" value="0" <#if jy.isPrimary=="0">checked="checked"</#if> />
													<span>否</span>
												</label>
												<label class="radio-inline">
													<input type="radio" id="isPrimaryYes" name="isPrimary" value="1" <#if jy.isPrimary=="1">checked="checked"</#if> />
													<span>是</span>
												</label>
											</div>
										</div>
										<div class="form-group" id="pbannerLayer" <#if jy.isPrimary=="0">style="display: none;"</#if>>
											<label class="col-md-2 control-label" for="pbanner" style="font-size: 16px;">主推Banner</label>
											<div class="col-md-7">
												<input class="form-control" type="text" id="pbanner" name="pbanner" value="${jy.pbanner?if_exists}" readonly="readonly" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectPannerBtn" class="btn btn-success" data-img="${imageUrl}/${jy.pbanner?if_exists}" value="选取" />
											</div>
											<div class="col-md-2">
												<input type="button" class="btn btn-info viewBanner" id="itmePanner" value="查看Banner" />
											</div>
										</div>
										<div class="form-group" id="remarkLayer" <#if jy.isPrimary=="0">style="display: none;"</#if>>
											<label class="col-md-2 control-label" for="remark" style="font-size: 16px;">主推描述</label>
											<div class="col-md-10">
												<textarea class="form-control" style="min-height: 120px;" id="remark" name="remark">${jy.remark?if_exists}</textarea>
											</div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" id="travelbut" class="btn btn-primary" value="保存" style="margin-right: 12px;" />
												<input type="button" class="btn btn-default" value="返回" onclick="window.location.href='${basePath}/travel/edit'" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="travel-detail-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>璀璨之旅旅程</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table id="dt_basic" class="table table-striped table-bordered table-hover overseaGrid">
									<thead>
										<tr>
											<th>标题</th>
											<th>排序</th>
										    <th>操作</th>
										</tr>
									</thead>
									<tbody>
										<#list items?if_exists as it>
										<tr class="small">
		                                    <td>${it.name}</td>
		                                    <td width="100" style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" class="form-control" style="width: 84px;" data-id="${it.id}" value="${it.orderNum?if_exists}" data-img="${imageUrl}/${it.cover?if_exists}" data-img-path="${it.cover?if_exists}"/>
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a></td>
		                                    <td style="display:none">${it.id}</td>
		                                    <td style="display:none">${it.journeyId}</td>
		                                    <td style="display:none">${it.cover}</td>
		                                    <td style="display:none">${it.describer}</td>
		                                    <td width="240" style="text-align: center;">
		                                        <a class="btn btn-info viewBanner" data-img="${imageUrl}/${it.cover}">查看插图</a>
		                                        <a class="btn btn-warning" href="" ids="up" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger" href="" ids="del">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addBtn" class="btn btn-primary" value="新增璀璨之旅旅程" style="margin-right: 12px;" />
									<input type="button" class="btn bg-color-pink txt-color-white" value="保存排序" id="saveOrderBtn"/>
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<div class="modal fade" id="checkCoverModal" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content" style="width: 840px;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">确认封面</h4>
			</div>
			<div class="modal-body" style="padding-bottom: 8px;">
				<form id="bannerUploadForm" class="form-horizontal" method="post" action="${basePath}/image/upload" enctype="multipart/form-data" target="uploadframe">
					<input type="hidden" name="relateCode" />
					<input type="hidden" name="relateType" />
					<fieldset>
						<div class="form-group">
							<div class="col-sm-12" style="text-align: center;"><img class="confirmCover" style="max-width: 480px;" /></div>
						</div>
						<div class="form-group">
							<div class="col-md-11">
								<input class="form-control" type="file" id="file" name="file" />
							</div>
							<div class="col-md-1">
								<input type="submit" class="btn btn-success" value="上传" />
							</div>
						</div>
						<div class="form-group">
							<div class="superbox col-sm-12"></div>
						</div>
					</fieldset>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" id="confirmBtn" class="btn btn-primary" style="margin-right: 12px;">确认</button>
				<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
			</div>
		</div>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>

<script type="text/javascript">
var flag;
var bc;
$(document).ready(function() {
	$('input[name=isPrimary]').on('change', function() {
		var $obj = $(this);
		if($obj.val() == '1') {
			$('#pbannerLayer').show();
			$('#remarkLayer').show();
		} else {
			$('#pbannerLayer').hide();
			$('#remarkLayer').hide();
		}
	});
	
	$("a[ids='del']").click(deleteIt);
	$("a[ids='up']").click(updateIt);
	$('#addBtn').on('click', function() {
		var jyid = '${jy.id?if_exists}';
		if(jyid == '') {
			alert('请先保存璀璨之旅信息');
			return;
		}
		window.location.href = '${basePath}/travel/item?journeyId='+jyid;
	});
	
	var $editForm = $('#editForm');
	var $selectBannerModal = $('#selectBannerModal');
	var selectBanner = function() {
		var params = {};
		var type = 'travel-cover';
		if(bc == 1) {
			type = 'travel-picture';
		} else if(bc == 2) {
			type = 'travel-banner';
		}
		params.relateType = type;
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val(type);
		$selectBannerModal.find('input[name=relateCode]').val($('#itemCode').val());
		if(bc == 1) {
			$selectBannerModal.find('input[name=format]').val('1600*558c.jpg');
			$selectBannerModal.find('#imgsize').text('1600 x 558');
			$selectBannerModal.find('.modal-title').text('添加头图');
		} else if(bc == 2) {
			$selectBannerModal.find('input[name=format]').val('1600*558c.jpg');
			$selectBannerModal.find('#imgsize').text('1600 x 558');
			$selectBannerModal.find('.modal-title').text('添加Banner');
		} else {
			$selectBannerModal.find('input[name=format]').val('295*204c.jpg');
			$selectBannerModal.find('#imgsize').text('295 x 204');
			$selectBannerModal.find('.modal-title').text('添加封面');
		}
		$selectBannerModal.modal();
	};
	$selectBannerModal.find('.superbox img').on('click', function() {
		var $obj = $(this);
		$itemBanner.val('${imageUrl}/'+$obj.attr('src'));
		$selectBannerModal.modal('hide');
	});
	var selectCo = function(){
		bc = 0;
		selectBanner();
	};
	var selectBa = function(){
		bc = 1;
		selectBanner();
	};
	var selectPrimary = function(){
		bc = 2;
		selectBanner();
	};
	$('#cover').click(selectCo);
	$('#selectCoverBtn').click(selectCo);
	$('#selectBannerBtn').click(selectBa);
	$('#banner').click(selectBa);
	$('#selectPannerBtn').click(selectPrimary);
	$('#pbanner').click(selectPrimary);
	
	$("#travelbut").click(addTravel);
	
});
var $checkCoverModal = $('#checkCoverModal');
$('#saveOrderBtn').on('click', function() {
		var jyid = '${jy.id?if_exists}';
		if(jyid == '') {
			alert('请先保存璀璨之旅信息');
			return;
		}
		saveTravelItemOrder();
	});
	

$checkCoverModal.find('#confirmBtn').on('click', function() {
	var _cover = $checkCoverModal.find('.confirmCover').attr('data-img-path');
	var _code = $checkCoverModal.find('input[name=relateCode]').val();
	var _url = '${basePath}/travel/itemcover/update';
	var _params = {};
	_params.cover = _cover;
	_params.id = _code;
	$.post(_url, _params, function(data) {
		if(data.code==1){
			saveTravelItemOrder();
		}else{
			alert("修改失败");
		}
		
	}, 'json');
});

function selectImage(url) {
	if(bc == 1){
		$('#banner').val(url);
		$('#itmeBanner').attr('data-img', '${imageUrl}/'+url);
		bc =0;
	} else if(bc == 2) {
		$('#pbanner').val(url);
		$('#itmePanner').attr('data-img', '${imageUrl}/'+url);
		bc =0;
	} else {
		$('#cover').val(url);
		$('#itmeCover').attr('data-img', '${imageUrl}/'+url);
	}
	$('#selectBannerModal').modal('hide');
};

function updateIt(){
	var pa = $(this).parent().parent();
	var url =  "${basePath}/travel/item?"+"id="+pa.children("td:eq(2)").text();
	window.location =url;
	return false;
}
function deleteIt(){
	if(!confirm("是否删除？")){
		return false;
	}
	var pa = $(this).parent().parent();
	var id = pa.children("td:eq(2)").text();
	$.ajax({
			async : false,
			type : "POST",
			dataType:"json",
			data:{
				id:id
			},
			url :  "${basePath}/travel/deleteItem",
			error : function(data) {
				return "执行失败";
			},
			success : function(data) {
				if(data.code==1){
					alert(data.desc);
					$(pa).remove();
					return false;
				}else{
					alert(data.desc);
					return false;
				}
			}
		});
		return false;
}


function addTravel(){
	var title = $('#title').val();
	title = $.trim(title);
	if(title == '') {
		alert('请输入旅程标题');
		return;
	}
	if(title.length>100) {
		alert('旅程标题的长度不能超过100个字');
	}
	var subtitle = $('#subtitle').val();
	var banner = $('#banner').val();
	if(banner == '') {
		alert('请上传旅程封头图');
		return;
	}
	var cover = $('#cover').val();
	if(cover == '') {
		alert('请上传旅程封面图');
		return;
	}
	var describer = $('#describer').val();
	describer = $.trim(describer);
	if(describer.length>500) {
		alert('描述不能超过500个字');
		return;
	}
	
	var isPrimary = $('input[name=isPrimary]:checked').val();
	var remark = $('#remark').val();
	var pbanner = $('#pbanner').val();
	if(isPrimary=='1' && pbanner == '') {
		alert('请上传主推Banner');
		return;
	}
	remark = $.trim(remark);
	if(isPrimary=='1' && remark.length>100) {
		alert('主推Banner描述不能超过100个字');
		return false;
	}
	$('#editForm').validate({
					 submitHandler:function(){
					 	$.ajax({
							async : false,
							type : "POST",
							dataType:"json",
							data:{
								id:$('#trid').val(),
								title:title,
								subtitle:subtitle,
								banner:banner,
								cover:cover,
								describer:describer,
								isPrimary:isPrimary,
								remark:remark,
								pbanner:pbanner
							},
							url :  "${basePath}/travel/save",
							error : function(data) {
								return "执行失败";
							},
							success : function(data) {
								if(data.code==1){
									alert(data.desc);
									window.location="${basePath}/travel/edit";
								}else{
									alert(data.desc);
								}
							}
						});
					 },
					rules: {
					},
					messages: {
					}
				});
	
}




function saveTravelItemOrder() {
		var items = '';
		var orderIndex = 1;
		$('input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			//var value = $obj.val();
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.orders = items;
		$.post('${basePath}/travel/orderItem', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/travel/detail?id='+"${jy.id?if_exists}";
			}
		}, 'json');
	}
$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
</script>
</body>
</html>