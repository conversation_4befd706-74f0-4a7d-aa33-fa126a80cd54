<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>选择璀璨之旅Banner</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="travel-edit-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>选择主推Banner</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table id="dt_basic" class="table table-striped table-bordered table-hover overseaGrid">
									<thead>
										<tr>
											<th>&nbsp;</th>
											<th>标题</th>
											<th>是否主推</th>
										</tr>
									</thead>
									<tbody>
									<#list journeys as jy>
										<tr class="small">
		                                    <td><input type="radio" name="jid" data-describer="${jy.remark?if_exists}" data-banner="${jy.pbanner?if_exists}" data-banner-url="${imageUrl}/${jy.pbanner?if_exists}" value="${jy.id?if_exists}" <#if jy.isPrimary?if_exists="1"> checked="checked"</#if> /></td>
		                                    <td>${jy.title?if_exists}</td>
		                                    <td><#if jy.isPrimary?if_exists="1">是<#else>否</#if></td>
										</tr>
										</#list>
									</tbody>
								</table>
								<form class="form-horizontal" style="margin-top: 12px;">
									<div class="form-group">
										<label class="col-md-1 control-label" for="pbanner" style="font-size: 16px;">Banner</label>
										<div class="col-md-8">
											<input class="form-control" type="text" id="pbanner" name="pbanner" readonly="readonly" />
										</div>
										<div class="col-md-1">
											<input type="button" id="selectBannerBtn" class="btn btn-success" value="选取" />
										</div>
										<div class="col-md-2">
											<input type="button" class="btn btn-info viewBanner" id="pBannerView" value="查看Banner" />
										</div>
									</div>
									<div class="form-group">
										<div class="col-md-12">
											<textarea class="form-control" style="min-height: 120px;" id="describer" name="describer"></textarea>
										</div>
									</div>
								</form>
								<div style="margin-top: 12px;">
									<input type="button" id="saveBtn" class="btn btn-primary" value="保存" style="margin-right: 12px;" />
									<a class="btn btn-info" href="${basePath}/travel/detail?primary=1">添加主推Banner</a>
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	var $selectBannerModal = $('#selectBannerModal');
	var selectBanner = function() {
		var relateType = 'travel-banner';
		var params = {};
		params.relateType = relateType;
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val(relateType);
		$selectBannerModal.find('input[name=relateCode]').val($('input[name=jid]:checked').val());
		$selectBannerModal.find('input[name=format]').val('1600*558c.jpg');
		$selectBannerModal.find('#imgsize').text('1600 x 558');
		$selectBannerModal.modal();
	};
	var $itemBanner = $('#pbanner');
	$('#selectBannerBtn').on('click', selectBanner);
	$itemBanner.on('click', selectBanner);
	
	$('input[name=jid]').on('change', renderDescriber);
	function renderDescriber() {
		var $cjid = $('input[name=jid]:checked');
		$('#describer').val($cjid.attr('data-describer'));
		$('#pbanner').val($cjid.attr('data-banner'));
		$('#pBannerView').attr('data-img', $cjid.attr('data-banner-url'));
	}
	renderDescriber();
	$('#saveBtn').on('click', function() {
		var id = $('input[name=jid]:checked').val();
		var pbanner = $('#pbanner').val();
		if(pbanner == '') {
			alert('请上传Banner');
			return false;
		}
		var remark = $('#describer').val();
		remark = $.trim(remark);
		if(remark.length>100) {
			alert('Banner描述不能超过100个字');
			return false;
		}
		var params = {};
		params.id = id;
		params.pbanner = pbanner;
		params.remark = remark;
		$.post('${basePath}/travel/banner/change', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/travel/edit';
			}
		}, 'json');
		return false;
	});
});
function selectImage(url) {
	$('#pbanner').val(url);
	$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};
</script>
</body>
</html>