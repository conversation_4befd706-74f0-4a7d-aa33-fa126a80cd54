<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="home-edit-2" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>${itemName}</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/about/save">
									<input type="hidden" id="itemCode" name="itemCode" value="${itemData.itemCode?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-2 control-label" for="itemName" style="font-size: 16px;">栏目名</label>
											<div class="col-md-10"><input class="form-control" type="text" id="itemName" name="itemName" value="${itemData.title?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webTitle" style="font-size: 16px;">网页Title</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webTitle" name="webTitle" value="${itemData.webtitle?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="webDesc" style="font-size: 16px;">网页Description</label>
											<div class="col-md-10"><input class="form-control" type="text" id="webDesc" name="webDesc" value="${itemData.webdesc?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="mblTitle" style="font-size: 16px;">手机Title</label>
											<div class="col-md-10"><input class="form-control" type="text" id="mblTitle" name="mblTitle" value="${itemData.mbltitle?if_exists}"></div>
										</div>
										<div class="form-group">
											<label class="col-md-2 control-label" for="mblDesc" style="font-size: 16px;">手机Description</label>
											<div class="col-md-10"><input class="form-control" type="text" id="mblDesc" name="mblDesc" value="${itemData.mbldesc?if_exists}"></div>
										</div>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="home-edit-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>首页Banner设置</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<table id="dt_basic" class="table table-striped table-bordered table-hover">
									<thead>
										<tr>
											<th>类型</th>
											<th>名称</th>
											<th width="100">排序</th>
										    <th width="160">操作</th>
										</tr>
									</thead>
									<tbody>
										<#list datas as data>
										<tr class="small">
		                                    <td><#if data.type=="1">酒店</#if><#if data.type=="2">促销活动</#if><#if data.type=="link">链接</#if></td>
		                                    <td>${data.title?if_exists} ${data.title1?if_exists}</td>
		                                    <td style="text-align: center;">
		                                    	<input type="hidden" name="ordernum" class="form-control" style="width: 84px;" data-id="${data.id}" value="${data.orderNum}" />
		                                    	<a class="baseOrder ascOrder btn bg-color-redLight txt-color-white btn-sm" href="javascript:void(0);" style="margin-right: 12px;"><i class="fa fa-arrow-up"></i></a>
		                                    	<a class="baseOrder descOrder btn bg-color-greenDark txt-color-white btn-sm" href="javascript:void(0);"><i class="fa fa-arrow-down"></i></a>
		                                    </td>
		                                    <td style="text-align: center;">
		                                        <!-- <a class="btn btn-success viewBanner" data-img="${imageUrl}/${data.cover}" href="javascript:void(0);">查看Banner</a> -->
		                                        <a class="btn btn-warning updateBtn" href="javascript:void(0)" data-id="${data.id}" style="margin: 0px 12px;">修改</a>
		                                        <a class="btn btn-danger deleteBtn" href="javascript:void(0)" data-id="${data.id}">删除</a>
		                                    </td>
										</tr>
										</#list>
									</tbody>
								</table>
								<div style="margin-top: 12px;">
									<input type="button" id="addBtn" class="btn btn-primary" value="添加 Banner" <#if (datas?size>=5)>disabled="disabled" </#if>/>
									<input type="button" id="saveOrderBtn" class="btn bg-color-pink txt-color-white" value="保存排序" style="margin: 0px 12px;" <#if (datas?size<=1)>disabled="disabled" </#if> />
									<input type="button" id="previewBtn" class="btn btn-info" value="预览 Banner" <#if (datas?size<3)>disabled="disabled" </#if>/>
								</div>
							</div>
							<div class="alert alert-warning">
								<i class="fa-fw fa fa-warning"></i><strong>提示：</strong>
								<#if (datas?size<3)>
									请至少维护3幅Banner。
								<#elseif (datas?size>=5)>
									Banner已达到5幅，不能继续添加。
								<#else>
									Banner至少3幅，最多5幅，还可以添加${5-datas?size}幅。
								</#if>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	$('#addBtn').on('click', function() {
		window.location.href = '${basePath}/home/<USER>';
	});
	$('.updateBtn').on('click', function() {
		var $obj = $(this);
		window.location.href = '${basePath}/home/<USER>'+$obj.attr('data-id');
	});
	
	$('#saveOrderBtn').on('click', function() {
		var items = '';
		var orderIndex = 1;
		$('input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			//var value = $obj.val();
			items += id+':'+orderIndex+';';
			orderIndex ++;
		});
		var params = {};
		params.items = items;
		$.post('${basePath}/banner/order/update', params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/home/<USER>';
			}
		}, 'json');
	});
	
	$('.deleteBtn').on('click', function() {
		if(confirm('确定要删除吗？')) {
			var $obj = $(this);
			var params = {};
			params.id = $obj.attr('data-id');
			$.post('${basePath}/banner/delete', params, function(data) {
				alert(data.desc);
				if(data.code == '100') {
					window.location.href = '${basePath}/login';
				} else if(data.code == '1') {
					window.location.href = '${basePath}/home/<USER>';
				}
			}, 'json');
		}
	});
	
	//$('.baseOrder.ascOrder:first').hide();
	//$('.baseOrder.descOrder:last').hide();
	$('.baseOrder').on('click', function() {
		var $obj = $(this);
		if($obj.hasClass('ascOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.prev();
			if($tobj.length) {
				$tobj.insertAfter($pobj);
			} else {
				alert('已经排在第一行了');
			}
		} else if($obj.hasClass('descOrder')) {
			var $pobj = $obj.parent().parent();
			var $tobj = $pobj.next();
			if($tobj.length>0) {
				$tobj.insertBefore($pobj);
			} else {
				alert('已经排在最后一行了');
			}
		}
	});
	
	var $previewForm = $('#previewForm');
	$('#previewBtn').on('click', function() {
		var ids = '';
		$('input[type=hidden][name=ordernum]').each(function() {
			var $obj = $(this);
			var id = $obj.attr('data-id');
			ids += id+',';
		});
		var params = {};
		params.ids = ids;
		$.post('${basePath}/banner/preview', params, function(data) {
			if(data.code == '1') {
				window.open('${homePreviewUrl}'+data.desc);
			} else {
				alert(data.desc);
			}
		}, 'json');
	});
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var url = $editForm.attr('action');
		var params = {};
		params.itemCode = $editForm.find('#itemCode').val();
		params.itemName = $editForm.find('#itemName').val();
		params.webTitle = $editForm.find('#webTitle').val();
		params.webDesc = $editForm.find('#webDesc').val();
		params.mblTitle = $editForm.find('#mblTitle').val();
		params.mblDesc = $editForm.find('#mblDesc').val();
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			}
		}, 'json');
		return false;
	});
});
</script>
</body>
</html>