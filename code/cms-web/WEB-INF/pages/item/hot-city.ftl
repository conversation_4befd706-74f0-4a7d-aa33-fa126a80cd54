<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>${itemName}</li>
		</ol>
	</div>
	<div id="content">
		<input type="hidden" id="itemCode" name="itemCode" value="${itemCode}" />
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="oversea-area-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2><#if siteHot.id gte 1>修改<#else>添加</#if>热门城市</h2>
						</header>
						<div>
							<div class="jarviswidget-editbox"></div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal" method="post" action="${basePath}/hot/city/save">
									<input type="hidden" id="id" name="id" value="${siteHot.id?if_exists}" />
									<input type="hidden" id="type" name="type" value="0" />
									<input type="hidden" id="name" name="name" value="${data.cityName?if_exists}" />
									<input type="hidden" id="nameEn" name="nameEn" value="${data.cityNameEn?if_exists}" />
									<fieldset>
										<div class="form-group">
											<label class="col-md-1 control-label" for="hotel" style="font-size: 16px;">城市</label>
											<div class="col-md-11">
												<select class="form-control" id="code" name="code">
									       			<option value="">请选择城市</option>
									       			<#list countries as datax>
									       				<#list datax.cities as datar>
									       					<option value="${datar.cityCode}" data-nameen="${datar.cityNameEn}" data-name="${datar.cityName}"<#if datar.cityCode==data.cityCode?if_exists> selected="selected"</#if>>${datax.countryName} - ${datar.cityName}</option>
									       				</#list>
									       			</#list>
									       		</select>
											</div>
										</div>
										<#if chainCode=="LW">
										<div class="form-group">
											<label class="col-md-1 control-label" for="cover" style="font-size: 16px;">封面</label>
											<div class="col-md-8">
												<input class="form-control" type="text" id="cover" name="cover" readonly="readonly" value="${siteHot.cover?if_exists}" />
											</div>
											<div class="col-md-1">
												<input type="button" id="selectBannerBtn" class="btn btn-success" value="选取" />
											</div>
											<div class="col-md-2">
												<input type="button" class="btn btn-info viewBanner" data-img="${imageUrl}/${data.cover?if_exists}" value="查看封面" />
											</div>
										</div>
										</#if>
									</fieldset>
									<div class="form-actions">
										<div class="row">
											<div class="col-md-12">
												<input type="submit" class="btn btn-primary" value="保存" style="margin-right: 12px;" />
												<input type="button" class="btn btn-default" value="返回" onclick="window.location.href='${basePath}/hot/edit'" />
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<#include "/common/utils.ftl" />
<@foot.foot basePath="${basePath}"></@foot.foot>
<script type="text/javascript">
$(document).ready(function() {
	var $selectBannerModal = $('#selectBannerModal');
	var selectBanner = function() {
		var params = {};
		params.relateType = 'mobile-hot-city-banner';
		$.post('${basePath}/image/list.json', params, function(datas) {
			$selectBannerModal.find('.superbox').html(convertImageHtml(datas));
		}, 'json');
		$selectBannerModal.find('input[name=relateType]').val('mobile-hot-city-banner');
		$selectBannerModal.find('input[name=relateCode]').val($editForm.find('#id').val());
		$selectBannerModal.find('input[name=format]').val('250*250c.jpg');
		$selectBannerModal.find('#imgsize').text('250 x 250');
		$selectBannerModal.find('.modal-title').text('添加封面');
		$selectBannerModal.modal();
	};
	var $itemBanner = $('#cover');
	$('#selectBannerBtn').on('click', selectBanner);
	$itemBanner.on('click', selectBanner);
	
	var $areaCode = $('#areaCode');
	$areaCode.on('change', function() {
		var $opt = $areaCode.find('option:selected');
		$editForm.find('#name').val($opt.attr('data-name'));
		$editForm.find('#nameEn').val($opt.attr('data-nameen'));
	});
	
	var $editForm = $('#editForm');
	$editForm.on('submit', function() {
		var url = $editForm.attr('action');
		var params = $editForm.serializeArray();
		$.post(url, params, function(data) {
			alert(data.desc);
			if(data.code == '100') {
				window.location.href = '${basePath}/login';
			} else if(data.code == '1') {
				window.location.href = '${basePath}/hot/edit';
			}
		}, 'json');
		return false;
	});
});
function selectImage(url) {
	$('#cover').val(url);
	$('.viewBanner').attr('data-img', '${imageUrl}/'+url);
	$('#selectBannerModal').modal('hide');
};
</script>
</body>
</html>