<#include "/common/common.ftl" />
<!DOCTYPE html>
<html lang="zh-cn">
<link rel="stylesheet" href="${basePath}/static/mobiscroll/css/mobiscroll.icons.css" type="text/css" />
<link rel="stylesheet" href="${basePath}/static/mobiscroll/css/mobiscroll.scroller.css" type="text/css" />
<link rel="stylesheet" href="${basePath}/static/mobiscroll/css/mobiscroll.scroller.android-holo.css" type="text/css" />
<@head.head title="${title}" basePath="${basePath}">
</@head.head>
<body>
<#include "/common/header.ftl" />
<#include "/common/menu.ftl" />
<div id="main" role="main">
	<div id="ribbon">
		<ol class="breadcrumb">
			<li>订单查询</li>
		</ol>
	</div>
	<div id="content">
		<section id="widget-grid" class="">
			<div class="row">
				<article class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="jarviswidget jarviswidget-color-darken" id="images-photo-1" data-widget-editbutton="false" data-widget-sortable="false" data-widget-colorbutton="false" data-widget-fullscreenbutton="false" data-widget-deletebutton="false">
						<header>
							<span class="widget-icon">
								<i class="fa fa-table"></i>
							</span>
							<h2>订单查询</h2>
						</header>
						<div>
							<div class="widget-body">
								<form id="editForm" class="form-horizontal">
								</form>
								<div class="widget-body no-padding">
									<div class="widget-body-toolbar">
										<form id="queryForm" class="form-inline" method="post" action="${basePath}/reservation/search">
											<input type="hidden" id="research" name="research" value="true" /> 
											<input type="hidden" id="start" name="start" value="${page.currentPage}" />
											<input type="hidden" id="limit" name="limit" value="${limit}" />
										<div class="form-group">
											<label for="lastName" class="col-md-4 control-label">客户姓:</label>
											<div class="col-md-4">
											<input type="text" class="form-control" id="lastName" name="lastName" value="${lastName?if_exists}" placeholder="" />
											</div>
										</div>
										<div class="form-group">
											<label for="lastName" class="col-md-4 control-label">客户名:</label>
											<div class="col-sm-4">
												<input type="text" class="form-control" id="firsName" name="firsName" value="${firsName?if_exists}" placeholder="" />
											</div>
										</div>
										<div class="form-group">
											<label for="lastName" class="col-md-4 control-label"> 订单号:</label>
											<div class="col-sm-8">
												<input type="text" class="form-control" id="confirmNo" name="confirmNo" value="${confirmNo?if_exists}" placeholder="" />
											</div>
										</div>
										<div class="form-group" style="margin-top: 10px;">
											<label for="status" class="col-md-6 control-label">订单状态:</label>
											<div class="col-md-4">
												<select class="form-control" id="status" name="status" >
													<option value="" <#if status?if_exists=="">selected</#if>>全部</option>
													<option value="Committed" <#if status?if_exists=="Committed">selected</#if>>已确认</option>
													<option value="Modified" <#if status?if_exists=="Modified">selected</#if>>已修改</option>
													<option value="Cancelled" <#if status?if_exists=="Cancelled">selected</#if>>已取消</option>
												</select>
											</div>
										</div>
										<br/>
										<div class="form-group" style="margin-top: 10px;">
											<label for="createStartTime" class="col-md-3 control-label">创建时间 起:</label>
											<div class="col-md-4">
												<input type="text" class="form-control" id="createStartTime"
													name="createStartTime" required="true" value="${createStartTime?if_exists }" placeholder=""/> 
											</div>
											<div class="col-md-1">至:
											</div>
											<div class="col-md-4">
												<input type="text" class="form-control" id="createEndTime"
													name="createEndTime" required="true" value="${createEndTime?if_exists }" placeholder=""/>
											</div>
										</div>
										<div class="form-group" style="margin-top: 10px;">
											<label for="checkinStartTime" class="col-md-3 control-label">入住时间 起:</label>
											<div class="col-md-4">
												<input type="text" class="form-control" id="checkinStartTime"
													name="checkinStartTime" required="true" value="${checkinStartTime?if_exists }" placeholder=""/>
											</div>
											<div class="col-md-1">至:
											</div>
											<div class="col-md-4">
												<input type="text" class="form-control" id="checkinEndTime"
													name="checkinEndTime" required="true" value="${checkinEndTime?if_exists }" placeholder=""/>
											</div>
										</div>
										<br/>
									<div class="form-group" style="margin: 0px 15px 8px 0px;">
										<input type="submit" class="btn btn-primary" value="查询" nclick="javascript:saveHidden(true);" style="margin-right: 8px;" />
										<a class="btn btn-success" href="javascript:exportReservationExcel()" style="margin-right: 8px;">导出Excel</a>
									</div>
									</form>
									</div>
									<table id="dt_basic" class="table table-striped table-bordered table-hover">
										<thead>
											<tr>
												<th width="20"><input type="checkbox" id="hids" /></th>
												<th width="100">酒店名称</th>
												<th width="20">姓</th>
												<th width="40">名</th>
												<th width="80">订单号</th>
												<th width="40">入住日期</th>
												<th width="80">预订日期</th>
												<th width="40">状态</th>
												<!-- <th width="100">操作</th> -->
											</tr>
										</thead>
										<tbody>
											<#list page.datas as data>
											<tr class="">
												<td>
													<input type="checkbox" name="id" value="${data.id?if_exists}" />
												</td>
												<td>
													<div style="float:left;" >
														${(data.hotel.hotelName)?if_exists}(${(data.hotel.hotelNameEn)?if_exists}/${data.hotelCode?if_exists})
													</div>
												</td>
												<td>
													<div style="float:left;" >
														${data.lastName?if_exists}
													</div>
												</td>
												<td>
													<div style="float:left;" >
														${data.firstName?if_exists}
													</div>
												</td>
												<td>
													<div style="float:left;" >
														${data.confirmation?if_exists}
													</div>
												</td>
												<td>
													<div style="float:left;">
														${data.indate?string('yyyy-MM-dd')}
													</div>
												</td>
												<td>
													<label>${data.createTime?string('yyyy-MM-dd HH:mm:ss')}</label>
												</td>
												<td>
													<label><#if data.status?if_exists=="Committed">已确认</#if><#if data.status?if_exists=="Modified">已修改</#if><#if data.status?if_exists=="Cancelled">已取消</#if></label>
												</td>
												<%-- <td style="text-align: center;">
													<a style="float: right;" href="${data.sourceUrl?if_exists}" target="_blank">订单详情</a> 
												</td> --%>
											</tr>
											</#list>
										</tbody>
									</table>
									<#include "/common/page.ftl" />
								</div>
							</div>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</div>
<@foot.foot basePath="${basePath}"></@foot.foot>
<script src="${basePath}/static/mobiscroll/js/mobiscroll.core.js"></script>
<script src="${basePath}/static/mobiscroll/js/mobiscroll.scroller.js" ></script>
<script src="${basePath}/static/mobiscroll/js/mobiscroll.datetime.js" ></script>
<script src="${basePath}/static/mobiscroll/js/i18n/mobiscroll.i18n.zh.js" ></script>
<script src="${basePath}/static/mobiscroll/js/mobiscroll.scroller.android-holo.js" ></script>
<script type="text/javascript">
var curr = new Date().getFullYear();
var opt = {
		'date' : {
			preset : 'date',
		// invalid: { daysOfWeek: [0, 6], daysOfMonth: ['5/1', '12/24', '12/25'] }
		},
		'datetime' : {
			preset : 'datetime',
			minDate : new Date(1912, 3, 10, 9, 22),
			maxDate : new Date(2114, 7, 30, 15, 44),
			stepMinute : 5
		},
		'time' : {
			preset : 'time'
		},
		'credit' : {
			preset : 'date',
			dateOrder : 'mmyy',
			dateFormat : 'mm/yy',
			startYear : curr,
			endYear : curr + 10,
			width : 100
		},
		'select' : {
			preset : 'select'
		},
		'select-opt' : {
			preset : 'select',
			group : true,
			width : 50
		}
	};

$('#createStartTime').scroller('destroy').scroller($.extend(opt['datetime'], {
	theme : "android-holo light",
	mode : "scroller",
	lang : "zh",
	display : "modal",
	dateFormat: 'yyyy-mm-dd',
	animate : "",
}));
$('#createEndTime').scroller('destroy').scroller($.extend(opt['datetime'], {
	theme : "android-holo light",
	mode : "scroller",
	lang : "zh",
	display : "modal",
	dateFormat: 'yyyy-mm-dd',
	animate : "",
}));
$('#checkinStartTime').scroller('destroy').scroller($.extend(opt['date'], {
	theme : "android-holo light",
	mode : "scroller",
	lang : "zh",
	display : "modal",
	dateFormat: 'yyyy-mm-dd',
	animate : "",
}));
$('#checkinEndTime').scroller('destroy').scroller($.extend(opt['date'], {
	theme : "android-holo light",
	mode : "scroller",
	lang : "zh",
	display : "modal",
	dateFormat: 'yyyy-mm-dd',
	animate : "",
}));

function exportReservationExcel() {
	//如果页面中没有用于下载iframe，增加iframe到页面中
	if ($('#downloadcsv').length <= 0) {
		$('body')
				.append(
						"<iframe id=\"downloadcsv\" style=\"display:none\"></iframe>");
	}
	var url = "${basePath}/reservation/Reservation.xls";
	$('#downloadcsv').attr('src', '');
	$('#downloadcsv').attr('src', url);
}

function toPage(pageNo) {
	$('#start').val(pageNo);
	var temp = $('#queryForm');
	$('#queryForm').submit();
}

</script>
</body>
</html>